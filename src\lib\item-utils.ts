// Item management utilities for battle pass rewards
import { createClient } from "../../supabase/client";
export interface Item {
  id: string;
  name: string;
  type: 'avatar' | 'avatar_border' | 'background' | 'title';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  value: string;
  unlock_requirement: string | null;
  is_default: boolean;
  description: string | null;
  created_at: string;
  image_url: string;
  is_animated: boolean;
}

export interface ItemReward {
  type: 'item';
  id: string;
  item: Item; // Populated item data
  item_id?: string;
}

export interface XPReward {
  type: 'xp';
  amount: number;
  item?: Item;
}

export interface CurrencyReward {
  type: 'currency';
  amount: number;
  item?: Item;
}

export type Reward = ItemReward | XPReward | CurrencyReward;

const supabase = createClient();

// Get all items
export async function getAllItems(): Promise<Item[]> {
  const { data, error } = await supabase
    .from('player_items')
    .select('*')
    .order('type', { ascending: true })
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching items:', error);
    return [];
  }

  return data || [];
}

// Get items by type
export async function getItemsByType(type: Item['type']): Promise<Item[]> {
  const { data, error } = await supabase
    .from('player_items')
    .select('*')
    .eq('type', type)
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching items by type:', error);
    return [];
  }

  return data || [];
}

// Get item by id
export async function getItemByItemId(itemId: string): Promise<Item | null> {
  const { data, error } = await supabase
    .from('player_items')
    .select('*')
    .eq('id', itemId)
    .single();

  if (error) {
    console.error('Error fetching item:', error);
    return null;
  }

  return data;
}

// Get multiple items by their item_ids
export async function getItemsByItemIds(itemIds: string[]): Promise<Item[]> {

  if (itemIds.length === 0) return [];
  const { data, error } = await supabase
    .from('player_items')
    .select('*')
    .in('id', itemIds);

  if (error) {
    console.error('Error fetching items by IDs:', error);
    return [];
  }

  return data || [];
}

// Create new item
export async function createItem(item: Omit<Item, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> {
  const { error } = await supabase
    .from('player_items')
    .insert(item);

  if (error) {
    console.error('Error creating item:', error);
    return false;
  }

  return true;
}

// Update item
export async function updateItem(id: string, updates: Partial<Item>): Promise<boolean> {
  const { error } = await supabase
    .from('player_items')
    .update(updates)
    .eq('id', id);

  if (error) {
    console.error('Error updating item:', error);
    return false;
  }

  return true;
}

// Delete item
export async function deleteItem(id: string): Promise<boolean> {
  const { error } = await supabase
    .from('player_items')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting item:', error);
    return false;
  }

  return true;
}

// Get rarity color for UI
export function getRarityColor(rarity: Item['rarity']): string {
  switch (rarity) {
    case 'common': return 'text-gray-600 bg-gray-100';
    case 'rare': return 'text-blue-600 bg-blue-100';
    case 'epic': return 'text-purple-600 bg-purple-100';
    case 'legendary': return 'text-amber-600 bg-amber-100';
    default: return 'text-gray-600 bg-gray-100';
  }
}

// Get type icon for UI
export function getTypeIcon(type: Item['type']): string {
  switch (type) {
    case 'avatar': return '👤';
    case 'avatar_border': return '🖼️';
    case 'background': return '🖼️';
    case 'title': return '🏷️';
    default: return '🎁';
  }
}

// Format reward text for display
export function formatRewardText(reward: Reward): string {
  if (!reward) return 'No reward';
  
  switch (reward.type) {
    case 'xp': return `${reward.amount} XP`;
    case 'currency': return `${reward.amount} Coins`;
    case 'item': return reward.item ? reward.item.name : 'Cosmetic Item';
    default: return 'Unknown reward';
  }
}

// Populate item data for rewards
export async function populateItemRewards(rewards: (Reward | undefined)[]): Promise<Reward[]> {
  const itemIds: string[] = [];

  // Collect all item IDs
  rewards.forEach(reward => {
    if (reward?.type === 'item' && reward.item_id) {
      itemIds.push(reward.item_id);
    }
  });

  // Fetch all items at once
  const items = await getItemsByItemIds(itemIds);
  const itemMap = new Map<string, Item>();
  items.forEach(item => itemMap.set(item.id, item));
  // Populate rewards with item data
  return rewards.map(reward => {
    if (!reward) return null;
    
    if (reward.type === 'item' && reward.item_id) {
      const item = itemMap.get(reward.item_id);
      return {
        ...reward,
        item
      };
    }
    
    return reward;
  }).filter(Boolean) as Reward[];
}
