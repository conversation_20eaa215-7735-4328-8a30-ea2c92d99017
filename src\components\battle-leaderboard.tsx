"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { 
  Crown, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Target, 
  Zap,
  Eye,
  Heart,
  Flame,
  Trophy,
  BarChart3,
  Users,
  Award
} from "lucide-react";
import StyledAvatar from "./styled-avatar";
import PlayerStatsPopup from "./player-stats-popup";
import { Player, GameStateType } from "@/interfaces/interfaces";
import { formatTime } from "@/utils/waiting-utils";
import { getRankInfo, formatRP } from "@/utils/ranking-system";
import RankDisplay from "./rank-display";

interface BattleLeaderboardProps {
  players: Player[];
  currentUserId: string;
  gameState: GameStateType;
  currentRound: number;
  timeLeft: number;
  roomName: string;
  className?: string;
}

interface LeaderboardStats {
  activePlayers: number;
  spectators: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalPlayers: number;
  highestScore: number;
  averageScore: number;
  eliminationRate: number;
  avgAccuracy: number;
}

export default function BattleLeaderboard({
  players,
  currentUserId,
  gameState,
  currentRound,
  timeLeft,
  roomName,
  className
}: BattleLeaderboardProps) {
  const [selectedView, setSelectedView] = useState<'compact' | 'detailed' | 'statistics'>('compact');
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  // Calculate comprehensive statistics
  const stats = useMemo((): LeaderboardStats => {
    const activePlayers = players.filter(p => (p.lives || 0) > 0 && !p.is_spectator);
    const spectators = players.filter(p => (p.lives || 0) <= 0 || p.is_spectator);
    const correctAnswers = players.filter(p => p.lastAnswer === "correct").length;
    const incorrectAnswers = players.filter(p => p.lastAnswer === "incorrect").length;
    const scores = players.map(p => p.score || 0);
    const totalAnswers = players.reduce((sum, p) => sum + (p.total_answers || 0), 0);
    const totalCorrect = players.reduce((sum, p) => sum + (p.correct_answers || 0), 0);

    return {
      activePlayers: activePlayers.length,
      spectators: spectators.length,
      correctAnswers,
      incorrectAnswers,
      totalPlayers: players.length,
      highestScore: Math.max(...scores, 0),
      averageScore: scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0,
      eliminationRate: players.length > 0 ? Math.round((spectators.length / players.length) * 100) : 0,
      avgAccuracy: totalAnswers > 0 ? Math.round((totalCorrect / totalAnswers) * 100) : 0
    };
  }, [players]);

  // Sort players by rank
  const sortedPlayers = useMemo(() => {
    return [...players].sort((a, b) => {
      // Active players first, then by score
      const aActive = (a.lives || 0) > 0 && !a.is_spectator;
      const bActive = (b.lives || 0) > 0 && !b.is_spectator;
      
      if (aActive && !bActive) return -1;
      if (!aActive && bActive) return 1;
      
      return (b.score || 0) - (a.score || 0);
    });
  }, [players]);

  const getRankIcon = (index: number) => {
    if (index === 0) return <Crown className="w-4 h-4 text-yellow-500" fill="currentColor" />;
    if (index === 1) return <Crown className="w-4 h-4 text-gray-400" fill="currentColor" />;
    if (index === 2) return <Crown className="w-4 h-4 text-amber-600" fill="currentColor" />;
    return <span className="text-sm font-bold text-gray-600">#{index + 1}</span>;
  };
  const getPerformanceColor = (player: Player) => {
    const wpm = calculateWPM(player);
    
    if (wpm >= 40) return "text-green-600";
    if (wpm >= 25) return "text-yellow-600";
    return "text-red-600";
  };
  
  const calculateWPM = (player: Player): number => {
    const totalAnswers = player.total_answers || 0;
    const responseTimes = player.responseTimes || [];

    if (totalAnswers === 0) return 0;

    if (responseTimes.length > 0) {
      const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
      if (averageResponseTime <= 0) return 0;

      // Each response = one word; WPM = 60 / avg time per word
      return Math.round(60 / averageResponseTime);
    }

    // Fallback estimation
    const estimatedTimePerWord = 6; // reasonable default for spelling
    const totalTimeMinutes = (totalAnswers * estimatedTimePerWord) / 60;

    if (totalTimeMinutes <= 0) return 0;

    return Math.round(totalAnswers / totalTimeMinutes);
  };

  // Get estimated word length based on difficulty
  const getDifficultyWordLength = (difficulty: string): number => {
    switch (difficulty) {
      case 'easy': return 5;
      case 'medium': return 7;
      case 'hard': return 9;
      case 'extreme': return 11;
      default: return 6;
    }
  };

  const getStreakColor = (streak: number) => {
    if (streak >= 25) return "text-yellow-500";
    if (streak >= 20) return "text-yellow-400";
    if (streak >= 15) return "text-orange-500";
    if (streak >= 10) return "text-red-500";
    if (streak >= 5) return "text-gray-600";
    return "text-gray-400";
  };

  const getStreakBgColor = (streak: number) => {
    if (streak >= 10) return "bg-yellow-100";
    if (streak >= 7) return "bg-yellow-50";
    if (streak >= 5) return "bg-orange-100";
    if (streak >= 3) return "bg-red-100";
    if (streak >= 1) return "bg-gray-100";
    return "bg-gray-50";
  };

  const currentPlayer = players.find(p => p.id === currentUserId);
  const isSpectator = (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

  const StatisticsOverview = () => (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-blue-600" />
          Match Statistics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <Users className="w-3 h-3" />
                Active Players
              </span>
              <span className="font-semibold text-green-600">{stats.activePlayers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <Eye className="w-3 h-3" />
                Spectators
              </span>
              <span className="font-semibold text-gray-600">{stats.spectators}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <Trophy className="w-3 h-3" />
                Highest Score
              </span>
              <span className="font-semibold text-amber-600">{stats.highestScore}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <TrendingUp className="w-3 h-3" />
                Average Score
              </span>
              <span className="font-semibold text-blue-600">{stats.averageScore}</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm text-gray-600">Elimination Rate</span>
                <span className="font-semibold text-red-600">{stats.eliminationRate}%</span>
              </div>
              <Progress value={stats.eliminationRate} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm text-gray-600">Avg Accuracy</span>
                <span className="font-semibold text-green-600">{stats.avgAccuracy}%</span>
              </div>
              <Progress value={stats.avgAccuracy} className="h-2" />
            </div>
            <div className="pt-2 border-t">
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex justify-between">
                  <span>Difficulty:</span>
                  <span className="capitalize font-medium">{roomName}</span>
                </div>
                <div className="flex justify-between">
                  <span>Round:</span>
                  <span className="font-medium">#{currentRound}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const DetailedPlayersDialog = () => (
    <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-amber-600" />
            Complete Match Leaderboard
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-3">          <div className="grid grid-cols-5 gap-2 text-sm font-medium text-gray-600 border-b pb-2">
            <span>Rank</span>
            <span className="col-span-2">Player</span>
            <span className="text-center">WPM</span>
            <span className="text-right">Score</span>
          </div>
          
          {sortedPlayers.map((player, index) => (
            <div
              key={player.id}
              className={cn(
                "grid grid-cols-5 gap-2 items-center p-3 rounded-lg border",
                player.id === currentUserId && "bg-amber-50 border-amber-200",
                ((player.lives || 0) <= 0 || player.is_spectator) && "opacity-60 bg-gray-50"
              )}
            >
              <div className="flex items-center gap-2">
                {getRankIcon(index)}
              </div>
              
              <div className="col-span-2">
                <PlayerStatsPopup
                  playerId={player.id}
                  playerName={player.display_name || "Anonymous"}
                  playerAvatar={player.avatar_url}
                  playerBorder={player.avatar_border}
                >
                  <div className="flex items-center gap-2 cursor-pointer hover:bg-white/50 rounded p-1 transition-colors">
                    <div className="relative">
                      <StyledAvatar
                        src={player.avatar_url}
                        alt={player.display_name || ""}
                        fallback={player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                        size="sm"
                        border={player.avatar_border}
                      />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{player.display_name}</div>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(player.lives || 0, 3) }).map((_, i) => (
                          <Heart key={i} size={8} className="text-red-500" fill="#ef4444" />
                        ))}
                        {(player.lives || 0) > 3 && (
                          <span className="text-xs text-red-600">+{(player.lives || 0) - 3}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </PlayerStatsPopup>
              </div>
              
              <div className="text-center">
                <div className="flex flex-col items-center gap-1">                  {(player.total_answers || 0) > 0 && (
                    <span className={cn("text-sm font-medium", getPerformanceColor(player))}>
                      {calculateWPM(player)} WPM
                    </span>
                  )}
                  {(player.current_streak || 0) >= 3 && (
                    <div className={cn(
                      "flex items-center gap-0.5 px-1 py-0.5 rounded-full text-xs",
                      getStreakBgColor(player.current_streak || 0)
                    )}>
                      <Flame size={8} className={getStreakColor(player.current_streak || 0)} />
                      <span className={cn("font-bold", getStreakColor(player.current_streak || 0))}>
                        {player.current_streak}
                      </span>
                    </div>
                  )}
                  {gameState === "spelling" && (
                    <span className={cn(
                      "text-xs px-1.5 py-0.5 rounded-full",
                      player.has_answered ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                    )}>
                      {player.has_answered ? "Answered" : "Thinking..."}
                    </span>
                  )}
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-bold text-lg text-amber-800">{player.score || 0}</div>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className={cn("space-y-4", className)}>
      {/* View Toggle Buttons */}
      <div className="flex gap-2 mb-4">
        <Button
          variant={selectedView === 'compact' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedView('compact')}
          className="flex items-center gap-1"
        >
          <Trophy className="w-3 h-3" />
          Rankings
        </Button>
        <Button
          variant={selectedView === 'statistics' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedView('statistics')}
          className="flex items-center gap-1"
        >
          <BarChart3 className="w-3 h-3" />
          Stats
        </Button>
      </div>

      {/* Current View */}
      {selectedView === 'compact' && (
        <Card className="w-full">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Trophy className="w-5 h-5 text-amber-600" />
                Live Rankings
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {stats.activePlayers} Active
                </Badge>
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  Round {currentRound}
                </Badge>
              </div>
            </div>
            {gameState === "spelling" && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{formatTime(timeLeft)} remaining</span>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-2">
            {sortedPlayers.slice(0, 6).map((player, index) => (
              <div
                key={player.id}
                className={cn(
                  "flex items-center gap-3 p-3 rounded-lg border transition-all",
                  player.id === currentUserId && "bg-amber-50 border-amber-200",
                  ((player.lives || 0) <= 0 || player.is_spectator) && "opacity-60",
                  index < 3 && "bg-gradient-to-r from-yellow-50 to-amber-50"
                )}
              >
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  <div className="flex-shrink-0 w-6 flex items-center justify-center">
                    {getRankIcon(index)}
                  </div>
                  
                  <PlayerStatsPopup
                    playerId={player.id}
                    playerName={player.display_name || "Anonymous"}
                    playerAvatar={player.avatar_url}
                    playerBorder={player.avatar_border}
                  >
                    <div className="flex items-center gap-2 cursor-pointer hover:bg-white/50 rounded p-1 transition-colors">
                      <div className="relative">
                        <StyledAvatar
                          src={player.avatar_url}
                          alt={player.display_name || ""}
                          fallback={player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                          size="sm"
                          border={player.avatar_border}
                        />
                        {(player.current_streak || 0) >= 3 && (
                          <div className={cn(
                            "absolute -bottom-2 -right-1 flex items-center gap-0.5 px-0.5 py-0.5 rounded-full text-xs border",
                            getStreakBgColor(player.current_streak || 0)
                          )}>
                            <Flame size={6} className={getStreakColor(player.current_streak || 0)} />
                            <span className={cn("text-xs font-bold", getStreakColor(player.current_streak || 0))}>
                              {player.current_streak}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-sm truncate">
                          {player.display_name}
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="flex">
                            {Array.from({ length: Math.min(player.lives || 0, 3) }).map((_, i) => (
                              <Heart key={i} size={10} className="text-red-500" fill="#ef4444" />
                            ))}
                            {(player.lives || 0) > 3 && (
                              <span className="text-xs text-red-600 ml-1">+{(player.lives || 0) - 3}</span>
                            )}
                          </div>
                          {gameState === "spelling" && (
                            <span className={cn(
                              "text-xs px-1.5 py-0.5 rounded-full",
                              player.has_answered ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                            )}>
                              {player.has_answered ? "✓" : "⏳"}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </PlayerStatsPopup>
                </div>
                
                <div className="text-right">
                  <div className="font-bold text-lg text-amber-800">
                    {player.score || 0}
                  </div>                  {(player.total_answers || 0) > 0 && (
                    <div className={cn("text-xs", getPerformanceColor(player))}>
                      {calculateWPM(player)} WPM
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {players.length > 6 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetailsDialog(true)}
                className="w-full text-sm text-gray-600 hover:text-gray-800"
              >
                <Eye className="w-4 h-4 mr-2" />
                View All {players.length} Players
              </Button>
            )}
          </CardContent>
        </Card>
      )}
      
      {selectedView === 'statistics' && <StatisticsOverview />}

      {/* Detailed Dialog */}
      <DetailedPlayersDialog />

      {/* Spectator Information */}
      {isSpectator && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-blue-800 mb-2">
              <Eye className="w-4 h-4" />
              <span className="font-medium">Spectator Mode</span>
            </div>
            <div className="text-sm text-blue-700 space-y-1">
              <p>👁️ You're watching this match as a spectator</p>
              <p>📊 View live player progress and match statistics</p>
              <p>🏆 See final results when the match concludes</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
