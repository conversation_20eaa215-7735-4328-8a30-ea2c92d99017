'use client'
import { User<PERSON>ir<PERSON>, Clock, LogOut } from 'lucide-react'
import { Button } from './ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from './ui/dropdown-menu'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from './ui/alert-dialog'
import { createClient } from '../../supabase/client'
import { useRouter } from 'next/navigation'
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar'
import { useState, useEffect } from 'react'
import ActivityPopup from './activity-popup'
import StyledAvatar from './styled-avatar'

export default function UserProfile() {
    const supabase = createClient()
    const router = useRouter()
    const [playerProfile, setPlayerProfile] = useState<any>(null);
    const [showSignOutDialog, setShowSignOutDialog] = useState(false);

    const handleSignOut = async () => {
        await supabase.auth.signOut()
        router.refresh()
        setShowSignOutDialog(false)
    }

    useEffect(() => {
        const getUserAvatar = async () => {
            const { data: { user } } = await supabase.auth.getUser()

            const { data: playerProfile } = await supabase
                .from('players')
                .select(`
                    display_name,
                    avatar_url,
                    equipped_avatar_border_id,
                    avatar_border:equipped_avatar_border_id(image_url),
                    equipped_avatar_id,
                    avatar_items:equipped_avatar_id(image_url),
                    level_experience
                `)
                .eq('id', user?.id)
                .single();
            
            setPlayerProfile(playerProfile)
        }
        getUserAvatar()
    }, [supabase])

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                    <div className='relative'>
                        <StyledAvatar
                            src={playerProfile?.avatar_items?.image_url ? playerProfile?.avatar_items?.image_url : playerProfile?.avatar_url}
                            alt={playerProfile?.display_name}
                            fallback={playerProfile?.display_name.substring(0, 2).toUpperCase()}
                            size="md"
                            border={playerProfile?.avatar_border.image_url}
                            totalXP={playerProfile?.level_experience || 0}
                            showLevel={false}
                        />
                    </div>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => router.push('/profile')}>
                    Profile
                </DropdownMenuItem>
                <ActivityPopup>
                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                        <Clock className="h-4 w-4 mr-2" />
                        Recent Activity
                    </DropdownMenuItem>
                </ActivityPopup>                <DropdownMenuSeparator />
                <AlertDialog open={showSignOutDialog} onOpenChange={setShowSignOutDialog}>
                    <AlertDialogTrigger asChild>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                            <LogOut className="h-4 w-4 mr-2" />
                            Sign out
                        </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle className="text-amber-900">Sign Out</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to sign out? You'll need to sign in again to access your account.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction 
                                onClick={handleSignOut}
                                className="bg-red-600 hover:bg-red-700"
                            >
                                Sign Out
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </DropdownMenuContent>
        </DropdownMenu>

    )
}
