'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '../../../supabase/client';
import { useRouter } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Crown,
  Star,
  Gift,
  Zap,
  Calendar,
  Users,
  TrendingUp,
  Lock,
  Check
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import EnhancedBattlePassPanel from "@/components/enhanced-battle-pass-panel";
import { getCompleteBattlePassProgress } from "@/utils/battle-pass-utils";
import { Skeleton } from "@/components/ui/skeleton";

export default function BattlePassPage() {
  const [user, setUser] = useState<any>(null);
  const [playerData, setPlayerData] = useState<any>(null);
  const [battlePassProgress, setBattlePassProgress] = useState<any>(null);
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(true);

  const supabase = createClient();
  const router = useRouter();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        router.push("/sign-in");
        return;
      }

      setUser(currentUser);

      // Get user's player data
      const { data: userData } = await supabase
        .from('players')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      setPlayerData(userData);

      // Get battle pass progress
      const progressData = await getCompleteBattlePassProgress(currentUser.id);
      setBattlePassProgress(progressData);

      const name = userData?.display_name || currentUser.user_metadata?.full_name || 'Anonymous Player';
      setDisplayName(name);

    } catch (error) {
      console.error('Error loading battle pass data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <>
        <DashboardNavbar />
        <main className="w-full bg-gradient-to-br from-amber-50/60 via-orange-50/30 to-yellow-50/40 min-h-screen">
          <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-6 sm:gap-8">
            {/* Header Skeleton */}
            <header className="text-center space-y-4">
              <div className="flex items-center justify-center gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-10 w-48" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
              <Skeleton className="h-6 w-96 mx-auto" />
            </header>

            {/* Info Cards Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="border-2 border-amber-200">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-5 w-5" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Skeleton className="h-6 w-40" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-20 rounded-full" />
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Battle Pass Panel Skeleton */}
            <Card className="border-2 border-amber-200">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton className="h-6 w-6" />
                  <Skeleton className="h-6 w-48" />
                </div>
                <Skeleton className="h-4 w-64" />
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full rounded-full" />
                </div>

                {/* Tier Progress Skeleton */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
                  {Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="bg-white rounded-lg border p-4 space-y-3">
                      <div className="text-center">
                        <Skeleton className="h-4 w-12 mx-auto mb-2" />
                        <Skeleton className="h-16 w-16 mx-auto rounded-lg" />
                      </div>
                      <Skeleton className="h-4 w-full" />
                      <div className="flex justify-between">
                        <Skeleton className="h-6 w-16 rounded-full" />
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Premium Battle Pass Promotion Skeleton */}
            <Card className="border-2 border-yellow-300 bg-gradient-to-r from-yellow-50 to-orange-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-7 w-56" />
                    </div>
                    <Skeleton className="h-4 w-80" />
                    <div className="flex gap-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <Skeleton key={index} className="h-4 w-20" />
                      ))}
                    </div>
                  </div>
                  <Skeleton className="h-12 w-40 rounded-lg" />
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </>
    );
  }

  if (!user || !playerData) {
    return null;
  }

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Crown className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                Battle Pass
              </h1>
              <Crown className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Progress through tiers, unlock exclusive rewards, and show off your dedication to mastering words!
            </p>
          </header>

          {/* Battle Pass Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Current Season */}
            <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2 text-amber-900">
                  <Calendar className="h-5 w-5" />
                  Current Season
                </CardTitle>
              </CardHeader>
              <CardContent>
                {battlePassProgress ? (
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg">{battlePassProgress.season.season_name}</h3>
                    <p className="text-sm text-gray-600">
                      Ends: {new Date(battlePassProgress.season.end_date).toLocaleDateString()}
                    </p>
                    <Badge className={cn(
                      battlePassProgress.playerProgress.has_premium 
                        ? 'bg-amber-100 text-amber-800 border-amber-300' 
                        : 'bg-gray-100 text-gray-700 border-gray-300'
                    )}>
                      {battlePassProgress.playerProgress.has_premium ? 'Premium' : 'Free'}
                    </Badge>
                  </div>
                ) : (
                  <p className="text-gray-500">No active season</p>
                )}
              </CardContent>
            </Card>

            {/* Current Progress */}
            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2 text-blue-900">
                  <TrendingUp className="h-5 w-5" />
                  Your Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                {battlePassProgress ? (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Tier {battlePassProgress.playerProgress.current_tier}</span>
                      <span className="text-sm text-gray-600">
                        {battlePassProgress.playerProgress.current_xp.toLocaleString()} XP
                      </span>
                    </div>
                    <Progress 
                      value={battlePassProgress.progressPercentage} 
                      className="h-2"
                    />
                    {battlePassProgress.nextTierInfo && (
                      <p className="text-xs text-gray-500">
                        {battlePassProgress.xpToNextTier.toLocaleString()} XP to next tier
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">No progress data</p>
                )}
              </CardContent>
            </Card>

            {/* Rewards Info */}
            <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2 text-purple-900">
                  <Gift className="h-5 w-5" />
                  Rewards Available
                </CardTitle>
              </CardHeader>
              <CardContent>
                {battlePassProgress ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Free Track</span>
                    </div>
                    {battlePassProgress.playerProgress.has_premium && (
                      <div className="flex items-center gap-2">
                        <Crown className="h-4 w-4 text-amber-500" />
                        <span className="text-sm">Premium Track</span>
                      </div>
                    )}
                    <p className="text-xs text-gray-600 mt-2">
                      {battlePassProgress.tiers.length} tiers with exclusive cosmetics
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500">No rewards available</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* How to Earn XP */}
          <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-900">
                <Zap className="h-5 w-5" />
                How to Earn Battle Pass XP
              </CardTitle>
              <CardDescription>
                Complete these activities to progress through the battle pass tiers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Play Matches</h3>
                  <p className="text-xs text-gray-600">Earn XP for every match played</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <Crown className="h-8 w-8 text-amber-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Win Games</h3>
                  <p className="text-xs text-gray-600">Bonus XP for victories</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <Star className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Daily Challenges</h3>
                  <p className="text-xs text-gray-600">Complete daily objectives</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Achievements</h3>
                  <p className="text-xs text-gray-600">Unlock special milestones</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Battle Pass Panel */}
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Season Progress</h2>
              <p className="text-gray-600">Track your progress and claim your rewards</p>
            </div>
              <EnhancedBattlePassPanel 
              playerId={user.id}
              className="max-w-4xl mx-auto"
            />
          </div>

          {/* Premium Upgrade CTA */}
          {battlePassProgress && !battlePassProgress.playerProgress.has_premium && (
            <Card className="border-amber-300 bg-gradient-to-r from-amber-100 via-amber-50 to-amber-100 max-w-2xl mx-auto">
              <CardContent className="text-center py-8">
                <Crown className="h-12 w-12 text-amber-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-amber-900 mb-2">Upgrade to Premium</h3>
                <p className="text-amber-800 mb-4">
                  Unlock exclusive premium rewards and get more value from your battle pass journey!
                </p>
                <div className="flex items-center justify-center gap-4 text-sm text-amber-700 mb-6">
                  <div className="flex items-center gap-1">
                    <Check className="h-4 w-4" />
                    <span>Premium rewards</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Check className="h-4 w-4" />
                    <span>Exclusive cosmetics</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Check className="h-4 w-4" />
                    <span>Bonus XP</span>
                  </div>
                </div>
                <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
                  Upgrade Now
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </>
  );
};
