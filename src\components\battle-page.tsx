"use client";

import {
  Dispatch,
  SetStateAction,
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Heart, ArrowLeft, Crown, Flame, Eye } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  getCurrentWordForMatch,
  fetchPlayersFromDatabase,
  initiateSpellingTimer,
  performHeartbeatSyncBattle,
  transitionToBreak,
  transitionToSpelling,
  saveMatchToHistory,
} from "@/utils/battle-utils";
import { calculateMatchXP, awardExperience } from "@/utils/level-system";
import {
  getCurrentSeason,
  createPlayerBattlePassProgress,
  addBattlePassXP,
} from "@/utils/battle-pass-utils";
import RPGainPopup from "./rp-gain-popup";
import StyledAvatar from "./styled-avatar";
import PlayerStatsPopup from "./player-stats-popup";
import BattleLeaderboard from "./battle-leaderboard";
import RealTimeAnalytics from "./real-time-analytics";
import {
  Player,
  User,
  GameStateType,
  currentWord,
} from "@/interfaces/interfaces";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { createClient } from "../../supabase/client";
import React from "react";
import { playWordAudio } from "@/utils/audio-utils";
import { formatTime } from "@/utils/waiting-utils";
import { joinRoom } from "@/utils/room-utils";
import FloatingText from "./floating-text";
import MistakeReviewModal from "./mistake-review-modal";
import { hasPlayerPremiumBattlePass } from "@/utils/battle-pass-utils";
import { useConnectionStatus } from "./internet-status-provider";

interface BattlePageProps {
  roomName: string;
  roomColor: string;
  roomBgColor: string;
  matchId?: string;
  setMatchId?: Dispatch<SetStateAction<string | undefined>>;
  clientGameState: GameStateType;
  setClientGameState: Dispatch<SetStateAction<GameStateType>>;
  clientCurrentWordId: number;
  setClientCurrentWordId: Dispatch<SetStateAction<number>>;
  currentWord: currentWord;
  setCurrentWord: Dispatch<SetStateAction<currentWord>>;
  currentUser: User; // Add currentUser to props
}

export default function BattlePage({
  roomName,
  roomColor,
  roomBgColor,
  matchId,
  setMatchId,
  clientGameState,
  setClientGameState,
  clientCurrentWordId,
  setClientCurrentWordId,
  currentWord,
  setCurrentWord,
  currentUser,
}: BattlePageProps) {
  const supabase = createClient();
  const router = useRouter();
  const connectionStatus = useConnectionStatus();

  const isFirstMount = React.useRef(true);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState(true);
  const [userAnswer, setUserAnswer] = useState("");
  const [playerData, setPlayerData] = useState<Player[]>([]);
  const [initialTime, setInitialTime] = useState(15);
  const [initialBreakTimeInSeconds, setInitialBreakTimeInSeconds] =
    useState(10);
  const [correctAnswer, setCorrectAnswer] = useState("");
  const [timeLeft, setTimeLeft] = useState(15);
  const [breakTimerStart, setBreakTimerStart] = useState(false);
  const [recentEliminations, setRecentEliminations] = useState<string[]>([]);
  const [currentRound, setCurrentRound] = useState(1);
  const [timeLeftPrecise, setTimeLeftPrecise] = useState(15000); // Time in milliseconds for precise tracking
  const [matchStartTime, setMatchStartTime] = useState<number | null>(null);
  const [isMatchEnded, setIsMatchEnded] = useState(false);
  const [showRPPopup, setShowRPPopup] = useState(false);
  const [isValidMatch, setIsvalidMatch] = useState(false);
  const [isAllPlayerAnswered, setIsAllPlayerAnswered] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const [matchEndData, setMatchEndData] = useState<{
    finalRank: number;
    totalPlayers: number;
    score: number;
    difficulty: string;
    eliminationRound?: number;
  } | null>(null);
  // Floating text animation states
  const [floatingText, setFloatingText] = useState<{
    text: string;
    type: "success" | "error";
    isVisible: boolean;
  }>({
    text: "",
    type: "success",
    isVisible: false,
  }); // Mistake review modal states
  const [showMistakeModal, setShowMistakeModal] = useState(false);
  const [lastWrongAnswer, setLastWrongAnswer] = useState("");
  const [lastCorrectAnswer, setLastCorrectAnswer] = useState("");
  const [hasBattlePass, setHasBattlePass] = useState(false);
  const [mistakeCount, setMistakeCount] = useState(0);
  const [mistakeStreak, setMistakeStreak] = useState(0);

  // Current streak is now tracked in the database - no local state needed
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Connection state tracking
  const [isConnectionPaused, setIsConnectionPaused] = useState(false);
  const [pausedTimeLeft, setPausedTimeLeft] = useState<number | null>(null);
  const [pausedTimeLeftPrecise, setPausedTimeLeftPrecise] = useState<
    number | null
  >(null);

  // Synchronization tracking
  const [lastSyncTime, setLastSyncTime] = useState<number>(Date.now());
  const [syncCheckInterval, setSyncCheckInterval] =
    useState<NodeJS.Timeout | null>(null);
  const [isSyncCheckActive, setIsSyncCheckActive] = useState(true);

  // Helper function to show floating text animations
  const showFloatingText = (text: string, type: "success" | "error") => {
    setFloatingText({
      text,
      type,
      isVisible: true,
    });
  };

  const onFloatingTextComplete = () => {
    setFloatingText((prev) => ({ ...prev, isVisible: false }));
  };

  // Helper function to get streak color based on streak length
  const getStreakColor = (streak: number) => {
    if (streak >= 25) return "text-yellow-500"; // Gold for 25+ streaks
    if (streak >= 20) return "text-yellow-400"; // Light gold for 20-24 streaks
    if (streak >= 15) return "text-orange-500"; // Orange for 15-19 streaks
    if (streak >= 10) return "text-red-500"; // Red for 10-14 streaks
    if (streak >= 5) return "text-gray-600"; // Gray for 5-9 streaks
    return "text-gray-400"; // Very light gray for 0 streak
  };

  // Helper function to get streak background color
  const getStreakBgColor = (streak: number) => {
    if (streak >= 10) return "bg-yellow-100"; // Gold background for 10+ streaks
    if (streak >= 7) return "bg-yellow-50"; // Light gold background for 7-9 streaks
    if (streak >= 5) return "bg-orange-100"; // Orange background for 5-6 streaks
    if (streak >= 3) return "bg-red-100"; // Red background for 3-4 streaks
    if (streak >= 1) return "bg-gray-100"; // Gray background for 1-2 streaks
    return "bg-gray-50"; // Very light gray background for 0 streak
  };
  const handleLeaveRoom = async (currentUserId: string) => {
    setIsLeaving(true);
    if (!matchId) {
      router.push("/dashboard");
      return;
    }
    try {
      let leave = false;
      await saveStatistics();

      // Use soft delete instead of hard delete to preserve player data for match history
      const { error: updateError } = await supabase
        .from("match_players")
        .update({
          status: "left",
          left_at: new Date().toISOString(),
        })
        .eq("player_id", currentUserId)
        .eq("match_id", matchId);

      if (updateError) {
        console.error("Error updating player status to left:", updateError);
        toast.error("Failed to leave room: Could not update your status.");
        return;
      }

      // If this player is the only active player left, end the match
      if (activePlayers.length === 0 && playerData.length > 1) {
        console.log("Last active player leaving - ending match");
        await endMatch();
        const success = await saveMatchToHistory(matchId);
        if (success) {
          leave = true;
        } else {
          console.error("Error saving match to history");
        }
      } else {
        leave = true;
      }

      if (leave) {
        toast.success("You have left the room.");
        if (setMatchId) {
          setMatchId(undefined);
        }
        router.push("/dashboard");
      }
    } catch (error) {
      console.error("Exception leaving room:", error);
      toast.error("An unexpected error occurred while leaving the room.");
      setIsLeaving(false);
    }
    setIsLeaving(true);
  };

  // Helper function to check if match should end
  const shouldEndMatch = () => {
    const shouldEnd = activePlayers.length <= 1 && playerData.length > 1;

    if (shouldEnd) {
      console.log(
        `Match should end: ${activePlayers.length} active players out of ${playerData.length} total players`
      );
      console.log(
        "Active players:",
        activePlayers.map((p) => ({ name: p.display_name, lives: p.lives }))
      );
      console.log(
        "All players:",
        playerData.map((p) => ({
          name: p.display_name,
          lives: p.lives,
          spectator: p.is_spectator,
        }))
      );
      setIsMatchEnded(true);
      clearTimer();
    }

    return shouldEnd;
  };

  // Function to save user statistics to user_stats table
  const saveStatistics = async () => {
    if (!matchId || !currentUser) return;

    try {
      // Get current player data
      const currentPlayer = playerData.find((p) => p.id === currentUser.id);
      if (!currentPlayer) return false;

      // Get match data for difficulty and start time
      const { data: matchData, error: matchError } = await supabase
        .from("matches")
        .select("difficulty, created_at, start_time, difficulty, current_round")
        .eq("id", matchId)
        .single();

      if (matchError) {
        return false;
      }

      // Validate match: Must have reached at least round 2
      setIsvalidMatch(matchData.current_round >= 4);
      const isValidMatch = matchData.current_round >= 4;
      if (!isValidMatch) {
        // Show toast notification to inform player
        toast.info("Match too short for rewards (less than 2 rounds)", {
          duration: 4000,
        });
        return true; // Exit early for invalid matches - no statistics, RP, XP, or battle pass XP gained
      }

      // Calculate player's final rank (sorted by score)
      const sortedPlayers = [...playerData].sort(
        (a, b) => (b.score || 0) - (a.score || 0)
      );
      const finalRank =
        sortedPlayers.findIndex((p) => p.id === currentUser.id) + 1; // Get user stats from database
      const { data: currentUserStats, error: playerStatsError } = await supabase
        .from("user_stats")
        .select(
          `total_answers, correct_answers, matches_played, highest_round_reached, longest_streak`
        )
        .eq("user_id", currentUser.id)
        .eq("difficulty", matchData.difficulty)
        .single();

      const totalAnswers =
        currentPlayer.total_answers + (currentUserStats?.total_answers || 0);
      const correctAnswers =
        currentPlayer.correct_answers +
        (currentUserStats?.correct_answers || 0);
      const matchesPlayed = (currentUserStats?.matches_played || 0) + 1;
      const highestRoundReached = Math.max(
        currentUserStats?.highest_round_reached || 0,
        currentRound - 1
      );
      const longestStreak = Math.max(
        currentUserStats?.longest_streak || 0,
        currentPlayer.longest_streak || 0
      );

      // Calculate XP gain for both level and battle pass systems
      const eliminationRound =
        (currentPlayer.lives || 0) <= 0 ? currentRound : undefined;
      const xpGain = calculateMatchXP(
        finalRank,
        playerData.length,
        currentPlayer.score || 0,
        matchData.difficulty,
        eliminationRound
      );

      if (!isValidMatch) {
        xpGain.totalXP = 0;
      }

      // Award level experience
      try {
        const expResult = await awardExperience(currentUser.id, xpGain.totalXP);
        if (expResult.success) {
          console.log(
            `Awarded ${xpGain.totalXP} XP to player ${currentUser.id}`
          );
          if (expResult.leveledUp) {
            console.log(`Player leveled up to level ${expResult.newLevel}!`);
          }
        } else {
          console.error("Failed to award experience to player");
        }
      } catch (expError) {
        console.error("Error awarding experience:", expError);
      }

      // Handle battle pass XP
      try {
        // Get current active season
        const currentSeason = await getCurrentSeason();
        if (currentSeason) {
          // Check if player has battle pass progress, create if not exists
          const { data: existingProgress } = await supabase
            .from("player_battle_pass")
            .select("*")
            .eq("player_id", currentUser.id)
            .eq("season_id", currentSeason.id)
            .single();

          if (!existingProgress) {
            // Create initial progress
            await createPlayerBattlePassProgress(
              currentUser.id,
              currentSeason.id
            );
          }

          // Calculate battle pass XP (50% of level XP)
          const battlePassXP = Math.round(xpGain.totalXP * 0.5);

          const battlePassResult = await addBattlePassXP(
            currentUser.id,
            battlePassXP
          );
          if (battlePassResult) {
            console.log(
              `Awarded ${battlePassXP} Battle Pass XP to player ${currentUser.id}`
            );
          } else {
            console.error("Failed to award battle pass XP to player");
          }
        } else {
          console.log("No active battle pass season found");
        }
      } catch (battlePassError) {
        console.error("Error handling battle pass XP:", battlePassError);
      }
      if (playerStatsError?.code === "PGRST116") {
        const { error: insertError } = await supabase
          .from("user_stats")
          .insert({
            user_id: currentUser.id,
            updated_at: new Date().toISOString(),
            highest_round_reached: highestRoundReached,
            correct_answers: correctAnswers,
            total_answers: totalAnswers,
            matches_played: matchesPlayed,
            difficulty: matchData.difficulty,
            longest_streak: longestStreak,
            rankpoints: 0,
            highest_rankpoints: 0,
            current_rank_tier: "parchment",
            current_rank_division: "IV",
          });

        if (insertError) {
          console.error("Error inserting user statistics:", insertError);
        } else {
          console.log("User statistics inserted successfully");
        }
        return;
      }

      // Update user statistics in user_stats
      const { error: userStatUpdateError } = await supabase
        .from("user_stats")
        .update({
          updated_at: new Date().toISOString(),
          highest_round_reached: highestRoundReached,
          correct_answers: correctAnswers,
          total_answers: totalAnswers,
          matches_played: matchesPlayed,
          longest_streak: longestStreak,
        })
        .eq("user_id", currentUser.id)
        .eq("difficulty", matchData.difficulty);
      if (userStatUpdateError) {
        console.error("Error saving user statistics:", userStatUpdateError);
        console.error("Query details:", {
          user_id: currentUser.id,
          difficulty: matchData.difficulty,
          data: {
            updated_at: new Date().toISOString(),
            highest_round_reached: highestRoundReached,
            correct_answers: correctAnswers,
            total_answers: totalAnswers,
            matches_played: matchesPlayed,
            longest_streak: longestStreak,
          },
        });
      } else {
        console.log("User statistics saved successfully");
      }
    } catch (error) {
      console.error("Error in saveMatchStatistics:", error);
    }

    return true;
  };

  // Function to end the match and transition to results
  const endMatch = async () => {
    if (!matchId) return;
    if (intervalRef.current) {
      clearTimer();
    }

    try {
      // Update match status to finished and current_state to results
      const { error: matchError } = await supabase
        .from("matches")
        .update({
          status: "finished",
          current_state: "results",
        })
        .eq("id", matchId);

      if (matchError) {
        console.error("Error ending match:", matchError);
        return;
      }

      console.log("Match ended - transitioning to results");
      setClientGameState("results");

      // Fetch all of the players from the match
      const fetchAllPlayers = await fetchPlayersFromDatabase(
        roomName,
        matchId,
        true
      );

      setPlayerData(fetchAllPlayers || []);

      // Prepare XP popup data for current user
      if (currentUser && fetchAllPlayers) {
        const currentPlayer = fetchAllPlayers.find(
          (p) => p.id === currentUser.id
        );
        if (currentPlayer) {
          // Find player's rank (sorted by score)
          const sortedPlayers = [...fetchAllPlayers].sort(
            (a, b) => (b.score || 0) - (a.score || 0)
          );
          const finalRank =
            sortedPlayers.findIndex((p) => p.id === currentUser.id) + 1;

          setMatchEndData({
            finalRank,
            totalPlayers: fetchAllPlayers.length,
            score: currentPlayer.score || 0,
            difficulty: roomName,
            eliminationRound:
              (currentPlayer.lives || 0) <= 0 ? currentRound : undefined,
          });

          // Show RP popup after a short delay
          setTimeout(() => {
            setShowRPPopup(true);
          }, 2000);
        }
      }
    } catch (error) {
      console.error("Error ending match:", error);
    }
  };

  // Calculate dynamic timer duration based on round number
  const calculateTimerDuration = (round: number): number => {
    // Start with 15 seconds, decrease by 0.2 seconds each round, minimum 5 seconds
    const baseDuration = 15;
    const decreasePerRound = 0.2;
    const minDuration = 2;

    const calculatedDuration = baseDuration - (round - 1) * decreasePerRound;
    return Math.max(calculatedDuration, minDuration);
  };

  // Calculate score based on response time
  const calculateTimeBasedScore = (
    timeRemainingMs: number,
    totalTimeMs: number
  ): number => {
    // Score range: 500-1000 points
    const minScore = 500;
    const maxScore = 1000;

    // Calculate percentage of time remaining (faster response = higher percentage)
    const timePercentage = timeRemainingMs / totalTimeMs;

    // Calculate score: faster responses get higher scores
    const score = minScore + timePercentage * (maxScore - minScore);

    return Math.round(score);
  };
  const handleCheckAnswer = async () => {
    setClientGameState("break");

    // Prevent spectators from submitting answers
    const currentPlayer = playerData.find((p) => p.id === currentUser.id);
    const isSpectator =
      (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

    if (isSpectator) {
      toast.error("Spectators cannot submit answers.");
      return;
    }

    const trimmedAnswer = userAnswer.trim();
    const isCorrect =
      trimmedAnswer.toLowerCase() === correctAnswer.toLowerCase();

    if (isCorrect) {
      if (!currentPlayer) {
        toast.error("Player not found.");
        return;
      }

      let updatedScore = currentPlayer.score || 0;
      let updatedLives = currentPlayer.lives || 0;
      let lastAnswerStatus = "";

      // Calculate time-based score
      const totalTimeMs = calculateTimerDuration(currentRound) * 1000;
      const timeRemainingMs = timeLeftPrecise;
      const earnedPoints = calculateTimeBasedScore(
        timeRemainingMs,
        totalTimeMs
      );
      const responseTime = (totalTimeMs - timeRemainingMs) / 1000; // Response time in seconds

      updatedScore += earnedPoints;
      lastAnswerStatus = "correct";

      // Reset mistake streak on correct answer
      setMistakeStreak(0);

      // Update current user's streak (correct answer)
      const newCurrentStreak = (currentPlayer?.current_streak || 0) + 1;
      const newLongestStreak = Math.max(
        currentPlayer?.longest_streak || 0,
        newCurrentStreak
      );

      // Show floating points animation
      showFloatingText(`+${earnedPoints}`, "success"); // Update response times array for WPM calculation
      const currentResponseTimes = currentPlayer.responseTimes || [];
      const updatedResponseTimes = [...currentResponseTimes, responseTime];

      // Update player in database
      const { error: updateError } = await supabase
        .from("match_players")
        .update({
          score: updatedScore,
          lives: updatedLives,
          correct_answers: (currentPlayer.correct_answers || 0) + 1,
          total_answers: (currentPlayer.total_answers || 0) + 1,
          lastAnswer: lastAnswerStatus,
          has_answered: true,
          longest_streak: newLongestStreak,
          current_streak: newCurrentStreak,
          response_times: updatedResponseTimes,
        })
        .eq("player_id", currentUser.id)
        .eq("match_id", matchId);

      if (updateError) {
        console.error("Error updating player data:", updateError);
        toast.error("Failed to update player data.");
        return;
      }

      // Update local state
      setPlayerData((prevData) =>
        prevData.map((p) =>
          p.id === currentUser.id
            ? {
                ...p,
                score: updatedScore,
                lives: updatedLives,
                lastAnswer: lastAnswerStatus,
                has_answered: true,
                current_streak: newCurrentStreak,
                longest_streak: newLongestStreak,
                correct_answers: (p.correct_answers || 0) + 1,
                total_answers: (p.total_answers || 0) + 1,
                responseTimes: [...(p.responseTimes || []), responseTime],
              }
            : p
        )
      );

      setUserAnswer("");
    } else {
      // Handle incorrect or empty answers
      if (!currentPlayer) {
        toast.error("Player not found.");
        return;
      }

      let updatedScore = currentPlayer.score || 0;
      let updatedLives = currentPlayer.lives || 0;
      let lastAnswerStatus = "";

      // Calculate response time for tracking
      const totalTimeMs = calculateTimerDuration(currentRound) * 1000;
      const timeRemainingMs = timeLeftPrecise;
      const responseTime = (totalTimeMs - timeRemainingMs) / 1000; // Response time in seconds

      updatedLives = Math.max(0, updatedLives - 1);
      lastAnswerStatus = "incorrect";

      // Track the mistake for review modal (only for non-empty wrong answers)
      setLastWrongAnswer(trimmedAnswer);
      setLastCorrectAnswer(correctAnswer);
      setMistakeCount((prev) => prev + 1);
      setMistakeStreak((prev) => prev + 1);

      // Show floating life loss animation
      showFloatingText("-1❤️", "error");
      // Update response times array for WPM calculation (even for incorrect answers)
      const currentResponseTimes = currentPlayer.responseTimes || [];
      const updatedResponseTimes = [...currentResponseTimes, responseTime];

      // Update player in database (reset current streak for any wrong answer)
      const { error: updateError } = await supabase
        .from("match_players")
        .update({
          score: updatedScore,
          lives: updatedLives,
          correct_answers: currentPlayer.correct_answers || 0,
          total_answers: (currentPlayer.total_answers || 0) + 1,
          lastAnswer: lastAnswerStatus,
          has_answered: true,
          current_streak: 0,
          response_times: updatedResponseTimes,
        })
        .eq("player_id", currentUser.id)
        .eq("match_id", matchId);

      if (updateError) {
        console.error("Error updating player data:", updateError);
        toast.error("Failed to update player data.");
        return;
      } // Update local state
      setPlayerData((prevData) =>
        prevData.map((p) =>
          p.id === currentUser.id
            ? {
                ...p,
                score: updatedScore,
                lives: updatedLives,
                lastAnswer: lastAnswerStatus,
                has_answered: true,
                current_streak: 0,
                total_answers: (p.total_answers || 0) + 1,
                responseTimes: [...(p.responseTimes || []), responseTime],
              }
            : p
        )
      );

      setUserAnswer("");
    }
    fetchPlayers();
  };

  const fetchPlayers = async () => {
    if (matchId) {
      const fetchedPlayers = await fetchPlayersFromDatabase(roomName, matchId);
      setPlayerData(fetchedPlayers || []);

      if (fetchedPlayers && fetchedPlayers.length <= 1) {
        endMatch();
      }
    }
  };

  // Comprehensive synchronization check
  const performSyncCheck = async () => {
    if (!matchId || isConnectionPaused || clientGameState === "results") {
      return;
    }

    const allAnswered =
      activePlayers.every((p) => p.has_answered) && activePlayers.length > 0;

    if (!allAnswered) {
      console.log('fetching players...')
      await fetchPlayers();
    }

    // try {
    //   console.log("🔄 Performing sync check...");

    //   // Fetch current match state from server
    //   const { data: serverMatchData, error: matchError } = await supabase
    //     .from("matches")
    //     .select("current_state, current_round, start_time, all_player_answered, status")
    //     .eq("id", matchId)
    //     .single();

    //   if (matchError || !serverMatchData) {
    //     console.warn("Sync check failed - could not fetch match data:", matchError);
    //     return;
    //   }

    //   let needsSync = false;
    //   const syncIssues: string[] = [];

    //   // Check game state synchronization
    //   if (serverMatchData.current_state !== clientGameState) {
    //     syncIssues.push(`Game state: ${clientGameState} → ${serverMatchData.current_state}`);
    //     needsSync = true;
    //   }

    //   // Check round synchronization
    //   if (serverMatchData.current_round !== currentRound) {
    //     syncIssues.push(`Round: ${currentRound} → ${serverMatchData.current_round}`);
    //     needsSync = true;
    //   }

    //   // Check timer synchronization (if there's a start_time)
    //   if (serverMatchData.start_time && !isAudioPlaying) {
    //     const now = new Date();
    //     const startTime = new Date(serverMatchData.start_time);
    //     const serverTimeLeft = Math.max(0, Math.floor((startTime.getTime() - now.getTime()) / 1000));
    //     const timeDifference = Math.abs(serverTimeLeft - timeLeft);

    //     // If time difference is more than 2 seconds, we need to sync
    //     if (timeDifference > 2) {
    //       syncIssues.push(`Timer: ${timeLeft}s → ${serverTimeLeft}s (diff: ${timeDifference}s)`);
    //       needsSync = true;
    //     }
    //   }

    //   if (needsSync) {
    //     console.warn("🚨 Synchronization issues detected:", syncIssues);

    //     // Perform synchronization
    //     if (serverMatchData.current_state !== clientGameState) {
    //       console.log(`Syncing game state: ${clientGameState} → ${serverMatchData.current_state}`);
    //       setClientGameState(serverMatchData.current_state as GameStateType);
    //     }

    //     if (serverMatchData.current_round !== currentRound) {
    //       console.log(`Syncing round: ${currentRound} → ${serverMatchData.current_round}`);
    //       setCurrentRound(serverMatchData.current_round);
    //     }

    //     // Sync timer if needed
    //     if (serverMatchData.start_time && !isAudioPlaying) {
    //       const now = new Date();
    //       const startTime = new Date(serverMatchData.start_time);
    //       const serverTimeLeft = Math.max(0, Math.floor((startTime.getTime() - now.getTime()) / 1000));
    //       const timeDifference = Math.abs(serverTimeLeft - timeLeft);

    //       if (timeDifference > 2) {
    //         console.log(`Syncing timer: ${timeLeft}s → ${serverTimeLeft}s`);
    //         setTimeLeft(serverTimeLeft);
    //         setTimeLeftPrecise(serverTimeLeft * 1000);
    //       }
    //     }

    //     toast.info("🔄 Game state synchronized", { duration: 2000 });
    //     setLastSyncTime(Date.now());
    //   } else {
    //     console.log("✅ All systems in sync");
    //     setLastSyncTime(Date.now());
    //   }

    // } catch (error) {
    //   console.error("Error during sync check:", error);
    // }
  };

  const playCurrentWordAudio = async (word: currentWord) => {
    if (!matchId || !word.text) return;

    setIsAudioPlaying(true);

    try {
      const audioFinished = await playWordAudio(word.text, word.difficulty);

      // If audio finished playing successfully, run the codeblock
      if (audioFinished) {
        if (matchId) {
          let round = currentRound;

          if (currentRound == 0) {
            // Fetch current round from database
            const { data: matchData, error: matchError } = await supabase
              .from("matches")
              .select("current_round")
              .eq("id", matchId)
              .single();

            round = matchData?.current_round || 1;
          }

          if (round) {
            setCurrentRound(round);

            // Calculate dynamic timer duration
            const dynamicDuration = calculateTimerDuration(round);

            await initiateSpellingTimer(
              matchId,
              setTimeLeft,
              setTimeLeftPrecise,
              setInitialTime,
              dynamicDuration
            );

            setBreakTimerStart(false);
            setIsAudioPlaying(false);
          }
        }
      }
      inputRef.current?.focus();
    } catch (error) {
      console.error("Error in audio playback:", error);
    }
  };
  const playAudio = async (): Promise<currentWord> => {
    if (clientGameState !== "spelling") setClientGameState("spelling");
    const wordData = await getCurrentWordForMatch(matchId);
    setClientCurrentWordId(wordData.id);
    setCorrectAnswer(wordData.text);
    setCurrentWord(wordData);

    // Clear previous mistake data when starting a new word
    setLastWrongAnswer("");
    setLastCorrectAnswer("");

    await playCurrentWordAudio(wordData);

    return wordData;
  };

  const startTimer = useCallback(async () => {
    if (intervalRef.current) return;

    intervalRef.current = setInterval(async () => {
      setTimeLeftPrecise((prev) => {
        const newTime = prev - 100; // Decrease by 100ms
        setTimeLeft(Math.ceil(newTime / 1000)); // Update seconds display
        return Math.max(0, newTime);
      });
    }, 100); // 100ms interval for precise timing

    return () => {
      clearTimer();
    };
  }, []);

  const clearTimer = async () => {
    if (!matchId) return;

    if (intervalRef.current) {
      console.log("Timer Cleared");
      clearInterval(intervalRef.current!);
      intervalRef.current = null;
    }
  };

  // Memoize active players calculation to reduce recalculations
  const activePlayers = useMemo(() => {
    return playerData.filter(
      (p: Player) => !p.is_spectator && (p.lives || 0) > 0
    );
  }, [playerData]);

  useEffect(() => {
    if (!matchId) return;

    const allAnswered =
      activePlayers.every((player) => player.has_answered) &&
      activePlayers.length > 0;

    if (allAnswered && !isAllPlayerAnswered) {
      setIsAllPlayerAnswered(true);
      console.log(`All players have answered!`);
      transitionToBreak(
        matchId,
        setTimeLeft,
        setTimeLeftPrecise,
        setInitialTime,
        initialBreakTimeInSeconds,
        clientCurrentWordId,
        setCurrentRound,
        setIsAudioPlaying,
        setBreakTimerStart,
        clearTimer,
        currentRound
      );
    }
  }, [activePlayers, isConnectionPaused, isAllPlayerAnswered]);

  // Timer
  useEffect(() => {
    // Don't process timer events if connection is paused
    if (isConnectionPaused) {
      return;
    }

    // Check if match should end before processing normal game flow
    if (shouldEndMatch() && matchId && clientGameState !== "results") {
      console.log("Match should end - no active players left");
      endMatch();
      return;
    }

    if (timeLeft <= 0 && matchId && !breakTimerStart) {
      console.log("Time's up! Transitioning to break...");
      transitionToBreak(
        matchId,
        setTimeLeft,
        setTimeLeftPrecise,
        setInitialTime,
        initialBreakTimeInSeconds,
        clientCurrentWordId,
        setCurrentRound,
        setIsAudioPlaying,
        setBreakTimerStart,
        clearTimer,
        currentRound
      );
    }
    // After break time
    else if (timeLeft <= 0 && matchId && breakTimerStart) {
      console.log("Break time's up! Transitioning to spelling...");
      if (shouldEndMatch()) {
        endMatch();
        return;
      }

      setIsAllPlayerAnswered(false);

      transitionToSpelling(
        matchId,
        setBreakTimerStart,
        playAudio,
        clearTimer,
        playerData,
        setPlayerData
      );
    }
  }, [timeLeft, isConnectionPaused]);

  useEffect(() => {
    // Don't start timers if connection is paused
    if (isConnectionPaused) {
      return;
    }

    if (isAudioPlaying && breakTimerStart && clientGameState !== "results") {
      // Start timer for break time
      console.log("Break Timer Started");
      startTimer();
    } else if (
      !isAudioPlaying &&
      !breakTimerStart &&
      clientGameState !== "results"
    ) {
      // Start timer for spelling
      startTimer();
      console.log("Spelling Timer Started");
    }
  }, [breakTimerStart, isAudioPlaying, isConnectionPaused]);

  useEffect(() => {
    if (!matchId) return;
    const subscription = supabase
      .channel("match_changes")
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "matches",
          filter: `id=eq.${matchId}`,
        },
        async (payload) => {
          // Update last sync time when we receive real-time updates
          setLastSyncTime(Date.now());

          if (payload.new.current_state !== clientGameState) {
            console.log(
              `Match state changed from ${clientGameState} to ${payload.new.current_state}`
            );
            if (
              clientGameState == "break" &&
              payload.new.current_state == "spelling" &&
              timeLeftPrecise > 0
            ) {
              transitionToSpelling(
                matchId,
                setBreakTimerStart,
                playAudio,
                clearTimer,
                playerData,
                setPlayerData
              );
            }
            if (payload.new.current_state == "results" && !isMatchEnded) {
              setIsMatchEnded(true);
              endMatch();
            }
            setClientGameState(payload.new.current_state as GameStateType);
          }

          if (payload.new.current_word_id !== clientCurrentWordId) {
            try {
              const { data: wordData, error: wordError } = await supabase
                .from("words")
                .select("*")
                .eq("id", payload.new.current_word_id)
                .single();

              if (wordError) throw wordError;

              if (wordData) {
                setCurrentWord({
                  id: wordData.id,
                  text: wordData.text,
                  difficulty: wordData.difficulty,
                  audioUrl: wordData.audio_url,
                });
              }
              setClientCurrentWordId(wordData.id);
            } catch (error) {
              console.error("Error fetching word data:", error);
            }
          }
        }
      )
      .subscribe();

    // Combined subscription for all match_players updates
    const playerSubscription = supabase
      .channel("match_players_changes")
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "match_players",
          filter: `match_id=eq.${matchId}`,
        },
        async (payload) => {
          // Handle player leaving (status changed to 'left')
          if (payload.new && payload.new.status === "left") {
            console.log("Player soft left the match:", payload.new);

            // Refresh player data to get accurate counts
            if (clientGameState !== "results") {
              await fetchPlayers();
            }
            return; // Early return to avoid processing other updates for left players
          }

          // Check for elimination (lives went to 0)
          const oldPlayer = playerData.find(
            (p) => p.id === payload.new.player_id
          );

          const wasEliminated =
            oldPlayer && (oldPlayer.lives || 0) > 0 && payload.new.lives === 0;

          if (wasEliminated) {
            // Show elimination notification for spectators
            const currentPlayer = playerData.find(
              (p) => p.id === currentUser.id
            );
            const isSpectator =
              (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

            if (isSpectator) {
              toast.info(`${oldPlayer.display_name} has been eliminated!`, {
                duration: 3000,
              });

              // Add to recent eliminations for spectator UI
              setRecentEliminations((prev) => [
                ...prev.slice(-2), // Keep only last 2 eliminations
                oldPlayer.display_name || "Player",
              ]);
            }
          }

          // Update the specific player in the playerData state
          setPlayerData((prevData) => {
            const updatedData = prevData.map((player) =>
              player.id === payload.new.player_id
                ? {
                    ...player,
                    score: payload.new.score,
                    lives: payload.new.lives,
                    lastAnswer: payload.new.lastAnswer,
                    has_answered: payload.new.has_answered,
                    current_streak: payload.new.current_streak,
                    longest_streak: payload.new.longest_streak,
                    correct_answers: payload.new.correct_answers,
                    total_answers: payload.new.total_answers,
                    responseTimes: payload.new.response_times || [],
                  }
                : player
            );

            return updatedData;
          });
        }
      )
      .subscribe();

    // Also keep the DELETE subscription for backward compatibility
    const playerDeletionSubscription = supabase
      .channel("match_players_deletions")
      .on(
        "postgres_changes",
        {
          event: "DELETE",
          schema: "public",
          table: "match_players",
          filter: `match_id=eq.${matchId}`,
        },
        async (payload) => {
          console.log("Player hard deleted from match:", payload.old);

          // Refresh player data to get accurate counts
          if (clientGameState !== "results") {
            await fetchPlayers();
          }
        }
      )
      .subscribe();
    if (isMatchEnded) {
      // No intervals to clear here anymore
    }
    return () => {
      subscription.unsubscribe();
      playerSubscription.unsubscribe();
      playerDeletionSubscription.unsubscribe();
    };
  }, [matchId, supabase, clientGameState, isMatchEnded, playerData, timeLeft]);

  // Separate useEffect for heartbeat interval - runs independently
  useEffect(() => {
    if (!matchId || isMatchEnded) return;

    const heartbeatInterval = setInterval(() => {

      if (intervalRef.current) {
        performHeartbeatSyncBattle(
          supabase,
          matchId,
          setTimeLeft,
          setTimeLeftPrecise,
          isAudioPlaying,
          clientGameState,
          breakTimerStart
        );
      }

    }, 5000);

    if (isMatchEnded) {
      clearInterval(heartbeatInterval);
    }

    return () => {
      clearInterval(heartbeatInterval);
    };
  }, [matchId, isMatchEnded, supabase, isAudioPlaying, clientGameState, breakTimerStart]);

  // Separate useEffect for sync check interval - runs independently
  useEffect(() => {
    if (!matchId || isMatchEnded || !isSyncCheckActive || isConnectionPaused)
      return;

    const enhancedSyncInterval = setInterval(() => {
      performSyncCheck();
    }, 3000);

    if (isMatchEnded) {
      clearInterval(enhancedSyncInterval);
    }

    return () => {
      clearInterval(enhancedSyncInterval);
    };
  }, [matchId, isMatchEnded, isSyncCheckActive, isConnectionPaused, activePlayers]);

  // Check if user has battle pass
  useEffect(() => {
    const checkBattlePass = async () => {
      if (currentUser?.id) {
        const hasPremium = await hasPlayerPremiumBattlePass(currentUser.id);
        setHasBattlePass(hasPremium);
      }
    };
    checkBattlePass();
  }, [currentUser?.id]);

  // Close mistake review modal when game state changes from break to spelling
  useEffect(() => {
    if (clientGameState === "spelling" && showMistakeModal) {
      setShowMistakeModal(false);
    }
  }, [clientGameState, showMistakeModal]);

  useEffect(() => {
    if (isMatchEnded && matchId) {
      saveStatistics();

      (async () => {
        const success = await saveMatchToHistory(matchId);
        if (success) {
          console.log("Match saved to history successfully");
        } else {
          console.error("Error saving match to history");
        }

        await supabase
          .from("matches")
          .update({ current_state: "results" })
          .eq("id", matchId)
          .neq("current_state", "results");
      })();
    }
  }, [isMatchEnded]);

  // Handle connection restoration for battle page
  useEffect(() => {
    const handleConnectionLost = (event: Event) => {
      console.log(
        "Connection lost - pausing timers and preventing database updates"
      );

      // Pause the timer immediately
      clearTimer();

      // Store current timer state
      setPausedTimeLeft(timeLeft);
      setPausedTimeLeftPrecise(timeLeftPrecise);
      setIsConnectionPaused(true);

      // Pause sync checking to avoid unnecessary server calls
      setIsSyncCheckActive(false);

      toast.warning("Connection lost - game paused");
    };

    const handleConnectionRestored = async (event: Event) => {
      const customEvent = event as CustomEvent;
      const { duration } = customEvent.detail;
      console.log(
        `Connection restored after ${duration}ms - syncing battle state`
      );

      try {
        // Clear paused state first
        setIsConnectionPaused(false);
        setPausedTimeLeft(null);
        setPausedTimeLeftPrecise(null);

        // Perform comprehensive sync check after reconnection
        await performSyncCheck();

        // Re-enable sync checking
        setIsSyncCheckActive(true);

        toast.success("Connection restored and synchronized!");
      } catch (error) {
        console.error("Error syncing battle state after reconnection:", error);
        toast.error(
          "Failed to sync battle state. Please refresh if issues persist."
        );
      }
    };

    // Listen for connection events
    window.addEventListener("connection-lost", handleConnectionLost);
    window.addEventListener("connection-restored", handleConnectionRestored);

    return () => {
      window.removeEventListener("connection-lost", handleConnectionLost);
      window.removeEventListener(
        "connection-restored",
        handleConnectionRestored
      );
    };
  }, [
    matchId,
    clientGameState,
    currentRound,
    timeLeft,
    timeLeftPrecise,
    supabase,
  ]);

  useEffect(() => {
    fetchPlayers();
  }, []);
  // First mount
  useEffect(() => {
    (async () => {
      if (!matchId || playerData.length === 0) {
        if (playerData.length === 0) {
          fetchPlayers();
        }
        return;
      }

      if (isFirstMount.current) {
        isFirstMount.current = false;

        // Run test calculations for development
        // testCalculations();
        const currentPlayer = playerData.find(
          (player) => player.id === currentUser.id
        );

        const { data: matchStateData, error } = await supabase
          .from("matches")
          .select("current_state, current_round")
          .eq("id", matchId)
          .single();

        if (error) {
          console.error(error);
        } else if (matchStateData) {
          // Initialize current round and timer
          const round = matchStateData.current_round || 1;
          setCurrentRound(round);
          setClientGameState(matchStateData.current_state as GameStateType);

          // Initialize match start time for statistics tracking
          if (!matchStartTime) {
            setMatchStartTime(Date.now());
          }

          // If current state is spelling and current player hasn't answered, play audio
          if (
            matchStateData.current_state === "spelling" &&
            currentPlayer?.has_answered === false
          ) {
            await playAudio();
          }

          // If current state is spelling and current player has answered, set game state to break
          if (
            matchStateData.current_state === "spelling" &&
            currentPlayer?.has_answered === true
          ) {
            setClientGameState("break");

            await initiateSpellingTimer(
              matchId,
              setTimeLeft,
              setTimeLeftPrecise,
              setInitialTime,
              initialTime
            );

            setIsAudioPlaying(false);
            setBreakTimerStart(false);
          }

          if (matchStateData.current_state === "break") {
            await initiateSpellingTimer(
              matchId,
              setTimeLeft,
              setTimeLeftPrecise,
              setInitialTime,
              initialTime
            );

            setBreakTimerStart(true);
          }
        }
      }
    })();
  }, [fetchPlayers]);

  return (
    <div className="bg-amber-50/30 min-h-screen w-full">
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-8">
        {/* Header */}
        {clientGameState !== "results" && (
          <header className="flex flex-col gap-3 sm:gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 sm:gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-amber-800 hover:text-amber-900 hover:bg-amber-100 h-8 w-8 sm:h-9 sm:w-9"
                  onClick={() => handleLeaveRoom(currentUser.id)}
                  disabled={isLeaving}
                >
                  <ArrowLeft size={18} />
                </Button>
                <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-amber-900">
                  {roomName} Battle
                </h1>
              </div>
              <div
                className={cn(
                  "px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm",
                  roomBgColor
                )}
              >
                <span className={cn("font-medium", roomColor)}>{roomName}</span>
              </div>
            </div>
          </header>
        )}
        {/* Timer */}
        <div
          className={cn(
            "text-sm p-3 px-4 rounded-lg flex flex-col gap-2 border sticky top-[10px] z-10",
            isConnectionPaused
              ? "bg-orange-100 text-orange-800 border-orange-200"
              : "bg-amber-100 text-amber-800 border-amber-200"
          )}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock size={14} />
              <span>
                {isConnectionPaused && "⏸️ Game Paused - Connection Lost"}
                {!isConnectionPaused &&
                  clientGameState === "spelling" &&
                  isAudioPlaying &&
                  `Listening...`}
                {!isConnectionPaused &&
                  clientGameState === "spelling" &&
                  !isAudioPlaying &&
                  `Time left: ${formatTime(timeLeft)} (${initialTime.toFixed(2)}s timer)`}
                {!isConnectionPaused &&
                  clientGameState === "break" &&
                  "Break time!" + timeLeft}
                {!isConnectionPaused &&
                  clientGameState === "gameOver" &&
                  "Game Over!"}
                {!isConnectionPaused &&
                  clientGameState === "results" &&
                  "Final Results"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {clientGameState !== "results" && (
                <span>Round {currentRound}</span>
              )}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          {/* Main game area - conditionally rendered based on clientGameState */}
          <div className="md:col-span-2 bg-white rounded-xl p-6 border shadow-sm">
            {clientGameState === "spelling" &&
              (() => {
                const currentPlayer = playerData.find(
                  (p) => p.id === currentUser.id
                );
                const isSpectator =
                  (currentPlayer?.lives || 0) <= 0 ||
                  currentPlayer?.is_spectator;

                if (isSpectator) {
                  // Spectator UI for spelling state
                  return (
                    <div className="flex flex-col items-center justify-center py-8">
                      {/* Spectator Badge */}
                      <div className="mb-4 px-4 py-2 bg-gray-100 border border-gray-300 rounded-full">
                        <span className="text-sm font-medium text-gray-700">
                          👁️ SPECTATING
                        </span>
                      </div>

                      <h2 className="text-xl font-semibold text-amber-900 mb-6">
                        Players are spelling the word
                      </h2>

                      <div className="flex flex-col items-center gap-2 mb-8">
                        <div className="flex items-center gap-2">
                          {isAudioPlaying ? (
                            <div className="text-center">
                              <div className="h-12 w-48 bg-amber-100 animate-pulse rounded-lg mb-2"></div>
                              <p className="text-sm text-amber-600">
                                Word is being played...
                              </p>
                            </div>
                          ) : (
                            <>
                              <div className="text-4xl font-bold text-amber-800">
                                ??????
                              </div>
                            </>
                          )}
                        </div>

                        {isAudioPlaying ? (
                          <div className="h-6 w-24 bg-amber-100 animate-pulse rounded-full mt-2"></div>
                        ) : (
                          <div
                            className={cn(
                              "text-xs px-2 py-1 rounded-full mt-2",
                              currentWord.difficulty === "easy"
                                ? "bg-green-100 text-green-800"
                                : currentWord.difficulty === "medium"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-amber-100 text-amber-800"
                            )}
                          >
                            {currentWord.difficulty.charAt(0).toUpperCase() +
                              currentWord.difficulty.slice(1)}{" "}
                            difficulty
                          </div>
                        )}
                      </div>

                      {/* Timer Progress Bar */}
                      <div className="w-full max-w-md mb-6">
                        <div
                          className={cn(
                            "text-center mb-3 py-2 px-4 rounded-md text-sm font-medium",
                            isConnectionPaused
                              ? "bg-orange-100 text-orange-800"
                              : "bg-blue-100 text-blue-800"
                          )}
                        >
                          {isConnectionPaused
                            ? "⏸️ Game paused - waiting for connection..."
                            : isAudioPlaying
                              ? "Players are listening to the word..."
                              : `Players have ${formatTime(timeLeft)} to spell the word`}
                        </div>

                        <div className="w-full mb-3 h-2 bg-amber-200 rounded-full overflow-hidden">
                          <div
                            className={cn(
                              "h-full transition-all duration-100",
                              isConnectionPaused
                                ? "bg-orange-500"
                                : "bg-amber-600"
                            )}
                            style={{
                              width: isConnectionPaused
                                ? "100%"
                                : `${(timeLeftPrecise / (initialTime * 1000)) * 100}%`,
                            }}
                          />
                        </div>
                      </div>

                      {/* Recent Eliminations for Spectators */}
                      {recentEliminations.length > 0 && (
                        <div className="w-full max-w-md bg-red-50 rounded-xl p-4 border border-red-200 shadow-sm mt-4">
                          <h3 className="text-lg font-semibold text-red-900 mb-3 text-center">
                            Recent Eliminations
                          </h3>
                          <div className="space-y-1">
                            {recentEliminations
                              .slice(-3)
                              .map((playerName, index) => (
                                <div
                                  key={index}
                                  className="text-sm text-red-800 text-center"
                                >
                                  💀 {playerName} was eliminated
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                } else {
                  // Active player UI for spelling state
                  return (
                    <div className="flex flex-col items-center justify-center py-8">
                      <h2 className="text-xl font-semibold text-amber-900 mb-6">
                        Spell the word:
                      </h2>

                      <div className="flex flex-col items-center gap-2 mb-8">
                        <div className="flex items-center gap-2">
                          {isAudioPlaying ? (
                            <div className="text-center">
                              <div className="h-12 w-48 bg-amber-100 animate-pulse rounded-lg mb-2"></div>
                              <p className="text-sm text-amber-600">
                                Loading word...
                              </p>
                            </div>
                          ) : (
                            <>
                              <div className="text-4xl font-bold text-amber-800">
                                ??????
                              </div>
                            </>
                          )}
                        </div>

                        {isAudioPlaying ? (
                          <div className="h-6 w-24 bg-amber-100 animate-pulse rounded-full mt-2"></div>
                        ) : (
                          <div
                            className={cn(
                              "text-xs px-2 py-1 rounded-full mt-2",
                              currentWord.difficulty === "easy"
                                ? "bg-green-100 text-green-800"
                                : currentWord.difficulty === "medium"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-amber-100 text-amber-800"
                            )}
                          >
                            {currentWord.difficulty.charAt(0).toUpperCase() +
                              currentWord.difficulty.slice(1)}{" "}
                            difficulty
                          </div>
                        )}
                      </div>

                      <div className="w-full max-w-md">
                        <div
                          className={cn(
                            "text-center mb-3 py-2 px-4 rounded-md text-sm font-medium",
                            isAudioPlaying
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-green-100 text-green-800"
                          )}
                        >
                          {isAudioPlaying
                            ? "Now listen to the word!"
                            : "Now spell the word!"}
                        </div>

                        <div className="w-full mb-3 h-1 bg-amber-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-amber-600 transition-all duration-100"
                            style={{
                              width: `${(timeLeftPrecise / (initialTime * 1000)) * 100}%`,
                            }}
                          />
                        </div>
                        <form
                          className="flex gap-2"
                          onSubmit={(e) => {
                            e.preventDefault();
                            handleCheckAnswer();
                          }}
                        >
                          <input
                            ref={inputRef}
                            type="text"
                            className="flex h-10 w-full rounded-md border border-amber-300 bg-white px-3 py-2 text-sm placeholder:text-amber-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-amber-400 focus-visible:ring-offset-2"
                            placeholder="Type the word here..."
                            value={userAnswer}
                            onChange={(e) => {
                              if (!isAudioPlaying) {
                                setUserAnswer(e.target.value);
                              }
                            }}
                          />
                          <Button
                            className="bg-amber-600 hover:bg-amber-700 text-white"
                            type="submit"
                            disabled={isAudioPlaying}
                          >
                            Submit
                          </Button>
                        </form>
                      </div>

                      <div className="mt-6 flex justify-center">
                        <div className="flex gap-2">
                          {(() => {
                            const lives = currentPlayer?.lives || 0;
                            return Array.from({ length: lives }).map((_, i) => (
                              <Heart
                                key={i}
                                size={24}
                                className="text-red-500"
                                fill="#ef4444"
                              />
                            ));
                          })()}
                        </div>
                      </div>
                    </div>
                  );
                }
              })()}

            {clientGameState === "break" &&
              (() => {
                const currentPlayer = playerData.find(
                  (p) => p.id === currentUser.id
                );
                const isSpectator =
                  (currentPlayer?.lives || 0) <= 0 ||
                  currentPlayer?.is_spectator;

                return (
                  <div className="flex flex-col items-center justify-center py-8">
                    {/* Spectator badge for break time */}
                    {isSpectator && (
                      <div className="mb-4 px-4 py-2 bg-gray-100 border border-gray-300 rounded-full">
                        <span className="text-sm font-medium text-gray-700">
                          👁️ SPECTATING
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-4">
                      {!breakTimerStart ? (
                        <div className="text-4xl font-bold text-amber-600 mb-2">
                          Break Time!
                        </div>
                      ) : (
                        <div className="text-4xl font-bold text-amber-600 mb-2">
                          {formatTime(timeLeft)}
                        </div>
                      )}
                      {!breakTimerStart ? (
                        <p className="text-amber-700">
                          {isSpectator
                            ? "Watch the next round unfold..."
                            : "Next round starting soon..."}
                        </p>
                      ) : (
                        <p className="text-amber-700">
                          {isSpectator
                            ? "Next round is about to begin!"
                            : "Get ready for the next round!"}
                        </p>
                      )}{" "}
                    </div>
                    {/* Mistake Review Button - Only for non-spectators who have made a non-empty mistake */}
                    {!isSpectator && lastCorrectAnswer && (
                      <div className="mb-6 animate-fade-in">
                        <Button
                          onClick={() => setShowMistakeModal(true)}
                          variant="outline"
                          className={cn(
                            "border-amber-300 text-amber-700 hover:bg-amber-50 flex items-center gap-2 transition-all duration-200 hover:scale-105 shadow-sm",
                            mistakeStreak >= 2 &&
                              "border-red-300 text-red-700 hover:bg-red-50"
                          )}
                        >
                          <Eye className="h-4 w-4" />
                          Review Last Mistake
                          {mistakeStreak >= 2 && (
                            <Badge
                              variant="destructive"
                              className="text-xs px-1 py-0 h-5"
                            >
                              {mistakeStreak}x
                            </Badge>
                          )}
                          {!hasBattlePass && (
                            <Crown className="h-4 w-4 text-amber-600 animate-pulse" />
                          )}
                        </Button>
                        <p className="text-xs text-gray-500 mt-1 text-center">
                          {hasBattlePass
                            ? mistakeStreak >= 2
                              ? `${mistakeStreak} mistakes in a row - time to review!`
                              : "Analyze your spelling mistake"
                            : "Premium feature - upgrade to unlock"}
                        </p>
                      </div>
                    )}

                    {/* Enhanced Leaderboard */}
                    <BattleLeaderboard
                      players={playerData}
                      currentUserId={currentUser.id}
                      gameState={clientGameState}
                      currentRound={currentRound}
                      timeLeft={timeLeft}
                      roomName={roomName}
                      className="w-full max-w-md"
                    />
                  </div>
                );
              })()}

            {clientGameState === "gameOver" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-red-600 mb-2">
                    Game Over!
                  </div>
                  <p className="text-amber-700">The battle has ended</p>
                </div>

                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm mb-4">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">
                    Final Scores
                  </h3>
                  <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                    <span className="font-medium text-amber-800">Player</span>
                    <span className="font-medium text-amber-800 text-center">
                      Status
                    </span>
                    <span className="font-medium text-amber-800 text-right">
                      Score
                    </span>
                  </div>

                  {playerData.map((player) => (
                    <div
                      key={player.id}
                      className={cn(
                        "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                        ((player.lives || 0) <= 0 || player.is_spectator) &&
                          "bg-gray-100"
                      )}
                    >
                      <PlayerStatsPopup
                        playerId={player.id}
                        playerName={player.display_name || "Anonymous"}
                        playerAvatar={player.avatar_url}
                        playerBorder={player.avatar_border}
                      >
                        <div className="flex items-center gap-2 cursor-pointer hover:bg-amber-50 rounded p-1 transition-colors">
                          <div className="relative">
                            <StyledAvatar
                              src={player.avatar_url}
                              alt={player.display_name || ""}
                              fallback={
                                player.display_name
                                  ?.split(" ")
                                  .map((word: string) =>
                                    word.charAt(0).toUpperCase()
                                  )
                                  .join("")
                                  .substring(0, 2) || "AN"
                              }
                              size="md"
                              border={player.avatar_border}
                            />
                            {/* Streak Display - positioned below-right of avatar */}
                            {(() => {
                              if ((player.current_streak || 0) > 5) {
                                return (
                                  <div
                                    className={cn(
                                      "absolute -bottom-3 -right-2 flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs border border-gray-300",
                                      getStreakBgColor(
                                        player.current_streak || 0
                                      )
                                    )}
                                  >
                                    <Flame
                                      size={8}
                                      className={cn(
                                        "",
                                        getStreakColor(
                                          player.current_streak || 0
                                        )
                                      )}
                                    />
                                    <span
                                      className={cn(
                                        "text-xs font-bold",
                                        getStreakColor(
                                          player.current_streak || 0
                                        )
                                      )}
                                    >
                                      {player.current_streak || 0}
                                    </span>
                                  </div>
                                );
                              }
                              return null;
                            })()}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm text-amber-800">
                              {player.display_name}
                            </div>
                            <div className="flex mt-1">
                              {Array.from({ length: player.lives || 0 }).map(
                                (_, i) => (
                                  <Heart
                                    key={i}
                                    size={14}
                                    className="text-red-500"
                                    fill="#ef4444"
                                  />
                                )
                              )}
                            </div>
                          </div>
                        </div>
                      </PlayerStatsPopup>
                      <div className="flex justify-center">
                        <span
                          className={cn(
                            "text-xs px-2 py-1 rounded-full",
                            player.lastAnswer === "correct"
                              ? "bg-green-100 text-green-800"
                              : player.lastAnswer === "incorrect"
                                ? "bg-red-100 text-red-800"
                                : "bg-amber-100 text-amber-800",
                            ((player.lives || 0) <= 0 || player.is_spectator) &&
                              "hidden" // Hide status for spectators
                          )}
                        >
                          {player.lastAnswer}
                        </span>
                      </div>
                      <div className="font-medium text-amber-800 text-right">
                        {player.score || 0}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {clientGameState === "results" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-green-600 mb-2">
                    Battle Results
                  </div>
                  <p className="text-amber-700">Final standings</p>
                </div>

                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">
                    Rankings
                  </h3>
                  {[...playerData]
                    .sort((a, b) => (b.score || 0) - (a.score || 0))
                    .map((player, index) => (
                      <div
                        key={player.id}
                        className={cn(
                          "grid grid-cols-5 items-center gap-2 py-2 px-2 rounded-lg",
                          index < 3
                            ? "border-b-2 border-amber-200"
                            : "border-b border-amber-100",
                          player.id === currentUser.id &&
                            "bg-amber-100 border-amber-300"
                        )}
                      >
                        <div className="col-span-5 flex justify-between items-center min-w-0">
                          {/* Left side: Rank, Avatar, Name, Status */}
                          <div className="flex items-center gap-3 min-w-0">
                            <div className="w-8 flex-shrink-0 text-center text-lg font-bold text-amber-800">
                              {index + 1}
                            </div>
                            <div
                              className={cn("relative", index < 3 && "mt-3")}
                            >
                              {index === 0 && (
                                <Crown
                                  size={18}
                                  className="absolute -top-3.5 left-1/2 -translate-x-1/2 z-10 text-yellow-500"
                                  fill="currentColor"
                                />
                              )}
                              {index === 1 && (
                                <Crown
                                  size={18}
                                  className="absolute -top-3.5 left-1/2 -translate-x-1/2 z-10 text-gray-400"
                                  fill="currentColor"
                                />
                              )}
                              {index === 2 && (
                                <Crown
                                  size={18}
                                  className="absolute -top-3.5 left-1/2 -translate-x-1/2 z-10 text-amber-600"
                                  fill="currentColor"
                                />
                              )}
                              <PlayerStatsPopup
                                playerId={player.id}
                                playerName={player.display_name || "Anonymous"}
                                playerAvatar={player.avatar_url}
                                playerBorder={player.avatar_border}
                              >
                                <div className="cursor-pointer">
                                  <StyledAvatar
                                    src={player.avatar_url}
                                    alt={player.display_name || ""}
                                    fallback={
                                      player.display_name
                                        ?.split(" ")
                                        .map((word: string) =>
                                          word.charAt(0).toUpperCase()
                                        )
                                        .join("")
                                        .substring(0, 2) || "AN"
                                    }
                                    size="lg"
                                    border={player.avatar_border}
                                  />
                                </div>
                              </PlayerStatsPopup>
                            </div>
                            <div className="flex flex-col min-w-0">
                              <div
                                className={cn(
                                  "font-bold text-amber-900 text-wrap truncate",
                                  "max-w-[7rem] sm:max-w-[10rem] md:max-w-[12rem] lg:max-w-[16rem]"
                                )}
                                title={player.display_name}
                              >
                                {player.display_name}
                              </div>
                              {/* Status column */}
                              <div>
                                {["left", "eliminated"].includes(
                                  player.status ?? ""
                                ) && (
                                  <span
                                    className={cn(
                                      "text-xs px-2 py-1 rounded-full font-semibold capitalize text-nowrap",
                                      player.status === "left" &&
                                        "bg-gray-200 text-gray-700 border border-gray-300",
                                      player.status === "eliminated" &&
                                        "bg-red-100 text-red-700 border border-red-200"
                                    )}
                                    title={
                                      player.status === "left"
                                        ? "Player left the match"
                                        : "Player was eliminated"
                                    }
                                  >
                                    {player.status === "left" &&
                                      "Left the match"}
                                    {player.status === "eliminated" &&
                                      "Eliminated"}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          {/* Right side: Score */}
                          <div className="text-right text-xl font-bold text-amber-800 break-words min-w-[3.5rem]">
                            {player.score || 0}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 mt-6">
                  <Button
                    onClick={() => handleLeaveRoom(currentUser.id)}
                    variant="outline"
                    className="border-amber-300 text-amber-700 hover:bg-amber-50"
                    disabled={isLeaving}
                  >
                    <ArrowLeft size={16} className="mr-2" />
                    Back to Dashboard
                  </Button>
                  <Button
                    onClick={async () => {
                      const result = await joinRoom(roomName);
                      if (result.success && result.redirectPath) {
                        router.push(result.redirectPath);
                      } else if (result.redirectPath) {
                        // User is already in a different room
                        router.push(result.redirectPath);
                      }
                    }}
                    className="bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    Play Again
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Right Sidebar - Enhanced Leaderboard */}
          <div className="md:col-span-1 space-y-4">
            <div className="hidden md:block w-80">
              <BattleLeaderboard
                players={playerData}
                currentUserId={currentUser.id}
                gameState={clientGameState}
                currentRound={currentRound}
                timeLeft={timeLeft}
                roomName={roomName}
              />
            </div>

            {/* Real-time Analytics */}
            <RealTimeAnalytics
              players={playerData}
              currentRound={currentRound}
              timeLeft={timeLeft}
              gameState={clientGameState}
              matchStartTime={matchStartTime || undefined}
            />
          </div>
        </div>
        {/* Game info section */}
        <div className="bg-white rounded-xl p-6 border shadow-sm">
          <h2 className="text-xl font-semibold text-amber-900 mb-4">
            Battle Information
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">
                Rules
              </h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Type the word correctly within 15 seconds</li>
                <li>Each correct answer earns you 10 points</li>
                <li>Three mistakes and you're eliminated</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">
                Tips
              </h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Listen carefully to the pronunciation</li>
                <li>Watch for common spelling patterns</li>
                <li>Double-check before submitting</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">
                Difficulty: {roomName}
              </h3>
              <p className="text-sm text-amber-700">
                {roomName === "Easy" &&
                  "Simple words with common spelling patterns"}
                {roomName === "Medium" &&
                  "Moderate difficulty with some tricky words"}
                {roomName === "Hard" &&
                  "Challenging words that test your skills"}
              </p>
            </div>
          </div>
        </div>{" "}
        {/* RP Gain Popup */}
        {matchEndData && (
          <RPGainPopup
            matchId={matchId || ""}
            finalRank={matchEndData.finalRank}
            totalPlayers={matchEndData.totalPlayers}
            score={matchEndData.score}
            difficulty={matchEndData.difficulty}
            eliminationRound={matchEndData.eliminationRound}
            isVisible={showRPPopup}
            onClose={() => {
              setShowRPPopup(false);
              setMatchEndData(null);
            }}
            isValidMatch={isValidMatch}
          />
        )}{" "}
        {/* Floating Text Animation */}
        <FloatingText
          text={floatingText.text}
          type={floatingText.type}
          isVisible={floatingText.isVisible}
          onComplete={onFloatingTextComplete}
        />
        {/* Mistake Review Modal */}
        <MistakeReviewModal
          isOpen={showMistakeModal}
          onClose={() => setShowMistakeModal(false)}
          userAnswer={lastWrongAnswer}
          correctAnswer={lastCorrectAnswer}
          wordDifficulty={currentWord.difficulty}
          hasBattlePass={hasBattlePass}
        />
      </div>
    </div>
  );
}
