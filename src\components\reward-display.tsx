import React, { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Zap, Star, Gift, Crown, Eye } from 'lucide-react';
import { Reward, getRarityColor, getTypeIcon } from '@/lib/item-utils';
import { RewardPreviewModal } from '@/components/reward-preview-modal';
import { Button } from './ui/button';
import { BattlePassTier } from '@/lib/admin-battle-pass-utils';
import { checkUnlockRequirement } from '@/utils/battle-pass-utils';

interface RewardDisplayProps {
  tier: BattlePassTier;
  reward: Reward;
  isPremium?: boolean;
  className?: string;
  showDescription?: boolean;
  size?: 'sm' | 'md' | 'lg';
  clickable?: boolean;
  playerLevel?: number;
  playerRP?: number;
  hasBattlePass?: boolean;
  battlePassTier?: number;
  isUnlocked?: boolean;
  isEquipped?: boolean;
  equippedItems?: { [key: string]: string };
  onEquip?: (itemId: string, itemType: string) => void;
  onPreview?: (itemId: string, itemType: string) => void;
  onUnlock: (itemId: string) => void;
  isItemUnlocked: (itemId: string) => boolean;
}

export function RewardDisplay({ 
  tier,
  reward,
  isPremium = false,
  className,
  showDescription = false,
  size = 'md',
  clickable = true,
  playerLevel = 1,
  playerRP = 0,
  hasBattlePass = false,
  battlePassTier = 0,
  isUnlocked = true,
  isEquipped = false,
  isItemUnlocked,
  equippedItems = {},
  onEquip,
  onPreview,
  onUnlock
}: RewardDisplayProps) {
  const [showModal, setShowModal] = useState(false);
  const [unlockInfo, setUnlockInfo] = useState({
    unlocked: false,
    message: 'No requirements',
    canUnlock: false
  });

  if (reward.type === 'item') {
    useEffect(() => {
      const unlockRequirement = isPremium 
        ? tier.premium_reward.item?.unlock_requirement 
        : tier.free_reward.item?.unlock_requirement;

      if (unlockRequirement) {
        const unlockInfo = checkUnlockRequirement(
          unlockRequirement, 
          isPremium,
          playerLevel, 
          playerRP, 
          hasBattlePass, 
          battlePassTier
        )
        setUnlockInfo(
          unlockInfo
        );
        // console.log(unlockInfo, reward.item)
      }
    }, [])
  }

  const handleClick = () => {
    if (clickable) {
      setShowModal(true);
    }
  };

  if (!reward) {
    return (
      <>
        <div className={cn(
          'flex items-center gap-2 p-2 bg-gray-50 rounded-lg',
          className
        )}>
          <div className="p-1 bg-gray-100 rounded">
            <Gift className="h-4 w-4 text-gray-400" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">No reward</p>
          </div>
        </div>
      </>
    );
  }

  const sizeClasses = {
    sm: 'p-2 text-xs',
    md: 'p-3 text-sm',
    lg: 'p-4 text-base'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  if (reward.type === 'xp') {
    return (
      <>
        <div 
          className={cn(
            'flex items-center gap-3 bg-blue-50 rounded-lg transition-all',
            clickable && 'cursor-pointer hover:shadow-sm hover:bg-blue-100',
            sizeClasses[size],
            className
          )}
          onClick={handleClick}
        >
          <div className="p-2 bg-blue-100 rounded">
            <Zap className={cn(iconSizes[size], 'text-blue-600')} />
          </div>
          <div className="flex-1">
            <p className="font-medium text-blue-900">{reward.amount} XP</p>
            {showDescription && (
              <p className="text-xs text-blue-700">Experience Points</p>
            )}
          </div>
          {clickable && (
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <Eye className="h-4 w-4 text-blue-600" />
            </div>
          )}
        </div>
        
        <RewardPreviewModal
          reward={reward}
          unlockInfo={unlockInfo}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          playerLevel={playerLevel}
          playerRP={playerRP}
          hasBattlePass={hasBattlePass}
          battlePassTier={battlePassTier}
          isUnlocked={isUnlocked}
          isEquipped={isEquipped}
        />
      </>
    );
  }

  if (reward.type === 'currency') {
    return (
      <>
        <div 
          className={cn(
            'flex items-center gap-3 bg-amber-50 rounded-lg transition-all',
            clickable && 'cursor-pointer hover:shadow-sm hover:bg-amber-100',
            sizeClasses[size],
            className
          )}
          onClick={handleClick}
        >
          <div className="p-2 bg-amber-100 rounded">
            <Star className={cn(iconSizes[size], 'text-amber-600')} />
          </div>
          <div className="flex-1">
            <p className="font-medium text-amber-900">{reward.amount} Coins</p>
            {showDescription && (
              <p className="text-xs text-amber-700">Game Currency</p>
            )}
          </div>
          {clickable && (
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <Eye className="h-4 w-4 text-amber-600" />
            </div>
          )}
        </div>
        
        <RewardPreviewModal
          reward={reward}
          unlockInfo={unlockInfo}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          playerLevel={playerLevel}
          playerRP={playerRP}
          hasBattlePass={hasBattlePass}
          battlePassTier={battlePassTier}
          isUnlocked={isUnlocked}
          isEquipped={isEquipped}
        />
      </>
    );
  }

  if (reward.type === 'item') {
    const item = reward.item;

    if (!item) {
      return (
        <>
          <div className={cn(
            'flex items-center gap-3 bg-gray-50 rounded-lg',
            sizeClasses[size],
            className
          )}>
            <div className="p-2 bg-gray-100 rounded">
              <Gift className={cn(iconSizes[size], 'text-gray-400')} />
            </div>
            <div className="flex-1">
              <p className="font-medium text-gray-500">Unknown Item</p>
              <p className="text-xs text-gray-400">Item ID: {reward.item_id}</p>
            </div>
          </div>
        </>
      );
    }
    
    return (
      <>
        <div 
          className={cn(
            'flex items-center gap-3 bg-white border rounded-lg transition-all group',
            clickable && 'cursor-pointer hover:shadow-md hover:border-amber-200',
            sizeClasses[size],
            className
          )}
          onClick={handleClick}
        >
          {/* Item Image or Icon */}
          <div className="relative">
            {item.image_url ? (
              <div className="relative">
                <img
                  src={item.image_url}
                  alt={item.name}
                  className={cn(
                    'rounded object-cover',
                    size === 'sm' ? 'w-8 h-8' : size === 'md' ? 'w-10 h-10' : 'w-12 h-12'
                  )}
                  onError={(e) => {
                    // Fallback to icon if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
                <div 
                  className={cn(
                    'hidden items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200 rounded text-purple-600',
                    size === 'sm' ? 'w-8 h-8 text-lg' : size === 'md' ? 'w-10 h-10 text-xl' : 'w-12 h-12 text-2xl'
                  )}
                >
                  {getTypeIcon(item.type)}
                </div>
              </div>
            ) : (
              <div className={cn(
                'flex items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200 rounded text-purple-600',
                size === 'sm' ? 'w-8 h-8 text-lg' : size === 'md' ? 'w-10 h-10 text-xl' : 'w-12 h-12 text-2xl'
              )}>
                {getTypeIcon(item.type)}
              </div>
            )}
          </div>

          {/* Item Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <p className={cn(
                'font-medium text-gray-900 truncate',
                size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
              )}>
                {item.name}
              </p>
              <Badge 
                variant="outline" 
                className={cn(
                  getRarityColor(item.rarity),
                  size === 'sm' ? 'text-xs px-1 py-0' : 'text-xs'
                )}
              >
                {item.rarity}
              </Badge>
            </div>
            
            <p className={cn(
              'text-gray-600 capitalize',
              size === 'sm' ? 'text-xs' : 'text-xs'
            )}>
              {item.type.replace('_', ' ')}
            </p>
            
            {showDescription && item.description && (
              <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                {item.description}
              </p>
            )}
            
            {showDescription && item.unlock_requirement && (
              <p className="text-xs text-blue-600 mt-1">
                {item.unlock_requirement}
              </p>
            )}
          </div>

          {/* Claim if unclaimed */}
          {(unlockInfo.canUnlock && !isItemUnlocked(item.id)) && (
            <Button
              variant="outline"
              className="h-8 px-4 text-xs"
              onClick={() => onUnlock(item.id)}
            >
              Claim
            </Button>
          )}

          {/* Hover indicator */}
          {clickable && (
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <Eye className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>

        <RewardPreviewModal
          reward={reward}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          playerLevel={playerLevel}
          playerRP={playerRP}
          hasBattlePass={hasBattlePass}
          battlePassTier={battlePassTier}
          isUnlocked={isUnlocked}
          isEquipped={isEquipped}
          unlockInfo={unlockInfo}
          onEquip={reward.item_id && onEquip ? 
            () => onEquip(reward.item_id!, reward.item!.type) : undefined}
          onPreview={reward.item_id && onPreview ?
            () => onPreview(reward.item_id!, reward.item!.type) : undefined}
        />
      </>
    );
  }

  // Fallback for unknown reward types
  return (
    <>
      <RewardPreviewModal
        reward={reward}
        unlockInfo={unlockInfo}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        playerLevel={playerLevel}
        playerRP={playerRP}
        hasBattlePass={hasBattlePass}
        battlePassTier={battlePassTier}
        isUnlocked={isUnlocked}
        isEquipped={isEquipped}
      />
    </>
  );
}