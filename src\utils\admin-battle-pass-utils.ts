// Admin utilities for battle pass management
import { createClient } from "../../supabase/client";

export interface BattlePassSeason {
  id: string;
  season_name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_at: string;
}

export interface BattlePassTier {
  id: string;
  season_id: string;
  tier_number: number;
  xp_required: number;
  free_reward: {
    type: 'item' | 'currency' | 'xp';
    item_id?: string;
    amount?: number;
  } | null;
  premium_reward: {
    type: 'item' | 'currency' | 'xp';
    item_id?: string;
    amount?: number;
  } | null;
  created_at: string;
}

export interface BattlePassStats {
  totalSeasons: number;
  activeSeasons: number;
  totalPlayers: number;
  premiumPlayers: number;
  totalTiers: number;
}

const supabase = createClient();

// Season Management Functions
export async function getBattlePassSeasons(): Promise<BattlePassSeason[]> {
  try {
    const { data, error } = await supabase
      .from('battle_pass_seasons')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching battle pass seasons:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getBattlePassSeasons:', error);
    return [];
  }
}

export async function getBattlePassSeason(seasonId: string): Promise<BattlePassSeason | null> {
  try {
    const { data, error } = await supabase
      .from('battle_pass_seasons')
      .select('*')
      .eq('id', seasonId)
      .single();

    if (error) {
      console.error('Error fetching battle pass season:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getBattlePassSeason:', error);
    return null;
  }
}

export async function createBattlePassSeason(seasonData: {
  season_name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
}): Promise<boolean> {
  try {
    // If setting as active, deactivate all other seasons first
    if (seasonData.is_active) {
      await supabase
        .from('battle_pass_seasons')
        .update({ is_active: false })
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Update all existing seasons
    }

    const { error } = await supabase
      .from('battle_pass_seasons')
      .insert({
        season_name: seasonData.season_name,
        start_date: seasonData.start_date,
        end_date: seasonData.end_date,
        is_active: seasonData.is_active
      });

    if (error) {
      console.error('Error creating battle pass season:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in createBattlePassSeason:', error);
    return false;
  }
}

export async function updateBattlePassSeason(seasonId: string, updates: Partial<BattlePassSeason>): Promise<boolean> {
  try {
    // If setting as active, deactivate all other seasons first
    if (updates.is_active) {
      await supabase
        .from('battle_pass_seasons')
        .update({ is_active: false })
        .neq('id', seasonId);
    }

    const { error } = await supabase
      .from('battle_pass_seasons')
      .update(updates)
      .eq('id', seasonId);

    if (error) {
      console.error('Error updating battle pass season:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in updateBattlePassSeason:', error);
    return false;
  }
}

export async function deleteBattlePassSeason(seasonId: string): Promise<boolean> {
  try {
    // First delete all tiers for this season
    const { error: tiersError } = await supabase
      .from('battle_pass_tiers')
      .delete()
      .eq('season_id', seasonId);

    if (tiersError) {
      console.error('Error deleting season tiers:', tiersError);
      return false;
    }

    // Delete player progress for this season
    const { error: progressError } = await supabase
      .from('player_battle_pass')
      .delete()
      .eq('season_id', seasonId);

    if (progressError) {
      console.error('Error deleting player progress:', progressError);
      // Continue anyway - this isn't critical
    }

    // Finally delete the season itself
    const { error } = await supabase
      .from('battle_pass_seasons')
      .delete()
      .eq('id', seasonId);

    if (error) {
      console.error('Error deleting battle pass season:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in deleteBattlePassSeason:', error);
    return false;
  }
}

// Tier Management Functions
export async function getSeasonTiers(seasonId: string): Promise<BattlePassTier[]> {
  try {
    const { data, error } = await supabase
      .from('battle_pass_tiers')
      .select('*')
      .eq('season_id', seasonId)
      .order('tier_number', { ascending: true });

    if (error) {
      console.error('Error fetching season tiers:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getSeasonTiers:', error);
    return [];
  }
}

export async function createBattlePassTier(seasonId: string, tierData: {
  tier_number: number;
  xp_required: number;
  free_reward: {
    type: 'xp' | 'item' | 'currency' | null;
    amount?: number;
    item_id?: string;
  } | null;
  premium_reward: {
    type: 'xp' | 'item' | 'currency' | null;
    amount?: number;
    item_id?: string;
  } | null;
}): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('battle_pass_tiers')
      .insert({
        season_id: seasonId,
        tier_number: tierData.tier_number,
        xp_required: tierData.xp_required,
        free_reward: tierData.free_reward,
        premium_reward: tierData.premium_reward
      });

    if (error) {
      console.error('Error creating battle pass tier:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in createBattlePassTier:', error);
    return false;
  }
}

export async function updateBattlePassTier(tierId: string, updates: Partial<BattlePassTier>): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('battle_pass_tiers')
      .update(updates)
      .eq('id', tierId);

    if (error) {
      console.error('Error updating battle pass tier:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in updateBattlePassTier:', error);
    return false;
  }
}

export async function deleteBattlePassTier(tierId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('battle_pass_tiers')
      .delete()
      .eq('id', tierId);

    if (error) {
      console.error('Error deleting battle pass tier:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in deleteBattlePassTier:', error);
    return false;
  }
}

export async function reorderTiers(seasonId: string, tierIds: string[]): Promise<boolean> {
  try {
    // Update tier numbers based on the new order
    for (let i = 0; i < tierIds.length; i++) {
      const { error } = await supabase
        .from('battle_pass_tiers')
        .update({ tier_number: i + 1 })
        .eq('id', tierIds[i])
        .eq('season_id', seasonId);

      if (error) {
        console.error('Error reordering tier:', error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Exception in reorderTiers:', error);
    return false;
  }
}

// Statistics Functions
export async function getBattlePassStats(): Promise<BattlePassStats> {
  try {
    const [seasonsData, playersData, tiersData] = await Promise.all([
      supabase.from('battle_pass_seasons').select('is_active'),
      supabase.from('player_battle_pass').select('has_premium'),
      supabase.from('battle_pass_tiers').select('id')
    ]);

    const seasons = seasonsData.data || [];
    const players = playersData.data || [];
    const tiers = tiersData.data || [];

    return {
      totalSeasons: seasons.length,
      activeSeasons: seasons.filter(s => s.is_active).length,
      totalPlayers: players.length,
      premiumPlayers: players.filter(p => p.has_premium).length,
      totalTiers: tiers.length
    };
  } catch (error) {
    console.error('Exception in getBattlePassStats:', error);
    return {
      totalSeasons: 0,
      activeSeasons: 0,
      totalPlayers: 0,
      premiumPlayers: 0,
      totalTiers: 0
    };
  }
}

// Player Management Functions (for future use)
export async function getPlayerBattlePassProgress(playerId: string): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('player_battle_pass')
      .select(`
        *,
        battle_pass_seasons (
          season_name,
          is_active
        )
      `)
      .eq('player_id', playerId);

    if (error) {
      console.error('Error fetching player battle pass progress:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getPlayerBattlePassProgress:', error);
    return null;
  }
}

export async function updatePlayerBattlePassProgress(
  playerId: string, 
  seasonId: string, 
  updates: {
    current_tier?: number;
    current_xp?: number;
    has_premium?: boolean;
  }
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('player_battle_pass')
      .update({
        ...updates,
        last_updated: new Date().toISOString()
      })
      .eq('player_id', playerId)
      .eq('season_id', seasonId);

    if (error) {
      console.error('Error updating player battle pass progress:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in updatePlayerBattlePassProgress:', error);
    return false;
  }
}
