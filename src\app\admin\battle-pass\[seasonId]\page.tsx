'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  Crown, 
  Plus, 
  Edit3, 
  Trash2, 
  Star, 
  Gift, 
  Zap, 
  ArrowLeft,
  Save,
  RotateCcw,
  Move,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { 
  getBattlePassSeason,
  getSeasonTiers,
  createBattlePassTier,
  updateBattlePassTier,
  deleteBattlePassTier,
  reorderTiers,
  BattlePassSeason,
  BattlePassTier
} from '@/lib/admin-battle-pass-utils';
import { Reward, formatRewardText } from '@/lib/item-utils';
import { ItemSelector } from '@/components/item-selector';
import { AVATAR_BORDERS, BACKGROUND_THEMES } from '@/utils/customization-utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface TierFormData {
  tier_number: number;
  xp_required: number;
  free_reward: Reward | null;
  premium_reward: Reward | null;
}

interface SeasonManagementPageProps {
  params: {
    seasonId: string;
  };
}

export default function SeasonManagementPage({ params }: SeasonManagementPageProps) {
  const router = useRouter();
  const { seasonId } = params;
  
  const [season, setSeason] = useState<BattlePassSeason | null>(null);
  const [tiers, setTiers] = useState<BattlePassTier[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  
  // Form state
  const [tierForm, setTierForm] = useState<TierFormData>({
    tier_number: 1,
    xp_required: 0,
    free_reward: null,
    premium_reward: null
  });
  // Helper function to create proper Reward objects
  const createReward = (type: 'xp' | 'item' | 'currency', amount?: number, item_id?: string): Reward => {
    switch (type) {
      case 'xp':
        return { type: 'xp', amount: amount || 0 };
      case 'currency':
        return { type: 'currency', amount: amount || 0 };
      case 'item':
        return { 
          type: 'item', 
          id: item_id || '',
          item_id: item_id,
          item: {
            id: item_id || '',
            name: 'Placeholder Item',
            type: 'avatar',
            rarity: 'common',
            value: '',
            unlock_requirement: null,
            is_default: false,
            description: null,
            created_at: new Date().toISOString(),
            image_url: '',
            is_animated: false
          }
        };
      default:
        return { type: 'xp', amount: 0 };
    }
  };

  useEffect(() => {
    loadSeasonData();
  }, [seasonId]);

  useEffect(() => {
    console.log(tiers);
  }, [tiers])

  const loadSeasonData = async () => {
    try {
      setLoading(true);
      const [seasonData, tiersData] = await Promise.all([
        getBattlePassSeason(seasonId),
        getSeasonTiers(seasonId)
      ]);
      
      setSeason(seasonData);
      setTiers(tiersData);
    } catch (error) {
      console.error('Error loading season data:', error);
      toast.error('Failed to load season data');
    } finally {
      setLoading(false);
    }
  };

  const resetTierForm = () => {
    setTierForm({
      tier_number: Math.max(...tiers.map(t => t.tier_number), 0) + 1,
      xp_required: 0,
      free_reward: null,
      premium_reward: null
    });
  };

  const handleCreateTier = async () => {
    try {
      const success = await createBattlePassTier(seasonId, tierForm);
      if (success) {
        toast.success('Tier created successfully');
        setCreateDialogOpen(false);
        resetTierForm();
        await loadSeasonData();
      } else {
        toast.error('Failed to create tier');
      }
    } catch (error) {
      console.error('Error creating tier:', error);
      toast.error('Failed to create tier');
    }
  };

  const handleUpdateTier = async (tierId: string, updates: Partial<BattlePassTier>) => {
    try {
      const success = await updateBattlePassTier(tierId, updates);
      if (success) {
        toast.success('Tier updated successfully');
        await loadSeasonData();
      } else {
        toast.error('Failed to update tier');
      }
    } catch (error) {
      console.error('Error updating tier:', error);
      toast.error('Failed to update tier');
    }
  };

  const handleDeleteTier = async (tierId: string) => {
    try {
      const success = await deleteBattlePassTier(tierId);
      if (success) {
        toast.success('Tier deleted successfully');
        await loadSeasonData();
      } else {
        toast.error('Failed to delete tier');
      }
    } catch (error) {
      console.error('Error deleting tier:', error);
      toast.error('Failed to delete tier');
    }
  };

  const handleCreateDefault10Tiers = async () => {
    try {
      const defaultTiers = [];
      for (let i = 1; i <= 10; i++) {
        const xp = i === 1 ? 100 : Math.floor(100 * Math.pow(1.3, i - 1));
        defaultTiers.push({
          tier_number: i,
          xp_required: xp,
          free_reward: {
            type: 'xp' as const,
            amount: 50 + (i * 10)
          },
          premium_reward: i % 3 === 0 ? {
            type: 'item' as const,
            item_id: 'placeholder-item-id'
          } : {
            type: 'xp' as const,
            amount: 100 + (i * 20)
          }
        });
      }

      let successCount = 0;
      for (const tier of defaultTiers) {
        const success = await createBattlePassTier(seasonId, tier);
        if (success) successCount++;
      }

      if (successCount === defaultTiers.length) {
        toast.success(`Created ${successCount} default tiers successfully`);
        await loadSeasonData();
      } else {
        toast.warning(`Created ${successCount} out of ${defaultTiers.length} tiers`);
        await loadSeasonData();
      }
    } catch (error) {
      console.error('Error creating default tiers:', error);
      toast.error('Failed to create default tiers');
    }
  };

  const getItemPreviewStyle = (item: any): string => {
    if (!item?.image_url) {
      switch (item?.type) {
        case 'avatar_border':
          return AVATAR_BORDERS[item?.value as keyof typeof AVATAR_BORDERS] || '';
        case 'background':
          return BACKGROUND_THEMES[item?.value as keyof typeof BACKGROUND_THEMES] || '';
        default:
          return '';
      }
    }
    return '';
  };  

  const getRewardIcon = (rewardType: string | undefined, tierReward: any) => {
    
    const item = tierReward?.item;
    
    switch (rewardType) {
      case 'xp': return <Zap className="h-4 w-4 text-blue-600" />;
      case 'item': return (
        <>
          <Gift className="h-4 w-4 text-purple-600" />
          {item?.type === "avatar" && (
            <div className="relative">
                {item?.image_url ? (
                  <Avatar className="h-12 w-12 border-2 border-amber-200">
                    <AvatarImage 
                      src={item?.image_url} 
                      alt={item?.name} 
                    />
                    <AvatarFallback className="bg-amber-200 text-amber-800">
                      {item?.name.substring(0, 2)}
                    </AvatarFallback>
                  </Avatar>
            ) : (
              <div className={cn(
                'relative rounded-full',
                tierReward.item?.value
              )}>
                <Avatar className="h-12 w-12">
                </Avatar>
              </div>
            )}
            </div>
          )}

          {item?.type === "avatar_border" && (
            <div className='relative'>
              {item?.image_url ? (
                <div className="relative">
                  <Avatar className="h-12 w-12">
                  </Avatar>
                  <div 
                    className="absolute inset-0 rounded-full bg-cover bg-center"
                    style={{ backgroundImage: `url(${item?.image_url})` }}
                  />
                </div>
              ) : (
                <div className={cn(
                  'relative rounded-full',
                  getItemPreviewStyle(item)
                )}>
                  <Avatar className="h-12 w-12">
                  </Avatar>
                </div>
              )}
            </div>
          )}

          {item?.type === "background" && (
            <div className='relative'>
              {item?.image_url ? (
                <div 
                  className="w-20 h-12 bg-cover bg-center"
                  style={{ backgroundImage: `url(${item?.image_url})` }}
                >
                </div>
              ) : (
                <div className={cn(
                  'w-full h-full',
                  getItemPreviewStyle(item)
                )} />
              )}
            </div>
          )}

          {item?.type === "title" && (
            <div className="text-center p-2 bg-amber-50 rounded-lg">
              <span className="text-sm font-medium text-amber-900">{item?.value}</span>
            </div>
          )}
        </>
      );
      case 'currency': return <Star className="h-4 w-4 text-amber-600" />;
      default: return <Gift className="h-4 w-4 text-gray-400" />;
    }
  };

  const getRewardText = (reward: any) => {
    if (!reward) return 'No reward';
    return formatRewardText(reward);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
        </div>
      </div>
    );
  }

  if (!season) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Season not found</h3>
            <p className="text-gray-600 mb-4">The requested battle pass season could not be found.</p>
            <Link href="/admin/battle-pass">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Battle Pass
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link href="/admin/battle-pass">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Crown className="h-8 w-8 text-amber-600" />
              <h1 className="text-3xl font-bold text-amber-900">{season.season_name}</h1>
              <Badge className={season.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                {season.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <p className="text-amber-700">
              Manage tiers and rewards for this battle pass season.
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          {tiers.length === 0 && (
            <Button variant="outline" onClick={handleCreateDefault10Tiers}>
              <Plus className="h-4 w-4 mr-2" />
              Create 10 Default Tiers
            </Button>
          )}
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-amber-600 hover:bg-amber-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Tier
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Tier</DialogTitle>
                <DialogDescription>
                  Add a new tier to this battle pass season with custom rewards.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="tier_number">Tier Number</Label>
                    <Input
                      id="tier_number"
                      type="number"
                      value={tierForm.tier_number}
                      onChange={(e) => setTierForm(prev => ({ ...prev, tier_number: parseInt(e.target.value) || 1 }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="xp_required">XP Required</Label>
                    <Input
                      id="xp_required"
                      type="number"
                      value={tierForm.xp_required}
                      onChange={(e) => setTierForm(prev => ({ ...prev, xp_required: parseInt(e.target.value) || 0 }))}
                    />
                  </div>
                </div>                {/* Free Reward */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-blue-900">Free Reward</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label>Reward Type</Label>
                      <Select
                        value={tierForm.free_reward?.type || 'none'}
                        onValueChange={(value) => {
                          if (value === 'none') {
                            setTierForm(prev => ({ ...prev, free_reward: null }));
                          } else if (value === 'xp') {
                            setTierForm(prev => ({ ...prev, free_reward: createReward('xp', 100) }));
                          } else if (value === 'currency') {
                            setTierForm(prev => ({ ...prev, free_reward: createReward('currency', 100) }));
                          } else if (value === 'item') {
                            setTierForm(prev => ({ ...prev, free_reward: createReward('item', undefined, 'placeholder-item-id') }));
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No Reward</SelectItem>
                          <SelectItem value="xp">XP</SelectItem>
                          <SelectItem value="item">Item</SelectItem>
                          <SelectItem value="currency">Currency</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {tierForm.free_reward?.type === 'xp' && (
                      <div>
                        <Label>XP Amount</Label>
                        <Input
                          type="number"
                          value={tierForm.free_reward.amount || 0}
                          onChange={(e) => setTierForm(prev => ({
                            ...prev,
                            free_reward: prev.free_reward ? {
                              ...prev.free_reward,
                              amount: parseInt(e.target.value) || 0
                            } : null
                          }))}
                        />
                      </div>
                    )}
                    {tierForm.free_reward?.type === 'currency' && (
                      <div>
                        <Label>Currency Amount</Label>
                        <Input
                          type="number"
                          value={tierForm.free_reward.amount || 0}
                          onChange={(e) => setTierForm(prev => ({
                            ...prev,
                            free_reward: prev.free_reward ? {
                              ...prev.free_reward,
                              amount: parseInt(e.target.value) || 0
                            } : null
                          }))}
                        />
                      </div>                    )}
                    {tierForm.free_reward?.type === 'item' && (
                      <div className="col-span-2">
                        <Label>Select Item</Label>                        <ItemSelector
                          selectedItemId={tierForm.free_reward.item_id || ''}
                          onSelectItem={(itemId) => setTierForm(prev => ({
                            ...prev,
                            free_reward: prev.free_reward ? {
                              ...prev.free_reward,
                              id: itemId || '',
                              item_id: itemId || ''
                            } : null
                          }))}
                          placeholder="Select an item for free reward"
                        />
                      </div>
                    )}
                  </div>
                </div>                {/* Premium Reward */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-amber-900">Premium Reward</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label>Reward Type</Label>
                      <Select
                        value={tierForm.premium_reward?.type || 'none'}
                        onValueChange={(value) => {
                          if (value === 'none') {
                            setTierForm(prev => ({ ...prev, premium_reward: null }));
                          } else if (value === 'xp') {
                            setTierForm(prev => ({ ...prev, premium_reward: createReward('xp', 200) }));
                          } else if (value === 'currency') {
                            setTierForm(prev => ({ ...prev, premium_reward: createReward('currency', 200) }));
                          } else if (value === 'item') {
                            setTierForm(prev => ({ ...prev, premium_reward: createReward('item', undefined, 'placeholder-premium-item-id') }));
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No Reward</SelectItem>
                          <SelectItem value="xp">XP</SelectItem>
                          <SelectItem value="item">Item</SelectItem>
                          <SelectItem value="currency">Currency</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {tierForm.premium_reward?.type === 'xp' && (
                      <div>
                        <Label>XP Amount</Label>
                        <Input
                          type="number"
                          value={tierForm.premium_reward.amount || 0}
                          onChange={(e) => setTierForm(prev => ({
                            ...prev,
                            premium_reward: prev.premium_reward ? {
                              ...prev.premium_reward,
                              amount: parseInt(e.target.value) || 0
                            } : null
                          }))}
                        />
                      </div>
                    )}
                    {tierForm.premium_reward?.type === 'currency' && (
                      <div>
                        <Label>Currency Amount</Label>
                        <Input
                          type="number"
                          value={tierForm.premium_reward.amount || 0}
                          onChange={(e) => setTierForm(prev => ({
                            ...prev,
                            premium_reward: prev.premium_reward ? {
                              ...prev.premium_reward,
                              amount: parseInt(e.target.value) || 0
                            } : null
                          }))}
                        />
                      </div>                    )}
                    {tierForm.premium_reward?.type === 'item' && (
                      <div className="col-span-2">
                        <Label>Select Item</Label>                        <ItemSelector
                          selectedItemId={tierForm.premium_reward.item_id || ''}
                          onSelectItem={(itemId) => setTierForm(prev => ({
                            ...prev,
                            premium_reward: prev.premium_reward ? {
                              ...prev.premium_reward,
                              id: itemId || '',
                              item_id: itemId || ''
                            } : null
                          }))}
                          placeholder="Select a premium item"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => {
                    resetTierForm();
                  }}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                  <Button onClick={handleCreateTier}>
                    <Save className="h-4 w-4 mr-2" />
                    Create Tier
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Season Info */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">Start Date:</span>
              <p className="text-gray-900">{new Date(season.start_date).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">End Date:</span>
              <p className="text-gray-900">{new Date(season.end_date).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Total Tiers:</span>
              <p className="text-gray-900">{tiers.length}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Status:</span>
              <p className="text-gray-900">{season.is_active ? 'Active' : 'Inactive'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tiers List */}
      <Card>
        <CardHeader>
          <CardTitle>Battle Pass Tiers</CardTitle>
          <CardDescription>
            Manage the tiers and rewards for this battle pass season.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {tiers.length === 0 ? (
            <div className="text-center py-8">
              <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No tiers found</h3>
              <p className="text-gray-600 mb-4">This season doesn't have any tiers yet. Create some to get started.</p>
              <div className="flex justify-center gap-2">
                <Button onClick={handleCreateDefault10Tiers}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create 10 Default Tiers
                </Button>
                <Button variant="outline" onClick={() => setCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Custom Tier
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {tiers.map((tier) => (
                <div
                  key={tier.id}
                  className="border rounded-lg p-4 bg-white hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center font-bold text-amber-800">
                          {tier.tier_number}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">Tier {tier.tier_number}</h3>
                          <p className="text-sm text-gray-600">{tier.xp_required.toLocaleString()} XP required</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Free Reward */}
                        <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                          <div className="p-2 bg-blue-100 rounded">
                            {getRewardIcon(tier.free_reward?.type, tier?.free_reward)}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-blue-900">Free Reward</p>
                            <p className="text-xs text-blue-700">{getRewardText(tier.free_reward)}</p>
                          </div>
                        </div>

                        {/* Premium Reward */}
                        <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-lg">
                          <div className="p-2 bg-amber-100 rounded">
                            {getRewardIcon(tier.premium_reward?.type, tier?.premium_reward)}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-amber-900">Premium Reward</p>
                            <p className="text-xs text-amber-700">{getRewardText(tier.premium_reward)}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                      <div className="flex items-center gap-2 ml-4">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              console.log(tier)
                              setTierForm({
                                tier_number: tier.tier_number,
                                xp_required: tier.xp_required,
                                free_reward: tier.free_reward,
                                premium_reward: tier.premium_reward
                              });
                            }}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Edit Tier {tier.tier_number}</DialogTitle>
                            <DialogDescription>
                              Modify the tier details and rewards.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="edit_tier_number">Tier Number</Label>
                                <Input
                                  id="edit_tier_number"
                                  type="number"
                                  value={tierForm.tier_number}
                                  onChange={(e) => setTierForm(prev => ({ ...prev, tier_number: parseInt(e.target.value) || 1 }))}
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit_xp_required">XP Required</Label>
                                <Input
                                  id="edit_xp_required"
                                  type="number"
                                  value={tierForm.xp_required}
                                  onChange={(e) => setTierForm(prev => ({ ...prev, xp_required: parseInt(e.target.value) || 0 }))}
                                />
                              </div>
                            </div>                            {/* Free Reward Edit */}
                            <div className="space-y-4">
                              <h3 className="font-semibold text-blue-900">Free Reward</h3>
                              <div className="grid grid-cols-3 gap-4">
                                <div>
                                  <Label>Reward Type</Label>
                                  <Select
                                    value={tierForm.free_reward?.type || 'none'}
                                    onValueChange={(value) => {
                                      if (value === 'none') {
                                        setTierForm(prev => ({ ...prev, free_reward: null }));
                                      } else if (value === 'xp') {
                                        setTierForm(prev => ({ ...prev, free_reward: createReward('xp', 100) }));
                                      } else if (value === 'currency') {
                                        setTierForm(prev => ({ ...prev, free_reward: createReward('currency', 100) }));
                                      } else if (value === 'item') {
                                        setTierForm(prev => ({ ...prev, free_reward: createReward('item', undefined, 'placeholder-item-id') }));
                                      }
                                    }}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">No Reward</SelectItem>
                                      <SelectItem value="xp">XP</SelectItem>
                                      <SelectItem value="item">Item</SelectItem>
                                      <SelectItem value="currency">Currency</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                {tierForm.free_reward?.type === 'xp' && (
                                  <div>
                                    <Label>XP Amount</Label>
                                    <Input
                                      type="number"
                                      value={tierForm.free_reward.amount || 0}
                                      onChange={(e) => setTierForm(prev => ({
                                        ...prev,
                                        free_reward: prev.free_reward ? {
                                          ...prev.free_reward,
                                          amount: parseInt(e.target.value) || 0
                                        } : null
                                      }))}
                                    />
                                  </div>
                                )}
                                {tierForm.free_reward?.type === 'currency' && (
                                  <div>
                                    <Label>Currency Amount</Label>
                                    <Input
                                      type="number"
                                      value={tierForm.free_reward.amount || 0}
                                      onChange={(e) => setTierForm(prev => ({
                                        ...prev,
                                        free_reward: prev.free_reward ? {
                                          ...prev.free_reward,
                                          amount: parseInt(e.target.value) || 0
                                        } : null
                                      }))}
                                    />
                                  </div>
                                )}                                
                                {tierForm.free_reward?.type === 'item' && (
                                  <div className="col-span-2">
                                    <Label>Select Item</Label>                                    <ItemSelector
                                      selectedItemId={tierForm?.free_reward?.item_id}
                                      onSelectItem={(itemId) => setTierForm(prev => ({
                                        ...prev,
                                        free_reward: prev.free_reward ? {
                                          ...prev.free_reward,
                                          id: itemId || '',
                                          item_id: itemId || ''
                                        } : null
                                      }))}
                                      placeholder="Select an item for free reward"
                                    />
                                  </div>
                                )}
                              </div>
                            </div>                            
                            {/* Premium Reward Edit */}
                            <div className="space-y-4">
                              <h3 className="font-semibold text-amber-900">Premium Reward</h3>
                              <div className="grid grid-cols-3 gap-4">
                                <div>
                                  <Label>Reward Type</Label>
                                  <Select
                                    value={tierForm.premium_reward?.type || 'none'}
                                    onValueChange={(value) => {
                                      if (value === 'none') {
                                        setTierForm(prev => ({ ...prev, premium_reward: null }));
                                      } else if (value === 'xp') {
                                        setTierForm(prev => ({ ...prev, premium_reward: createReward('xp', 200) }));
                                      } else if (value === 'currency') {
                                        setTierForm(prev => ({ ...prev, premium_reward: createReward('currency', 200) }));
                                      } else if (value === 'item') {
                                        setTierForm(prev => ({ ...prev, premium_reward: createReward('item', undefined, 'placeholder-premium-item-id') }));
                                      }
                                    }}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">No Reward</SelectItem>
                                      <SelectItem value="xp">XP</SelectItem>
                                      <SelectItem value="item">Item</SelectItem>
                                      <SelectItem value="currency">Currency</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                {tierForm.premium_reward?.type === 'xp' && (
                                  <div>
                                    <Label>XP Amount</Label>
                                    <Input
                                      type="number"
                                      value={tierForm.premium_reward.amount || 0}
                                      onChange={(e) => setTierForm(prev => ({
                                        ...prev,
                                        premium_reward: prev.premium_reward ? {
                                          ...prev.premium_reward,
                                          amount: parseInt(e.target.value) || 0
                                        } : null
                                      }))}
                                    />
                                  </div>
                                )}
                                {tierForm.premium_reward?.type === 'currency' && (
                                  <div>
                                    <Label>Currency Amount</Label>
                                    <Input
                                      type="number"
                                      value={tierForm.premium_reward.amount || 0}
                                      onChange={(e) => setTierForm(prev => ({
                                        ...prev,
                                        premium_reward: prev.premium_reward ? {
                                          ...prev.premium_reward,
                                          amount: parseInt(e.target.value) || 0
                                        } : null
                                      }))}
                                    />
                                  </div>
                                )}                                
                                {tierForm.premium_reward?.type === 'item' && (
                                  <div className="col-span-2">
                                    <Label>Select Item</Label>                                    <ItemSelector
                                      selectedItemId={tierForm?.premium_reward?.item_id}
                                      onSelectItem={(itemId) => setTierForm(prev => ({
                                        ...prev,
                                        premium_reward: prev.premium_reward ? {
                                          ...prev.premium_reward,
                                          id: itemId || '',
                                          item_id: itemId || ''
                                        } : null
                                      }))}
                                      placeholder="Select a premium item"
                                    />
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="flex justify-end gap-2">
                              <Button onClick={async () => {                                await handleUpdateTier(tier.id, {
                                  tier_number: tierForm.tier_number,
                                  xp_required: tierForm.xp_required,
                                  free_reward: tierForm.free_reward || undefined,
                                  premium_reward: tierForm.premium_reward || undefined
                                });
                              }}>
                                <Save className="h-4 w-4 mr-2" />
                                Update Tier
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Tier</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete Tier {tier.tier_number}? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction 
                              onClick={() => handleDeleteTier(tier.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
