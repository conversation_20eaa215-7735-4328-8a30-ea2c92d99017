"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { createClient } from "../../supabase/client";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { BookOpen, Zap, Flame, Crown, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { Difficulty } from "@/interfaces/interfaces";
import { joinRoom as joinRoomUtil } from "@/utils/room-utils";

// Define types for room counts
type RoomCounts = {
  easy: number;
  medium: number;
  hard: number;
  "extreme": number;
};

// Define type for match data
type MatchData = {
  id: string;
  difficulty: Difficulty | null;
  match_players: { player_id: string; status: string }[];
};

type RoomCardsProps = {
  initialRoomCounts: RoomCounts;
};

export default function RoomCards({ initialRoomCounts }: RoomCardsProps) {
  const [roomCounts, setRoomCounts] = useState<RoomCounts>(initialRoomCounts);
  const [supabase] = useState(() => createClient());
  const [joiningRoom, setJoiningRoom] = useState<string | null>(null);
  const router = useRouter();
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Room configurations
  const difficultyRooms = [
    {
      name: "Easy",
      icon: BookOpen,
      color: "text-green-500",
      bgColor: "bg-green-100",
    },
    {
      name: "Medium",
      icon: Zap,
      color: "text-blue-500",
      bgColor: "bg-blue-100",
    },
    {
      name: "Hard",
      icon: Flame,
      color: "text-orange-500",
      bgColor: "bg-orange-100",
    },
    {
      name: "Extreme",
      icon: Crown,
      color: "text-purple-500",
      bgColor: "bg-purple-100",
    },
  ];
  // Function to get player counts for each room
  const getRoomPlayerCounts = async (): Promise<RoomCounts> => {
    const { data, error } = await supabase
      .from('matches')
      .select(`
        id,
        difficulty,
        match_players!inner(player_id, status)
      `)
      .in('status', ['waiting', 'ongoing'])
      .eq('match_players.status', 'active');

    if (error) {
      console.error('Error fetching room counts:', error);
      return { ...roomCounts };
    }

    // Process the data to count players per difficulty level
    const newRoomCounts: RoomCounts = {
      easy: 0,
      medium: 0,
      hard: 0,
      "extreme": 0
    };

    (data as MatchData[]).forEach(match => {
      if (!match.difficulty) return;

      const difficulty = match.difficulty.toLowerCase();
      if (difficulty in newRoomCounts) {
        newRoomCounts[difficulty as keyof RoomCounts] += match.match_players.length;
      }
    });

    return newRoomCounts;
  };



  // Function to refresh room counts
  const refreshRoomCounts = async () => {
    try {
      // console.log('Refreshing room counts...');
      const counts = await getRoomPlayerCounts();
      setRoomCounts(counts);
      // console.log('Room counts updated:', counts);
    } catch (error) {
      console.error('Error refreshing room counts:', error);
    }
  };

  // Debounced version of refresh function
  const debouncedRefreshRoomCounts = useCallback(() => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    refreshTimeoutRef.current = setTimeout(() => {
      refreshRoomCounts();
    }, 500); // 500ms debounce
  }, []);

  // Function to join a room
  const joinRoom = async (roomName: string) => {
    setJoiningRoom(roomName);

    const result = await joinRoomUtil(roomName);

    if (result.success && result.redirectPath) {
      // Refresh room counts after successfully joining
      await refreshRoomCounts();
      router.push(result.redirectPath);
    } else if (result.redirectPath) {
      // User is already in a different room
      router.push(result.redirectPath);
    }
  };

  useEffect(() => {
    // Set up realtime subscription to keep counts updated
    const channel = supabase
      .channel('room-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'match_players'
        },
        async (payload) => {
          // console.log('New player added to match:', payload);

          // Check if the match has status 'waiting' before refreshing counts
          if (payload.new && payload.new.match_id) {
            const { data: match, error } = await supabase
              .from('matches')
              .select('status')
              .eq('id', payload.new.match_id)
              .single();

            if (!error && match && match.status === 'waiting') {
              // console.log('Player added to waiting match, refreshing room counts');
              // Use debounced refresh to prevent too many rapid calls
              debouncedRefreshRoomCounts();
            } else {
              console.log('Player added to non-waiting match, skipping refresh');
            }
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'match_players'
        },
        async (payload) => {
          // console.log('Player removed from match:', payload);

          // Check if the match has status 'waiting' before refreshing counts
          if (payload.old && payload.old.match_id) {
            const { data: match, error } = await supabase
              .from('matches')
              .select('status')
              .eq('id', payload.old.match_id)
              .single();

            if (!error && match && match.status === 'waiting') {
              // console.log('Player removed from waiting match, refreshing room counts');
              // Use debounced refresh to prevent too many rapid calls
              debouncedRefreshRoomCounts();
            } else {
              // console.log('Player removed from non-waiting match, skipping refresh');
            }
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'match_players'
        },
        async (payload) => {
          // Check if player status changed to 'left' (soft delete)
          if (payload.new && payload.new.status === 'left' && payload.new.match_id) {
            const { data: match, error } = await supabase
              .from('matches')
              .select('status')
              .eq('id', payload.new.match_id)
              .single();

            if (!error && match && match.status === 'waiting') {
              debouncedRefreshRoomCounts();
            }
          }
        }
      )
      .subscribe();

    // Initial load of room counts
    refreshRoomCounts();

    // Clean up subscription and timeout on unmount
    return () => {
      supabase.removeChannel(channel);
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [supabase, debouncedRefreshRoomCounts]);

  return (
    <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {difficultyRooms.map((room) => {
        // Convert room name to match the keys in RoomCounts
        const roomKey = room.name.toLowerCase() as keyof RoomCounts;
        const userCount = roomCounts[roomKey] || 0;

        return (
          <Card
            key={room.name}
            className="border-2 hover:border-amber-500 transition-all duration-200 hover:shadow-lg cursor-pointer bg-white overflow-hidden"
          >
            <CardHeader className={cn("pb-2", room.bgColor)}>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl font-bold">
                  {room.name}
                </CardTitle>
                <room.icon className={cn("h-6 w-6", room.color)} />
              </div>
              <CardDescription>Spelling challenge</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="flex items-center justify-center h-24 bg-amber-50/50 rounded-lg border border-amber-100">
                <div className="text-center">
                  <p className="text-4xl font-bold text-amber-800">
                    {userCount}
                  </p>
                  <p className="text-xs text-amber-600">players inside</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-center pb-4">
              <Button
                onClick={() => joinRoom(room.name)}
                disabled={joiningRoom !== null} // Disable all buttons when joining any room
                className={cn(
                  "px-4 py-2 text-white rounded-full text-sm font-medium transition-colors",
                  joiningRoom === room.name
                    ? "bg-amber-700" // Highlight the room being joined
                    : joiningRoom !== null
                      ? "bg-gray-400" // Gray out other rooms
                      : "bg-amber-600 hover:bg-amber-700" // Normal state
                )}
              >
                {joiningRoom === room.name ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Joining...
                  </>
                ) : joiningRoom !== null ? (
                  <>
                    <span className="text-xs">Join room</span>
                  </>
                ) : (
                  "Join Room"
                )}
              </Button>
            </CardFooter>
          </Card>
        );
      })}
    </section>
  );
}
