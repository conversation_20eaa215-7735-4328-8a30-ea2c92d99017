import { Suspense } from 'react';
import ReportManagement from '@/components/report-management';
import DashboardNavbar from '@/components/dashboard-navbar';

export default function AdminReportsPage() {
  return (
    <>
      <DashboardNavbar />
      <main className="min-h-screen bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30">
        <Suspense 
          fallback={
            <div className="container mx-auto px-4 py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
                <p>Loading report management...</p>
              </div>
            </div>
          }
        >
          <ReportManagement />
        </Suspense>
      </main>
    </>
  );
}
