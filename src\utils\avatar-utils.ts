/**
 * Avatar utilities for handling avatar border data structures
 */

/**
 * Safely extracts the image URL from avatar border data
 * Handles both single object and array formats from Supabase queries
 */
export function getAvatarBorderImageUrl(avatarBorder: any): string | undefined {
  if (!avatarBorder) return undefined;
  
  // Handle array format (sometimes Supabase returns arrays)
  if (Array.isArray(avatarBorder)) {
    return avatarBorder[0]?.image_url || undefined;
  }
  
  // Handle single object format
  return avatarBorder.image_url || undefined;
}

export function getAvatarImageUrl(avatar: any): string | undefined {
  if (!avatar) return undefined;

  // Handle array format (sometimes Supabase returns arrays)
  if (Array.isArray(avatar)) {
    return avatar[0]?.image_url || undefined;
  }
  
  // Handle single object format
  return avatar.image_url || undefined;
}

/**
 * Type guard to check if avatar border data is valid
 */
export function isValidAvatarBorder(avatarBorder: any): boolean {
  if (!avatarBorder) return false;
  
  if (Array.isArray(avatarBorder)) {
    return avatarBorder.length > 0 && avatarBorder[0]?.image_url;
  }
  
  return typeof avatarBorder === 'object' && avatarBorder.image_url;
}
