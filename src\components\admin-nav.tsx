'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Shield,
  Database,
  Package,
  Crown,
  Users,
  BarChart3,
  Settings,
  Menu,
  X,
  Home,
  ChevronRight,
  MessageSquare,
  Flag
} from 'lucide-react';

const adminRoutes = [
  {
    href: '/admin',
    label: 'Dashboard',
    icon: Home,
    description: 'Admin overview and stats'
  },
  {
    href: '/admin/seed',
    label: 'Database Seeding',
    icon: Database,
    description: 'Initialize default data',
    status: 'active'
  },
  {
    href: '/admin/items',
    label: 'Item Management',
    icon: Package,
    description: 'Manage customization items',
    status: 'active'
  },
  {
    href: '/admin/feedback',
    label: 'Feedback Management',
    icon: MessageSquare,
    description: 'Review user feedback',
    status: 'active'
  },
  {
    href: '/admin/reports',
    label: 'Report Management',
    icon: Flag,
    description: 'Handle player reports',
    status: 'active'
  },
  {
    href: '/admin/battle-pass',
    label: 'Battle Pass Management',
    icon: Crown,
    description: 'Manage battle pass content',
    status: 'active'
  },
  {
    href: '/admin/users',
    label: 'User Management',
    icon: Users,
    description: 'Manage player accounts',
    status: 'coming-soon'
  },
  {
    href: '/admin/analytics',
    label: 'Analytics',
    icon: BarChart3,
    description: 'View game statistics',
    status: 'coming-soon'
  },
  {
    href: '/admin/settings',
    label: 'System Settings',
    icon: Settings,
    description: 'Configure system preferences',
    status: 'coming-soon'
  }
];

interface AdminNavProps {
  className?: string;
}

export default function AdminNav({ className }: AdminNavProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/admin') {
      return pathname === '/admin';
    }
    return pathname.startsWith(href);
  };

  const getStatusBadge = (status?: string) => {
    if (!status || status === 'active') return null;
    
    if (status === 'coming-soon') {
      return (
        <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600">
          Soon
        </Badge>
      );
    }
    
    return null;
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden bg-white/90 backdrop-blur-sm border-b border-amber-200 sticky top-0 z-50">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-amber-600" />
            <span className="font-bold text-amber-900">Word Nook Admin</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Desktop Sidebar */}
      <div className={cn(
        "hidden lg:flex lg:flex-col lg:w-72 lg:fixed lg:inset-y-0 lg:bg-white/90 lg:backdrop-blur-sm lg:border-r lg:border-amber-200",
        className
      )}>
        <div className="flex flex-col flex-1 min-h-0">
          {/* Logo */}
          <div className="flex items-center gap-3 p-6 border-b border-amber-200">
            <Shield className="h-8 w-8 text-amber-600" />
            <div>
              <h1 className="font-bold text-xl text-amber-900">Word Nook</h1>
              <p className="text-sm text-amber-700">Admin Panel</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {adminRoutes.map((route) => {
              const Icon = route.icon;
              const active = isActive(route.href);
              const isDisabled = route.status === 'coming-soon';

              return (
                <div key={route.href}>
                  {isDisabled ? (
                    <div className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-lg text-sm cursor-not-allowed opacity-60",
                      "bg-gray-50 text-gray-600"
                    )}>
                      <Icon className="h-4 w-4" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{route.label}</span>
                          {getStatusBadge(route.status)}
                        </div>
                        <p className="text-xs text-gray-500 truncate">{route.description}</p>
                      </div>
                    </div>
                  ) : (
                    <Link href={route.href}>
                      <div className={cn(
                        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:bg-amber-50",
                        active 
                          ? "bg-amber-100 text-amber-900 border border-amber-200" 
                          : "text-gray-700 hover:text-amber-800"
                      )}>
                        <Icon className={cn("h-4 w-4", active ? "text-amber-600" : "text-gray-500")} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{route.label}</span>
                            {getStatusBadge(route.status)}
                          </div>
                          <p className={cn(
                            "text-xs truncate",
                            active ? "text-amber-700" : "text-gray-500"
                          )}>
                            {route.description}
                          </p>
                        </div>
                        {active && <ChevronRight className="h-4 w-4 text-amber-600" />}
                      </div>
                    </Link>
                  )}
                </div>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-amber-200">
            <div className="text-xs text-gray-500 text-center">
              <p>Word Nook Admin v1.0</p>
              <p>Built with Next.js & Supabase</p>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex">
          <div className="fixed inset-0 bg-black/50" onClick={() => setIsMobileMenuOpen(false)} />
          <div className="relative flex flex-col w-80 max-w-xs bg-white">
            <div className="flex items-center justify-between p-4 border-b border-amber-200">
              <div className="flex items-center gap-2">
                <Shield className="h-6 w-6 text-amber-600" />
                <span className="font-bold text-amber-900">Admin Panel</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
              {adminRoutes.map((route) => {
                const Icon = route.icon;
                const active = isActive(route.href);
                const isDisabled = route.status === 'coming-soon';

                return (
                  <div key={route.href}>
                    {isDisabled ? (
                      <div className={cn(
                        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm cursor-not-allowed opacity-60",
                        "bg-gray-50 text-gray-600"
                      )}>
                        <Icon className="h-4 w-4" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{route.label}</span>
                            {getStatusBadge(route.status)}
                          </div>
                          <p className="text-xs text-gray-500 truncate">{route.description}</p>
                        </div>
                      </div>
                    ) : (
                      <Link href={route.href} onClick={() => setIsMobileMenuOpen(false)}>
                        <div className={cn(
                          "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:bg-amber-50",
                          active 
                            ? "bg-amber-100 text-amber-900 border border-amber-200" 
                            : "text-gray-700 hover:text-amber-800"
                        )}>
                          <Icon className={cn("h-4 w-4", active ? "text-amber-600" : "text-gray-500")} />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{route.label}</span>
                              {getStatusBadge(route.status)}
                            </div>
                            <p className={cn(
                              "text-xs truncate",
                              active ? "text-amber-700" : "text-gray-500"
                            )}>
                              {route.description}
                            </p>
                          </div>
                          {active && <ChevronRight className="h-4 w-4 text-amber-600" />}
                        </div>
                      </Link>
                    )}
                  </div>
                );
              })}
            </nav>
          </div>
        </div>
      )}
    </>
  );
}
