'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { 
  Trophy, 
  Medal, 
  Target, 
  TrendingUp, 
  Clock, 
  Flame, 
  History, 
  Users, 
  BarChart3,
  Star,
  ArrowUp,
  ArrowDown,
  Zap,
  Brain,
  Timer,
  Award,
  Eye,
  RefreshCw
} from "lucide-react";
import { 
  getDetailedMatchHistory, 
  getMatchComparison, 
  formatMatchDuration, 
  getDifficultyColor, 
  getRankDisplay,
  type DetailedMatchHistory,
  type MatchComparison
} from "@/utils/match-details-utils";
import StyledAvatar from "@/components/styled-avatar";
import { getRankInfo } from "@/utils/ranking-system";

interface MatchHistorySectionProps {
  userId: string;
}

export default function MatchHistorySection({ userId }: MatchHistorySectionProps) {
  const [matchHistory, setMatchHistory] = useState<DetailedMatchHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMatch, setSelectedMatch] = useState<DetailedMatchHistory | null>(null);
  const [matchComparison, setMatchComparison] = useState<MatchComparison | null>(null);
  const [showComparison, setShowComparison] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchMatchHistory = async () => {
    try {
      setRefreshing(true);
      const data = await getDetailedMatchHistory(userId, 15);
      setMatchHistory(data);
    } catch (error) {
      console.error('Error fetching detailed match history:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchMatchHistory();
  }, [userId]);

  const handleMatchSelect = async (match: DetailedMatchHistory) => {
    setSelectedMatch(match);
    setShowComparison(false);
    setMatchComparison(null);
  };

  const loadMatchComparison = async () => {
    if (!selectedMatch) return;
    
    try {
      const comparison = await getMatchComparison(selectedMatch.match_id, userId);
      setMatchComparison(comparison);
      setShowComparison(true);
    } catch (error) {
      console.error('Error loading match comparison:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="p-4 rounded-lg border bg-gray-50 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                <div className="space-y-2">
                  <div className="w-32 h-4 bg-gray-300 rounded"></div>
                  <div className="w-24 h-3 bg-gray-300 rounded"></div>
                </div>
              </div>
              <div className="w-20 h-4 bg-gray-300 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (matchHistory.length === 0) {
    return (
      <div className="text-center py-12">
        <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Match History</h3>
        <p className="text-gray-500">Start playing to see your detailed match history here!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={fetchMatchHistory}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Match History List */}
      <div className="space-y-3">
        {matchHistory.map((match, index) => (
          <Dialog key={match.match_id}>
            <DialogTrigger asChild>
              <div 
                className={cn(
                  "p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50",
                  index === 0 ? "ring-2 ring-amber-200 bg-amber-50/50" : "bg-white border-gray-200"
                )}
                onClick={() => handleMatchSelect(match)}
              >                <div className="flex items-start justify-between gap-3">
                  <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {match.user_rank === 1 && <Trophy className="h-5 w-5 text-yellow-500" />}
                      {match.user_rank === 2 && <Medal className="h-5 w-5 text-gray-400" />}
                      {match.user_rank === 3 && <Medal className="h-5 w-5 text-amber-600" />}
                      {match.user_rank && match.user_rank > 3 && <Target className="h-5 w-5 text-gray-500" />}
                      <span className="font-semibold text-lg whitespace-nowrap">
                        {getRankDisplay(match.user_rank || 0)}
                      </span>
                    </div>

                    <div className="flex flex-col min-w-0 flex-1">
                      <div className="flex items-center gap-2 mb-1 flex-wrap">
                        <Badge className={cn("text-xs font-medium capitalize", getDifficultyColor(match.difficulty))}>
                          {match.difficulty}
                        </Badge>
                        <span className="text-sm text-gray-500 whitespace-nowrap">
                          {match.total_players} players
                        </span>
                      </div>

                      <div className="flex items-center gap-2 sm:gap-4 text-sm text-gray-600 flex-wrap">
                        <div className="flex items-center gap-1 whitespace-nowrap">
                          <TrendingUp className="h-3 w-3 flex-shrink-0" />
                          <span>{(match.user_score || 0).toLocaleString()} pts</span>
                        </div>
                        <div className="flex items-center gap-1 whitespace-nowrap">
                          <Clock className="h-3 w-3 flex-shrink-0" />
                          <span>{formatMatchDuration(match.match_duration_seconds)}</span>
                        </div>
                        <div className="flex items-center gap-1 whitespace-nowrap">
                          <BarChart3 className="h-3 w-3 flex-shrink-0" />
                          <span className="hidden sm:inline">Avg: </span>
                          <span className="sm:hidden">A: </span>
                          <span>{match.match_statistics.average_score.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>                  <div className="text-right flex-shrink-0">
                    <Badge variant="outline" className="text-xs mb-1 whitespace-nowrap">
                      <span className="hidden sm:inline">
                        {new Date(match.ended_at).toLocaleDateString()}
                      </span>
                      <span className="sm:hidden">
                        {new Date(match.ended_at).toLocaleDateString(undefined, { 
                          month: 'short', 
                          day: 'numeric'
                        })}
                      </span>
                    </Badge>
                    {index === 0 && (
                      <Badge className="text-xs bg-amber-100 text-amber-800 border-amber-200 block whitespace-nowrap">
                        Latest
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </DialogTrigger>

            <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-amber-600" />
                  Match Details - {getRankDisplay(match.user_rank || 0)}
                  <Badge className={cn("capitalize ml-2", getDifficultyColor(match.difficulty))}>
                    {match.difficulty}
                  </Badge>
                </DialogTitle>
              </DialogHeader>              <Tabs defaultValue="overview" className="w-full">
                <div className="overflow-x-auto">
                    <TabsList className="grid h-auto w-full grid-cols-3 min-w-fit gap-2">
                    <TabsTrigger value="overview" className="flex items-center gap-2 text-xs sm:text-base">Overview</TabsTrigger>
                    <TabsTrigger value="players" className="flex items-center gap-2 text-xs sm:text-base">Players & Scores</TabsTrigger>
                    <TabsTrigger value="analysis" className="flex items-center gap-2 text-xs sm:text-base">
                      <span className="hidden sm:inline">Performance Analysis</span>
                      <span className="sm:hidden">Analysis</span>
                    </TabsTrigger>
                    </TabsList>
                </div>

                <ScrollArea className="h-[60vh] mt-4">
                  <TabsContent value="overview" className="space-y-6">
                    {/* Match Overview Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 bg-amber-50 rounded-lg border border-amber-200">
                        <p className="text-2xl font-bold text-amber-700">{(match.user_score || 0).toLocaleString()}</p>
                        <p className="text-sm text-gray-600">Your Score</p>
                      </div>
                      <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <p className="text-2xl font-bold text-blue-700">{match.user_rank}</p>
                        <p className="text-sm text-gray-600">Your Rank</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-2xl font-bold text-green-700">{match.current_round}</p>
                        <p className="text-sm text-gray-600">Rounds Played</p>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                        <p className="text-2xl font-bold text-purple-700">{match.total_players}</p>
                        <p className="text-sm text-gray-600">Total Players</p>
                      </div>
                    </div>

                    {/* Match Statistics */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <BarChart3 className="h-5 w-5 text-amber-600" />
                          Match Statistics
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Highest Score:</span>
                            <span className="font-medium">{match.match_statistics.highest_score.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Average Score:</span>
                            <span className="font-medium">{match.match_statistics.average_score.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Completion Rate:</span>
                            <span className="font-medium">{match.match_statistics.completion_rate}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Duration:</span>
                            <span className="font-medium">{formatMatchDuration(match.match_duration_seconds)}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Match Info */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg">Match Information</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Date:</span>
                            <span className="font-medium">{new Date(match.ended_at).toLocaleDateString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Time:</span>
                            <span className="font-medium">{new Date(match.ended_at).toLocaleTimeString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Match ID:</span>
                            <span className="font-mono text-xs">{match.match_id.slice(-8)}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                    <TabsContent value="players" className="space-y-4">
                    <Card>
                      <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Users className="h-5 w-5 text-amber-600" />
                        Final Leaderboard
                        <Badge variant="outline" className="text-xs">
                        {match.total_players} Players
                        </Badge>
                      </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6">
                      <div className="space-y-2">
                        {match.players.map((player, index) => {
                        const isCurrentUser = player.player_id === userId;
                        const userRankInfo = getRankInfo(player.rp_after_match || 0);
                        
                        return (
                          <div
                          key={player.player_id}
                          className={cn(
                            "flex items-center justify-between p-2 sm:p-3 rounded-lg border transition-colors",
                            isCurrentUser 
                            ? "bg-amber-50 border-amber-200 ring-1 ring-amber-300" 
                            : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                          )}
                          >
                          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                            {/* Rank Badge */}
                            <div className={cn(
                            "w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0",
                            player.final_rank === 1 ? "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white" :
                            player.final_rank === 2 ? "bg-gradient-to-r from-gray-300 to-gray-400 text-white" :
                            player.final_rank === 3 ? "bg-gradient-to-r from-amber-600 to-amber-700 text-white" :
                            "bg-gray-200 text-gray-700"
                            )}>
                            {player.final_rank}
                            </div>

                            {/* Player Info */}
                            <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                            <div className='relative flex-shrink-0'>
                              <StyledAvatar
                              src={player.equipped_avatar ? player.equipped_avatar : player.avatar_url}
                              alt={player.display_name}
                              fallback={player.display_name.substring(0, 2).toUpperCase()}
                              size="sm"
                              border={player.equipped_avatar_border}
                              totalXP={player.level_experience || 0}
                              />
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                              <span className={cn(
                                "font-medium text-sm sm:text-base truncate",
                                isCurrentUser ? "text-amber-900" : "text-gray-900"
                              )}>
                                {player.display_name}
                              </span>
                              {isCurrentUser && (
                                <Badge variant="outline" className="text-xs px-1 py-0">
                                You
                                </Badge>
                              )}
                              </div>
                              {player.elimination_round && (
                              <p className="text-xs text-gray-500 truncate">
                                Eliminated Round {player.elimination_round}
                              </p>
                              )}
                            </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0 ml-2">
                            {/* Score */}
                            <div className="text-right">
                            <p className="font-bold text-sm sm:text-lg">
                              {player.score.toLocaleString()}
                            </p>
                            <p className="text-xs text-gray-500">points</p>
                            </div>

                            {/* RP Change */}
                            {player.rp_gained !== undefined && (
                            <div className="text-right min-w-[50px] sm:min-w-[60px]">
                              <div className={cn(
                              "flex items-center justify-end gap-1 text-xs sm:text-sm font-medium",
                              player.rp_gained > 0 ? "text-green-600" : 
                              player.rp_gained < 0 ? "text-red-600" : "text-gray-600"
                              )}>
                              {player.rp_gained > 0 && <ArrowUp className="h-3 w-3" />}
                              {player.rp_gained < 0 && <ArrowDown className="h-3 w-3" />}
                              <span className="whitespace-nowrap">
                                {player.rp_gained > 0 ? '+' : ''}{player.rp_gained}
                              </span>
                              </div>
                              <p className="text-xs text-gray-500">RP</p>
                            </div>
                            )}
                          </div>
                          </div>
                        );
                        })}
                      </div>
                      </CardContent>
                    </Card>
                    </TabsContent>

                  <TabsContent value="analysis" className="space-y-4">
                    <div className="flex justify-center mb-4">
                      <Button 
                        onClick={loadMatchComparison}
                        disabled={!selectedMatch}
                        className="gap-2"
                      >
                        <Brain className="h-4 w-4" />
                        Analyze Performance
                      </Button>
                    </div>

                    {showComparison && matchComparison && (
                      <div className="space-y-4">
                        {/* Performance Rating */}
                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Star className="h-5 w-5 text-amber-600" />
                              Performance Rating
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="text-center p-4 bg-gradient-to-b from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                                <div className={cn(
                                  "text-2xl font-bold mb-1",
                                  matchComparison.vs_average.performance_rating === 'Excellent' ? "text-green-600" :
                                  matchComparison.vs_average.performance_rating === 'Good' ? "text-blue-600" :
                                  matchComparison.vs_average.performance_rating === 'Average' ? "text-yellow-600" :
                                  matchComparison.vs_average.performance_rating === 'Below Average' ? "text-orange-600" :
                                  "text-red-600"
                                )}>
                                  {matchComparison.vs_average.performance_rating}
                                </div>
                                <p className="text-sm text-gray-600">Overall Rating</p>
                              </div>
                              
                              <div className="text-center p-4 bg-gradient-to-b from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                                <div className="text-2xl font-bold text-purple-600 mb-1">
                                  {matchComparison.vs_average.rank_percentile}%
                                </div>
                                <p className="text-sm text-gray-600">Percentile Rank</p>
                              </div>

                              <div className="text-center p-4 bg-gradient-to-b from-amber-50 to-amber-100 rounded-lg border border-amber-200">
                                <div className={cn(
                                  "text-2xl font-bold mb-1",
                                  matchComparison.vs_average.score_difference >= 0 ? "text-green-600" : "text-red-600"
                                )}>
                                  {matchComparison.vs_average.score_difference >= 0 ? '+' : ''}
                                  {matchComparison.vs_average.score_difference}
                                </div>
                                <p className="text-sm text-gray-600">vs Average</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        {/* Performance Insights */}
                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Brain className="h-5 w-5 text-amber-600" />
                              Performance Insights
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {matchComparison.player_insights.map((insight, index) => (
                                <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
                                    <span className="text-xs font-bold text-amber-600">{index + 1}</span>
                                  </div>
                                  <p className="text-sm text-gray-700 flex-1">{insight}</p>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        {/* Comparison with Winner */}
                        {matchComparison.vs_best_player.rank_difference > 0 && (
                          <Card>
                            <CardHeader className="pb-3">
                              <CardTitle className="text-lg flex items-center gap-2">
                                <Trophy className="h-5 w-5 text-amber-600" />
                                Gap to Winner
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="grid grid-cols-2 gap-4">
                                <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                                  <div className="text-2xl font-bold text-red-600 mb-1">
                                    -{matchComparison.vs_best_player.score_gap}
                                  </div>
                                  <p className="text-sm text-gray-600">Score Gap</p>
                                </div>
                                
                                <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                                  <div className="text-2xl font-bold text-blue-600 mb-1">
                                    {matchComparison.vs_best_player.rank_difference}
                                  </div>
                                  <p className="text-sm text-gray-600">Rank Positions</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    )}
                  </TabsContent>
                </ScrollArea>
              </Tabs>
            </DialogContent>
          </Dialog>
        ))}
      </div>
    </div>
  );
}
