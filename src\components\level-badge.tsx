'use client'

import { cn } from '@/lib/utils';
// Simple function to calculate level from XP
function getLevelFromXP(xp: number): number {
  if (xp < 50) return 1;
  if (xp < 150) return 2;
  if (xp < 300) return 3;
  if (xp < 500) return 4;
  if (xp < 750) return 5;
  if (xp < 1050) return 6;
  if (xp < 1400) return 7;
  if (xp < 1800) return 8;
  if (xp < 2250) return 9;
  if (xp < 2750) return 10;

  // For levels above 10, use exponential progression
  let level = 10;
  let requiredXP = 2750;
  let increment = 350;

  while (xp >= requiredXP + increment) {
    level++;
    requiredXP += increment;
    increment = Math.floor(increment * 1.15); // 15% increase each level
  }

  return level;
}

interface LevelBadgeProps {
  totalXP: number;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
}

export default function LevelBadge({
  totalXP,
  size = 'md',
  className
}: LevelBadgeProps) {
  const level = getLevelFromXP(totalXP);
  
  // Don't show level badge for level 1
  if (level <= 1) return null;

  const sizeClasses = {
    xs: 'text-[8px] min-w-[14px] h-[14px] px-1',
    sm: 'text-[9px] min-w-[16px] h-[16px] px-1',
    md: 'text-[10px] min-w-[18px] h-[18px] px-1.5',
    lg: 'text-[11px] min-w-[20px] h-[20px] px-2'
  };

  const getLevelColor = (level: number) => {
    if (level >= 50) return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white'; // Legendary
    if (level >= 25) return 'bg-gradient-to-r from-purple-400 to-pink-500 text-white'; // Epic
    if (level >= 15) return 'bg-gradient-to-r from-blue-400 to-cyan-500 text-white'; // Rare
    if (level >= 5) return 'bg-gradient-to-r from-green-400 to-emerald-500 text-white'; // Uncommon
    return 'bg-gradient-to-r from-gray-400 to-gray-500 text-white'; // Common
  };

  return (
    <div className={cn(
      'absolute -bottom-1 -right-1 rounded-full flex items-center justify-center font-bold shadow-sm border border-white/50',
      sizeClasses[size],
      getLevelColor(level),
      className
    )}>
      {level}
    </div>
  );
}
