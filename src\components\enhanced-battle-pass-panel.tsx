'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Lock, Check, Crown, Star, Gift, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '../../supabase/client';
import {
  BattlePassProgress,
  BattlePassTier,
  getCompleteBattlePassProgress,
  upgradeToPremiumBattlePass
} from '@/utils/battle-pass-utils';
import { 
  equipItem, 
  unlockItem, 
  hasUnlockedItem,
  getPlayerInventory,
  type PlayerItem
} from '@/utils/customization-utils';
import { RewardDisplay } from '@/components/reward-display';
import { RewardPreviewModal } from '@/components/reward-preview-modal';

interface EnhancedBattlePassPanelProps {
  playerId: string;
  className?: string;
}

interface PlayerData {
  id: string;
  level_experience: number;
  rankpoints: number;
  equipped_items: {
    avatar?: string;
    avatar_border?: string;
    background?: string;
    title?: string;
  };
}

export default function EnhancedBattlePassPanel({ playerId, className }: EnhancedBattlePassPanelProps) {
  const [battlePassData, setBattlePassData] = useState<BattlePassProgress | null>(null);
  const [playerData, setPlayerData] = useState<PlayerData | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState(false);
  const [unlockedItems, setUnlockedItems] = useState<Set<string>>(new Set());
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [selectedReward, setSelectedReward] = useState<any>(null);
  useEffect(() => {
    loadBattlePassData();
    loadPlayerData();
    loadUnlockedItems();
    
    // Set up real-time subscription for unlocked items
    const supabase = createClient();
    const subscription = supabase
      .channel('player_inventory_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'player_item_inventory',
          filter: `player_id=eq.${playerId}`
        },
        (payload) => {
          console.log('Inventory change detected:', payload);
          handleInventoryChange(payload);
        }
      )
      .subscribe();

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [playerId]);

  const loadPlayerData = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('players')
        .select(`
          id,
          level_experience,
          rankpoints,
          equipped_avatar_id,
          equipped_avatar_border_id,
          equipped_background_id,
          equipped_title_id
        `)
        .eq('id', playerId)
        .single();

      if (error) {
        console.error('Error fetching player data:', error);
        return;
      }

      setPlayerData({
        id: data.id,
        level_experience: data.level_experience || 0,
        rankpoints: data.rankpoints || 0,
        equipped_items: {
          avatar: data.equipped_avatar_id,
          avatar_border: data.equipped_avatar_border_id,
          background: data.equipped_background_id,
          title: data.equipped_title_id,
        }
      });
    } catch (error) {
      console.error('Error loading player data:', error);
    }
  };
  const loadUnlockedItems = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('player_item_inventory')
        .select('item_id')
        .eq('player_id', playerId);

      if (error) {
        console.error('Error loading unlocked items:', error);
        return;
      }

      const itemIds = new Set(data?.map(item => item.item_id) || []);
      setUnlockedItems(itemIds);
    } catch (error) {
      console.error('Error loading unlocked items:', error);
    }
  };

  const handleInventoryChange = (payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    switch (eventType) {
      case 'INSERT':
        // Item was unlocked
        if (newRecord?.item_id) {
          setUnlockedItems(prev => {
            const newSet = new Set(prev);
            newSet.add(newRecord.item_id);
            return newSet;
          });
          console.log('Item unlocked via real-time:', newRecord.item_id);
        }
        break;
        
      case 'DELETE':
        // Item was removed (rare case)
        if (oldRecord?.item_id) {
          setUnlockedItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(oldRecord.item_id);
            return newSet;
          });
          console.log('Item removed via real-time:', oldRecord.item_id);
        }
        break;
        
      case 'UPDATE':
        // Handle any updates if needed
        break;
        
      default:
        console.log('Unknown event type:', eventType);
    }
  };

  const loadBattlePassData = async () => {
    setLoading(true);
    try {
      const data = await getCompleteBattlePassProgress(playerId);
      setBattlePassData(data);
    } catch (error) {
      console.error('Error loading battle pass data:', error);
      toast.error('Failed to load battle pass data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeToPremium = async () => {
    if (!battlePassData) return;
    
    setUpgrading(true);
    try {
      const success = await upgradeToPremiumBattlePass(playerId);
      if (success) {
        toast.success('Upgraded to Premium Battle Pass!');
        await loadBattlePassData();
      } else {
        toast.error('Failed to upgrade to premium');
      }
    } catch (error) {
      console.error('Error upgrading to premium:', error);
      toast.error('Failed to upgrade to premium');
    } finally {
      setUpgrading(false);
    }
  };

  const handleEquipItem = async (itemId: string, itemType: string) => {
    try {
      const success = await equipItem(playerId, itemId, itemType as any);
      if (success) {
        toast.success('Item equipped successfully!');
        await loadPlayerData();
      } else {
        toast.error('Failed to equip item');
      }
    } catch (error) {
      console.error('Error equipping item:', error);
      toast.error('Failed to equip item');
    }
  };
  const handleUnlockItem = async (itemId: string) => {
    try {
      // Optimistically update the UI immediately
      setUnlockedItems(prev => {
        const newSet = new Set(prev);
        newSet.add(itemId);
        return newSet;
      });

      const success = await unlockItem(playerId, itemId);
      if (success) {
        toast.success('Item unlocked successfully!');
        // The real-time subscription will handle the actual update
        // but we keep the optimistic update for immediate feedback
      } else {
        // Revert optimistic update on failure
        setUnlockedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
        toast.error('Failed to unlock item');
      }
    } catch (error) {
      // Revert optimistic update on error
      setUnlockedItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
      console.error('Error unlocking item:', error);
      toast.error('Failed to unlock item');
    }
  };

  const handlePreviewItem = (itemId: string, itemType: string) => {
    // This would integrate with a preview system
    console.log('Preview item:', itemId, itemType);
  };

  const handleRewardClick = (reward: any, isUnlocked: boolean) => {
    if (reward && reward.type === 'item') {
      setSelectedReward({
        ...reward,
        isUnlocked: isUnlocked,
        isEquipped: playerData?.equipped_items && reward.item_id ? 
          Object.values(playerData.equipped_items).includes(reward.item_id) : false
      });
      setShowRewardModal(true);
    }
  };

  const isItemUnlocked = (itemId: string): boolean => {
    return unlockedItems.has(itemId);
  };

  const isItemEquipped = (itemId: string): boolean => {
    if (!playerData) return false;
    const equippedItems = playerData.equipped_items;
    return Object.values(equippedItems).includes(itemId);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-700"></div>
        </CardContent>
      </Card>
    );
  }

  if (!battlePassData) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">No active battle pass season</p>
        </CardContent>
      </Card>
    );
  }

  const { season, playerProgress, tiers, currentTierInfo, nextTierInfo, xpToNextTier, progressPercentage } = battlePassData;

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-amber-600" />
                {season.season_name}
              </CardTitle>
              <CardDescription>
                Battle Pass • Tier {playerProgress.current_tier} / {tiers.length - 1}
              </CardDescription>
            </div>
            {!playerProgress.has_premium && (
              <Button
                onClick={handleUpgradeToPremium}
                disabled={upgrading}
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
              >
                {upgrading ? 'Upgrading...' : 'Upgrade'}
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Current Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current XP: {playerProgress.current_xp.toLocaleString()}</span>
              {nextTierInfo && (
                <span>Next Tier: {xpToNextTier.toLocaleString()} XP</span>
              )}
            </div>
            <Progress 
              value={progressPercentage} 
              className="h-3"
            />
          </div>

          {/* Tier Rewards */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Tier Rewards</h3>
            <div className="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">
              {tiers.map((tier) => {
                const isUnlocked = playerProgress.current_tier >= tier.tier_number;
                const isCurrent = playerProgress.current_tier === tier.tier_number;
                
                return (
                  <div
                    key={tier.id}
                    className={cn(
                      'flex items-center gap-4 p-3 rounded-lg border',
                      isCurrent && 'ring-2 ring-amber-500 bg-amber-50',
                      isUnlocked && !isCurrent && 'bg-green-50 border-green-200',
                      !isUnlocked && 'bg-gray-50 border-gray-200'
                    )}
                  >
                    {/* Tier Number */}
                    <div className={cn(
                      'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                      isCurrent && 'bg-amber-500 text-white',
                      isUnlocked && !isCurrent && 'bg-green-500 text-white',
                      !isUnlocked && 'bg-gray-300 text-gray-600'
                    )}>
                      {isUnlocked ? <Check size={14} /> : tier.tier_number}
                    </div>

                    {/* Rewards */}
                    <div className="flex-1 grid grid-cols-1 gap-2">
                      {/* Free Reward */}
                      <div
                        className="cursor-pointer"
                        onClick={() => handleRewardClick(tier.free_reward, isUnlocked)}
                      >
                        <RewardDisplay 
                          tier={tier}
                          reward={tier.free_reward}
                          size="sm"
                          showDescription={false}
                          className="bg-blue-50 border-blue-200 hover:bg-blue-100 transition-colors"
                          clickable={false}
                          playerLevel={playerData ? Math.floor(Math.sqrt(playerData.level_experience / 50)) + 1 : 1}
                          playerRP={playerData?.rankpoints || 0}
                          hasBattlePass={playerProgress.has_premium}
                          battlePassTier={playerProgress.current_tier}
                          isUnlocked={isUnlocked}
                          onUnlock={(itemId) => handleUnlockItem(itemId)}
                          isItemUnlocked={(itemId) => isItemUnlocked(itemId)}
                        />
                      </div>

                      {/* Premium Reward */}
                      {playerProgress.has_premium ? (
                        <div
                          className="cursor-pointer"
                          onClick={() => handleRewardClick(tier.premium_reward, isUnlocked)}
                        >                        
                        <RewardDisplay 
                          tier={tier}
                          reward={tier.premium_reward}
                          isPremium={true}
                          size="sm"
                          showDescription={false}
                          className="bg-amber-50 border-amber-200 hover:bg-amber-100 transition-colors"
                          clickable={false}
                          playerLevel={playerData ? Math.floor(Math.sqrt(playerData.level_experience / 50)) + 1 : 1}
                          playerRP={playerData?.rankpoints || 0}
                          hasBattlePass={playerProgress.has_premium}
                          battlePassTier={playerProgress.current_tier}
                          isUnlocked={isUnlocked}
                          onUnlock={(itemId) => handleUnlockItem(itemId)}
                          isItemUnlocked={(itemId) => isItemUnlocked(itemId)}
                        />
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg border">
                          <div className="flex-1">
                            <div className='flex gap-2 mb-1'>
                              <p className="text-xs font-medium text-gray-500">Premium</p>
                              <Lock size={16} className="text-gray-400" />
                            </div>
                            <div
                              className="cursor-pointer"
                              onClick={() => handleRewardClick(tier.premium_reward, false)}
                            >                              
                            <RewardDisplay 
                                tier={tier}
                                reward={tier.premium_reward}
                                isPremium={true}
                                size="sm"
                                showDescription={false}
                                className="bg-amber-50 border-amber-200 hover:bg-amber-100 transition-colors"
                                clickable={false}
                                playerLevel={playerData ? Math.floor(Math.sqrt(playerData.level_experience / 50)) + 1 : 1}
                                playerRP={playerData?.rankpoints || 0}
                                hasBattlePass={playerProgress.has_premium}
                                battlePassTier={playerProgress.current_tier}
                                isUnlocked={false}
                                onUnlock={(itemId) => handleUnlockItem(itemId)}
                                isItemUnlocked={(itemId) => isItemUnlocked(itemId)}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* XP Required */}
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {tier.xp_required.toLocaleString()} XP
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Season Info */}
          <div className="pt-4 border-t border-gray-200">
            <div className="flex justify-between text-xs text-gray-500">
              <span>Season ends: {new Date(season.end_date).toLocaleDateString()}</span>
              <span>{playerProgress.has_premium ? 'Premium' : 'Free'} Pass</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Reward Preview Modal */}
      {selectedReward && (
        <RewardPreviewModal
          reward={selectedReward}
          isOpen={showRewardModal}
          showRequirements={false}
          onClose={() => {
            setShowRewardModal(false);
            setSelectedReward(null);
          }}
          playerLevel={playerData ? Math.floor(Math.sqrt(playerData.level_experience / 50)) + 1 : 1}
          playerRP={playerData?.rankpoints || 0}
          hasBattlePass={playerProgress.has_premium}
          battlePassTier={playerProgress.current_tier}
          isUnlocked={selectedReward.isUnlocked}
          isEquipped={selectedReward.isEquipped}
          onEquip={selectedReward.item_id ? 
            () => handleEquipItem(selectedReward.item_id, selectedReward.item.type) : undefined}
          onPreview={selectedReward.item_id ? 
            () => handlePreviewItem(selectedReward.item_id, selectedReward.item.type) : undefined}
        />
      )}
    </>
  );
}
