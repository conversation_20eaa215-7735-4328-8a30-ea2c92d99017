import { createClient } from '../../supabase/client';
import { getFeedbackStats } from './admin-feedback-utils';
import { getPendingReports } from './report-utils';

export interface AdminStats {
  feedback: {
    total: number;
    pending: number;
    critical: number;
  };
  reports: {
    total: number;
    pending: number;
    resolved: number;
  };
  players: {
    total: number;
    active_today: number;
    restricted: number;
  };
  matches: {
    total_today: number;
    active: number;
    completed_today: number;
  };
  items: {
    total: number;
    avatars: number;
    borders: number;
    backgrounds: number;
  };
  battlePass: {
    total_seasons: number;
    active_seasons: number;
    total_players: number;
    premium_players: number;
  };
}

/**
 * Get comprehensive admin statistics for dashboard
 */
export async function getAdminStats(): Promise<AdminStats> {
  const supabase = createClient();
  
  try {
    // Get feedback stats
    const feedbackStats = await getFeedbackStats();    // Get report stats
    const [reports, pendingReports] = await Promise.all([
      supabase.from('player_reports').select('status, created_at'),
      getPendingReports()
    ]);

    const reportData = reports.data || [];
    const reportStats = {
      total: reportData.length,
      pending: pendingReports.length,
      resolved: reportData.filter(r => r.status === 'resolved').length,
    };

    // Get player stats
    const { data: players } = await supabase
      .from('players')
      .select('created_at, id');

    const { data: restrictions } = await supabase
      .from('active_player_restrictions')
      .select('id');

    const playerStats = {
      total: players?.length || 0,
      active_today: players?.filter(p => {
        const createdAt = new Date(p.created_at);
        const today = new Date();
        return createdAt.toDateString() === today.toDateString();
      }).length || 0,
      restricted: restrictions?.length || 0,
    };

    // Get match stats
    const today = new Date().toISOString().split('T')[0];
    const { data: matches } = await supabase
      .from('matches')
      .select('status, created_at');

    const { data: activeMatches } = await supabase
      .from('matches')
      .select('id')
      .in('status', ['waiting', 'ongoing']);

    const matchData = matches || [];
    const matchStats = {
      total_today: matchData.filter(m => m.created_at.startsWith(today)).length,
      active: activeMatches?.length || 0,
      completed_today: matchData.filter(m => 
        m.created_at.startsWith(today) && m.status === 'finished'
      ).length,
    };    // Get item stats
    const { data: items } = await supabase
      .from('player_items')
      .select('type');

    const itemData = items || [];
    const itemStats = {
      total: itemData.length,
      avatars: itemData.filter(i => i.type === 'avatar').length,
      borders: itemData.filter(i => i.type === 'avatar_border').length,
      backgrounds: itemData.filter(i => i.type === 'background').length,
    };

    // Get battle pass stats
    const [seasonsData, battlePassPlayersData] = await Promise.all([
      supabase.from('battle_pass_seasons').select('is_active'),
      supabase.from('player_battle_pass').select('has_premium')
    ]);

    const seasons = seasonsData.data || [];
    const battlePassPlayers = battlePassPlayersData.data || [];
    
    const battlePassStats = {
      total_seasons: seasons.length,
      active_seasons: seasons.filter(s => s.is_active).length,
      total_players: battlePassPlayers.length,
      premium_players: battlePassPlayers.filter(p => p.has_premium).length,
    };

    return {
      feedback: {
        total: feedbackStats.total,
        pending: feedbackStats.pending,
        critical: feedbackStats.critical,
      },
      reports: reportStats,
      players: playerStats,
      matches: matchStats,
      items: itemStats,
      battlePass: battlePassStats,
    };

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return getDefaultAdminStats();
  }
}

/**
 * Get recent admin activity for dashboard
 */
export async function getRecentActivity(limit = 10): Promise<Array<{
  id: string;
  type: 'feedback' | 'report' | 'player_join' | 'match' | 'item';
  title: string;
  description: string;
  timestamp: string;
  status?: string;
  priority?: string;
}>> {
  const supabase = createClient();
  
  try {
    const activities: Array<{
      id: string;
      type: 'feedback' | 'report' | 'player_join' | 'match' | 'item';
      title: string;
      description: string;
      timestamp: string;
      status?: string;
      priority?: string;
    }> = [];

    // Get recent feedback
    const { data: recentFeedback } = await supabase
      .from('feedback')
      .select('id, title, category, status, priority, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    recentFeedback?.forEach(f => {
      activities.push({
        id: f.id,
        type: 'feedback',
        title: `New ${f.category.replace('_', ' ')} feedback`,
        description: f.title,
        timestamp: f.created_at,
        status: f.status,
        priority: f.priority,
      });
    });

    // Get recent reports
    const { data: recentReports } = await supabase
      .from('player_reports')
      .select('id, category, reason, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    recentReports?.forEach(r => {
      activities.push({
        id: r.id,
        type: 'report',
        title: `New ${r.category.replace('_', ' ')} report`,
        description: r.reason,
        timestamp: r.created_at,
        status: r.status,
      });
    });

    // Get recent player joins
    const { data: recentPlayers } = await supabase
      .from('players')
      .select('id, display_name, created_at')
      .order('created_at', { ascending: false })
      .limit(3);

    recentPlayers?.forEach(p => {
      activities.push({
        id: p.id,
        type: 'player_join',
        title: 'New player joined',
        description: `${p.display_name} created an account`,
        timestamp: p.created_at,
      });
    });

    // Sort by timestamp and limit
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);

  } catch (error) {
    console.error('Error fetching recent activity:', error);
    return [];
  }
}

/**
 * Get system health metrics
 */
export async function getSystemHealth(): Promise<{
  database: 'healthy' | 'warning' | 'error';
  storage: 'healthy' | 'warning' | 'error';
  auth: 'healthy' | 'warning' | 'error';
  overall: 'healthy' | 'warning' | 'error';
}> {
  const supabase = createClient();
  
  try {
    // Test database connection
    const { error: dbError } = await supabase
      .from('players')
      .select('id')
      .limit(1);    // Test auth
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    const dbHealth = dbError ? 'error' : 'healthy';
    const authHealth = authError ? 'error' : 'healthy';
    const storageHealth = 'healthy'; // Assume healthy for now

    let overall: 'healthy' | 'warning' | 'error' = 'healthy';
    if (dbHealth === 'error' || authHealth === 'error') {
      overall = 'error';
    }

    return {
      database: dbHealth,
      storage: storageHealth,
      auth: authHealth,
      overall,
    };

  } catch (error) {
    console.error('Error checking system health:', error);
    return {
      database: 'error',
      storage: 'error',
      auth: 'error',
      overall: 'error',
    };
  }
}

function getDefaultAdminStats(): AdminStats {
  return {
    feedback: {
      total: 0,
      pending: 0,
      critical: 0,
    },
    reports: {
      total: 0,
      pending: 0,
      resolved: 0,
    },
    players: {
      total: 0,
      active_today: 0,
      restricted: 0,
    },
    matches: {
      total_today: 0,      active: 0,
      completed_today: 0,
    },
    items: {
      total: 0,
      avatars: 0,
      borders: 0,
      backgrounds: 0,
    },
    battlePass: {
      total_seasons: 0,
      active_seasons: 0,
      total_players: 0,
      premium_players: 0,
    },
  };
}
