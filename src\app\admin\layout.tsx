import { Metadata } from 'next';
import AdminNav from '@/components/admin-nav';

export const metadata: Metadata = {
  title: 'Admin Panel - Word Nook',
  description: 'Administrative interface for Word Nook game management',
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-amber-100">
      <AdminNav />
      
      {/* Main Content */}
      <div className="lg:pl-72">
        <main className="min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
}
