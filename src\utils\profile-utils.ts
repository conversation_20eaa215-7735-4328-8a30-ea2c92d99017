import { createClient } from "../../supabase/client";

export interface MatchHistoryEntry {
  match_id: string;
  difficulty: string;
  final_rank: number;
  score: number;
  elimination_round: number | null;
  ended_at: string;
  current_round: number;
  match_duration_seconds: number;
  total_players: number;
  player_names: string[];
}

export interface PlayerStats {
  total_matches: number;
  wins: number;
  win_rate: number;
  highest_score: number;
  longest_streak: number;
  total_score: number;
  avg_score: number;
  favorite_difficulty: string;
  total_eliminations: number;
  survival_rate: number;
}

export interface AvatarOption {
  id: string;
  name: string;
  url: string;
  locked: boolean;
  unlock_condition?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface BackgroundOption {
  id: string;
  name: string;
  url: string;
  preview: string;
  locked: boolean;
  unlock_condition?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface BorderOption {
  id: string;
  name: string;
  style: string;
  preview: string;
  locked: boolean;
  unlock_condition?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * Get user's match history with detailed information
 */
export async function getUserMatchHistory(userId: string, limit: number = 20): Promise<MatchHistoryEntry[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('match_history_players')
      .select(`
        match_id,
        score,
        final_rank,
        elimination_round,
        difficulty,
        created_at,
        match_histories (
          current_round,
          match_duration_seconds,
          ended_at,
          player_ids
        )
      `)
      .eq('player_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching match history:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Get player names for each match
    const matchHistoryWithNames = await Promise.all(
      data.map(async (match: any) => {
        const playerIds = match.match_histories?.player_ids || [];
        
        // Get player names
        const { data: playersData } = await supabase
          .from('players')
          .select('id, display_name')
          .in('id', playerIds);

        const playerNames = playersData?.map(p => p.display_name) || [];

        return {
          match_id: match.match_id,
          difficulty: match.difficulty,
          final_rank: match.final_rank || 0,
          score: match.score || 0,
          elimination_round: match.elimination_round,
          ended_at: match.match_histories?.ended_at || match.created_at,
          current_round: match.match_histories?.current_round || 1,
          match_duration_seconds: match.match_histories?.match_duration_seconds || 0,
          total_players: playerIds.length,
          player_names: playerNames
        };
      })
    );

    return matchHistoryWithNames;

  } catch (error) {
    console.error('Exception in getUserMatchHistory:', error);
    return [];
  }
}

/**
 * Get comprehensive player statistics
 */
export async function getPlayerStats(userId: string): Promise<PlayerStats> {
  const supabase = createClient();

  try {
    // Get match history data
    const { data: matchData, error } = await supabase
      .from('match_history_players')
      .select('score, final_rank, difficulty, elimination_round')
      .eq('player_id', userId);

    if (error || !matchData) {
      return {
        total_matches: 0,
        wins: 0,
        win_rate: 0,
        highest_score: 0,
        longest_streak: 0,
        total_score: 0,
        avg_score: 0,
        favorite_difficulty: 'easy',
        total_eliminations: 0,
        survival_rate: 0
      };
    }

    const totalMatches = matchData.length;
    const wins = matchData.filter(m => m.final_rank === 1).length;
    const scores = matchData.map(m => m.score || 0);
    const totalScore = scores.reduce((a, b) => a + b, 0);
    const eliminations = matchData.filter(m => m.elimination_round && m.elimination_round > 0).length;

    // Calculate favorite difficulty
    const difficultyCount = matchData.reduce((acc: any, match) => {
      acc[match.difficulty] = (acc[match.difficulty] || 0) + 1;
      return acc;
    }, {});    const favoriteDifficulty = Object.keys(difficultyCount).length === 0
      ? 'easy'
      : Object.keys(difficultyCount).reduce((a, b) => 
          difficultyCount[a] > difficultyCount[b] ? a : b
        );

    // Get longest streak from user_stats
    const { data: userStats } = await supabase
      .from('user_stats')
      .select('longest_streak')
      .eq('user_id', userId)
      .order('longest_streak', { ascending: false })
      .limit(1)
      .single();

    return {
      total_matches: totalMatches,
      wins,
      win_rate: totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0,
      highest_score: Math.max(...scores, 0),
      longest_streak: userStats?.longest_streak || 0,
      total_score: totalScore,
      avg_score: totalMatches > 0 ? Math.round(totalScore / totalMatches) : 0,
      favorite_difficulty: favoriteDifficulty,
      total_eliminations: eliminations,
      survival_rate: totalMatches > 0 ? Math.round(((totalMatches - eliminations) / totalMatches) * 100) : 0
    };

  } catch (error) {
    console.error('Exception in getPlayerStats:', error);
    return {
      total_matches: 0,
      wins: 0,
      win_rate: 0,
      highest_score: 0,
      longest_streak: 0,
      total_score: 0,
      avg_score: 0,
      favorite_difficulty: 'easy',
      total_eliminations: 0,
      survival_rate: 0
    };
  }
}

/**
 * Get available avatar options with unlock status
 * Using DiceBear API for consistent, themed avatars
 */
export function getAvatarOptions(userLevel: number = 1): AvatarOption[] {
  const avatars: AvatarOption[] = [
    // Common avatars (always unlocked)
    { id: 'default', name: 'Default', url: '', locked: false, rarity: 'common' },
    { id: 'scholar', name: 'Scholar', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Scholar&backgroundColor=f3f4f6', locked: false, rarity: 'common' },
    { id: 'bookworm', name: 'Bookworm', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Bookworm&backgroundColor=fef3c7', locked: false, rarity: 'common' },
    { id: 'student', name: 'Student', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Student&backgroundColor=dbeafe', locked: false, rarity: 'common' },

    // Rare avatars (unlock at level 5)
    { id: 'wizard', name: 'Word Wizard', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Wizard&backgroundColor=e0e7ff', locked: userLevel < 5, unlock_condition: 'Reach Level 5', rarity: 'rare' },
    { id: 'sage', name: 'Spelling Sage', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sage&backgroundColor=ddd6fe', locked: userLevel < 5, unlock_condition: 'Reach Level 5', rarity: 'rare' },
    { id: 'professor', name: 'Professor', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Professor&backgroundColor=f0f9ff', locked: userLevel < 8, unlock_condition: 'Reach Level 8', rarity: 'rare' },

    // Epic avatars (unlock with achievements)
    { id: 'champion', name: 'Champion', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Champion&backgroundColor=fdf4ff', locked: userLevel < 15, unlock_condition: 'Win 10 matches', rarity: 'epic' },
    { id: 'master', name: 'Spell Master', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=SpellMaster&backgroundColor=f3e8ff', locked: userLevel < 20, unlock_condition: 'Reach Level 20', rarity: 'epic' },
    { id: 'oracle', name: 'Word Oracle', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Oracle&backgroundColor=ecfdf5', locked: userLevel < 25, unlock_condition: 'Perfect streak of 15', rarity: 'epic' },

    // Legendary avatars (special achievements)
    { id: 'legend', name: 'Legend', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Legend&backgroundColor=fffbeb', locked: userLevel < 50, unlock_condition: 'Reach Level 50', rarity: 'legendary' },
    { id: 'grandmaster', name: 'Grandmaster', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Grandmaster&backgroundColor=fef7cd', locked: userLevel < 100, unlock_condition: 'Win 100 matches', rarity: 'legendary' }
  ];

  return avatars;
}

/**
 * Update user's selected avatar
 */
export async function updateUserAvatar(userId: string, avatarId: string): Promise<boolean> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('players')
      .update({ avatar_url: avatarId })
      .eq('id', userId);

    if (error) {
      console.error('Error updating avatar:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in updateUserAvatar:', error);
    return false;
  }
}

/**
 * Format match duration for display
 */
export function formatMatchDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
}

/**
 * Get rank display with emoji
 */
export function getRankDisplay(rank: number): string {
  switch (rank) {
    case 1: return '🥇 1st';
    case 2: return '🥈 2nd';
    case 3: return '🥉 3rd';
    default: return `#${rank}`;
  }
}

/**
 * Get difficulty color class
 */
export function getDifficultyColor(difficulty: string): string {
  switch (difficulty.toLowerCase()) {
    case 'easy': return 'text-green-600 bg-green-100';
    case 'medium': return 'text-blue-600 bg-blue-100';
    case 'hard': return 'text-orange-600 bg-orange-100';
    case 'extreme': return 'text-purple-600 bg-purple-100';
    default: return 'text-gray-600 bg-gray-100';
  }
}

/**
 * Get available background options with unlock status
 */
export function getBackgroundOptions(userLevel: number = 1): BackgroundOption[] {
  const backgrounds: BackgroundOption[] = [
    // Common backgrounds (always unlocked)
    {
      id: 'default',
      name: 'Default',
      url: 'bg-gradient-to-r from-amber-100 to-amber-50',
      preview: 'linear-gradient(to right, #fef3c7, #fffbeb)',
      locked: false,
      rarity: 'common'
    },
    {
      id: 'ocean',
      name: 'Ocean Waves',
      url: 'bg-gradient-to-r from-blue-100 to-cyan-50',
      preview: 'linear-gradient(to right, #dbeafe, #ecfeff)',
      locked: false,
      rarity: 'common'
    },
    {
      id: 'forest',
      name: 'Forest Green',
      url: 'bg-gradient-to-r from-green-100 to-emerald-50',
      preview: 'linear-gradient(to right, #dcfce7, #ecfdf5)',
      locked: false,
      rarity: 'common'
    },

    // Rare backgrounds (unlock at level 5)
    {
      id: 'sunset',
      name: 'Sunset Glow',
      url: 'bg-gradient-to-r from-orange-200 to-pink-100',
      preview: 'linear-gradient(to right, #fed7aa, #fce7f3)',
      locked: userLevel < 5,
      unlock_condition: 'Reach Level 5',
      rarity: 'rare'
    },
    {
      id: 'purple',
      name: 'Royal Purple',
      url: 'bg-gradient-to-r from-purple-200 to-indigo-100',
      preview: 'linear-gradient(to right, #e9d5ff, #e0e7ff)',
      locked: userLevel < 5,
      unlock_condition: 'Reach Level 5',
      rarity: 'rare'
    },

    // Epic backgrounds (unlock with achievements)
    {
      id: 'galaxy',
      name: 'Galaxy',
      url: 'bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-900',
      preview: 'linear-gradient(to right, #581c87, #1e3a8a, #312e81)',
      locked: userLevel < 15,
      unlock_condition: 'Win 10 matches',
      rarity: 'epic'
    },
    {
      id: 'fire',
      name: 'Phoenix Fire',
      url: 'bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500',
      preview: 'linear-gradient(to right, #ef4444, #f97316, #eab308)',
      locked: userLevel < 20,
      unlock_condition: 'Reach Level 20',
      rarity: 'epic'
    },

    // Legendary backgrounds (special achievements)
    {
      id: 'rainbow',
      name: 'Rainbow Prism',
      url: 'bg-gradient-to-r from-pink-500 via-purple-500 via-blue-500 via-green-500 to-yellow-500',
      preview: 'linear-gradient(to right, #ec4899, #a855f7, #3b82f6, #10b981, #eab308)',
      locked: userLevel < 50,
      unlock_condition: 'Reach Level 50',
      rarity: 'legendary'
    },
    {
      id: 'diamond',
      name: 'Diamond Shine',
      url: 'bg-gradient-to-r from-gray-200 via-white to-gray-200',
      preview: 'linear-gradient(to right, #e5e7eb, #ffffff, #e5e7eb)',
      locked: userLevel < 100,
      unlock_condition: 'Win 100 matches',
      rarity: 'legendary'
    }
  ];

  return backgrounds;
}

/**
 * Get available border options with unlock status
 */
export function getBorderOptions(userLevel: number = 1): BorderOption[] {
  const borders: BorderOption[] = [
    // Common borders (always unlocked)
    {
      id: 'default',
      name: 'Default',
      style: 'border-2 border-white',
      preview: '2px solid #ffffff',
      locked: false,
      rarity: 'common'
    },
    {
      id: 'gold',
      name: 'Gold Ring',
      style: 'border-4 border-yellow-400',
      preview: '4px solid #facc15',
      locked: false,
      rarity: 'common'
    },
    {
      id: 'silver',
      name: 'Silver Ring',
      style: 'border-4 border-gray-300',
      preview: '4px solid #d1d5db',
      locked: false,
      rarity: 'common'
    },

    // Rare borders (unlock at level 5)
    {
      id: 'blue',
      name: 'Sapphire',
      style: 'border-4 border-blue-500 shadow-lg shadow-blue-500/50',
      preview: '4px solid #3b82f6 with blue glow',
      locked: userLevel < 5,
      unlock_condition: 'Reach Level 5',
      rarity: 'rare'
    },
    {
      id: 'purple',
      name: 'Amethyst',
      style: 'border-4 border-purple-500 shadow-lg shadow-purple-500/50',
      preview: '4px solid #a855f7 with purple glow',
      locked: userLevel < 5,
      unlock_condition: 'Reach Level 5',
      rarity: 'rare'
    },

    // Epic borders (unlock with achievements)
    {
      id: 'fire',
      name: 'Fire Aura',
      style: 'border-4 border-red-500 shadow-lg shadow-red-500/50 animate-pulse',
      preview: '4px solid #ef4444 with fire animation',
      locked: userLevel < 15,
      unlock_condition: 'Win 10 matches',
      rarity: 'epic'
    },
    {
      id: 'rainbow',
      name: 'Rainbow Glow',
      style: 'border-4 border-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 p-1',
      preview: 'Rainbow gradient border',
      locked: userLevel < 25,
      unlock_condition: 'Perfect streak of 15',
      rarity: 'epic'
    },

    // Legendary borders (special achievements)
    {
      id: 'diamond',
      name: 'Diamond Crown',
      style: 'border-8 border-transparent bg-gradient-to-r from-gray-300 via-white to-gray-300 p-2 shadow-2xl',
      preview: 'Diamond crown border',
      locked: userLevel < 50,
      unlock_condition: 'Reach Level 50',
      rarity: 'legendary'
    },
    {
      id: 'legendary',
      name: 'Legendary Aura',
      style: 'border-8 border-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 p-2 shadow-2xl animate-pulse',
      preview: 'Legendary animated border',
      locked: userLevel < 100,
      unlock_condition: 'Win 100 matches',
      rarity: 'legendary'
    }
  ];

  return borders;
}

/**
 * Update user's profile customization
 */
export async function updateUserProfile(
  userId: string,
  updates: {
    display_name?: string;
    title?: string;
    background_display?: string;
    avatar_border?: string;
  }
): Promise<boolean> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('players')
      .update(updates)
      .eq('id', userId);

    if (error) {
      console.error('Error updating user profile:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception in updateUserProfile:', error);
    return false;
  }
}
