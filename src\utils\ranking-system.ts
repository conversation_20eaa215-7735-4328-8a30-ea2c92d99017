import { createClient } from "../../supabase/client";

export interface RankInfo {
  tier: string;
  division: string;
  rp: number;
  rpForCurrentRank: number;
  rpForNextRank: number;
  rpToNextRank: number;
  progressPercentage: number;
  tierOrder: number;
  divisionOrder: number;
}

export interface RPGain {
  baseRP: number;
  rankBonus: number;
  performanceBonus: number;
  durationBonus: number;
  difficultyMultiplier: number;
  totalRP: number;
  breakdown: string[];
}

// Difficulty multipliers for RP gain/loss
const DIFFICULTY_MULTIPLIERS = {
  easy: 1.0,
  medium: 1.2,
  hard: 1.5,
  extreme: 2.0
};

/**
 * Get difficulty multiplier for a given difficulty level
 * @param difficulty - The difficulty level ('easy', 'medium', 'hard', 'extreme')
 * @param multipliers - The multipliers object to use
 * @returns The multiplier value or 1.0 as fallback
 */
export function getDifficultyMultiplier(
  difficulty: string, 
  multipliers: Record<string, number> = DIFFICULTY_MULTIPLIERS
): number {
  return multipliers[difficulty as keyof typeof multipliers] || 1.0;
}

// Rank tier definitions
export const RANK_TIERS = {
  Parchment: { min: 0, max: 999, color: 'from-gray-400 to-gray-600', textColor: 'text-gray-700' },
  Quill: { min: 1000, max: 1999, color: 'from-green-400 to-green-600', textColor: 'text-green-700' },
  Lexicon: { min: 2000, max: 2999, color: 'from-blue-400 to-blue-600', textColor: 'text-blue-700' },
  Sage: { min: 3000, max: 3999, color: 'from-purple-400 to-purple-600', textColor: 'text-purple-700' },
  Luminari: { min: 4000, max: Infinity, color: 'from-yellow-400 to-orange-500', textColor: 'text-yellow-700' }
};

// Division definitions (except Luminari)
export const DIVISIONS = ['IV', 'III', 'II', 'I'];

/**
 * Calculate rank information from RP
 */
export function getRankInfo(rp: number): RankInfo {
  let tier = 'Parchment';
  let division = 'IV';
  let tierOrder = 1;
  let divisionOrder = 1;

  // Determine tier
  if (rp >= 4000) {
    tier = 'Luminari';
    division = '';
    tierOrder = 5;
    divisionOrder = 5;
  } else if (rp >= 3000) {
    tier = 'Sage';
    tierOrder = 4;
  } else if (rp >= 2000) {
    tier = 'Lexicon';
    tierOrder = 3;
  } else if (rp >= 1000) {
    tier = 'Quill';
    tierOrder = 2;
  } else {
    tier = 'Parchment';
    tierOrder = 1;
  }

  // Determine division (except for Luminari)
  if (tier !== 'Luminari') {
    const tierInfo = RANK_TIERS[tier as keyof typeof RANK_TIERS];
    const tierRange = tierInfo.max - tierInfo.min + 1;
    const divisionSize = tierRange / 4;
    const rpInTier = rp - tierInfo.min;
    
    if (rpInTier >= divisionSize * 3) {
      division = 'I';
      divisionOrder = 4;
    } else if (rpInTier >= divisionSize * 2) {
      division = 'II';
      divisionOrder = 3;
    } else if (rpInTier >= divisionSize) {
      division = 'III';
      divisionOrder = 2;
    } else {
      division = 'IV';
      divisionOrder = 1;
    }
  }

  // Calculate progression
  let rpForCurrentRank = 0;
  let rpForNextRank = 0;
  let rpToNextRank = 0;
  let progressPercentage = 0;

  if (tier === 'Luminari') {
    rpForCurrentRank = 4000;
    rpForNextRank = 4000; // No next rank
    rpToNextRank = 0;
    progressPercentage = 100;
  } else {
    const tierInfo = RANK_TIERS[tier as keyof typeof RANK_TIERS];
    const divisionSize = (tierInfo.max - tierInfo.min + 1) / 4;
    const divisionIndex = DIVISIONS.indexOf(division);
    
    rpForCurrentRank = tierInfo.min + (divisionIndex * divisionSize);
    
    if (division === 'I' && tier !== 'Sage') {
      // Next tier
      const nextTierName = Object.keys(RANK_TIERS)[tierOrder] as keyof typeof RANK_TIERS;
      rpForNextRank = RANK_TIERS[nextTierName].min;
    } else if (division === 'I' && tier === 'Sage') {
      rpForNextRank = 4000; // Luminari
    } else {
      // Next division
      rpForNextRank = rpForCurrentRank + divisionSize;
    }
    
    rpToNextRank = Math.max(0, rpForNextRank - rp);
    progressPercentage = Math.min(100, ((rp - rpForCurrentRank) / (rpForNextRank - rpForCurrentRank)) * 100);
  }

  return {
    tier,
    division,
    rp,
    rpForCurrentRank,
    rpForNextRank,
    rpToNextRank,
    progressPercentage,
    tierOrder,
    divisionOrder
  };
}

/**
 * Calculate RP gain/loss for a match result
 */
export function calculateRPChange(
  playerRank: number,
  finalRank: number,
  totalPlayers: number,
  matchDurationMinutes: number,
  difficulty: string,
  playerScore: number,
  averageScore: number
): RPGain {
  const difficultyMultiplier = getDifficultyMultiplier(difficulty, DIFFICULTY_MULTIPLIERS);
  
  // Base RP calculation
  let baseRP = 0;
  const rankPercentile = (totalPlayers - finalRank + 1) / totalPlayers;
  
  if (rankPercentile >= 0.5) {
    // Top 50% - gain RP
    baseRP = Math.round(20 + (rankPercentile - 0.5) * 60); // 20-50 base RP
  } else {
    // Bottom 50% - lose RP
    baseRP = Math.round(-10 - (0.5 - rankPercentile) * 30); // -10 to -25 base RP
  }

  // Rank bonus (higher ranked players gain less, lose more)
  const currentRankInfo = getRankInfo(playerRank);
  let rankBonus = 0;
  if (currentRankInfo.tierOrder >= 4) { // Sage and Luminari
    rankBonus = baseRP > 0 ? -5 : -5; // Gain less, lose more
  } else if (currentRankInfo.tierOrder >= 3) { // Lexicon
    rankBonus = baseRP > 0 ? -2 : -2;
  }

  // Performance bonus based on score relative to average
  let performanceBonus = 0;
  if (averageScore > 0) {
    const scoreRatio = playerScore / averageScore;
    if (scoreRatio >= 1.5) {
      performanceBonus = 10;
    } else if (scoreRatio >= 1.2) {
      performanceBonus = 5;
    } else if (scoreRatio <= 0.5) {
      performanceBonus = -5;
    } else if (scoreRatio <= 0.8) {
      performanceBonus = -2;
    }
  }

  // Duration bonus (longer matches = more RP)
  let durationBonus = 0;
  if (matchDurationMinutes >= 15) {
    durationBonus = 5;
  } else if (matchDurationMinutes >= 10) {
    durationBonus = 3;
  } else if (matchDurationMinutes >= 5) {
    durationBonus = 1;
  }

  // Calculate total RP
  const subtotal = baseRP + rankBonus + performanceBonus + durationBonus;
  const totalRP = Math.round(subtotal * difficultyMultiplier);

  // Create breakdown
  const breakdown: string[] = [];
  breakdown.push(`Base RP: ${baseRP > 0 ? '+' : ''}${baseRP}`);
  if (rankBonus !== 0) breakdown.push(`Rank Adjustment: ${rankBonus > 0 ? '+' : ''}${rankBonus}`);
  if (performanceBonus !== 0) breakdown.push(`Performance: ${performanceBonus > 0 ? '+' : ''}${performanceBonus}`);
  if (durationBonus !== 0) breakdown.push(`Duration: +${durationBonus}`);
  if (difficultyMultiplier !== 1.0) breakdown.push(`${difficulty} Multiplier: x${difficultyMultiplier}`);

  return {
    baseRP,
    rankBonus,
    performanceBonus,
    durationBonus,
    difficultyMultiplier,
    totalRP,
    breakdown
  };
}

/**
 * Update player's RP and rank in database
 */
export async function updatePlayerRP(
  playerId: string,
  rpChange: number,
  matchId?: string,
  reason: string = 'match_result'
): Promise<boolean> {
  const supabase = createClient();

  try {
    // Get current player data
    const { data: player, error: fetchError } = await supabase
      .from('players')
      .select('rankpoints, highest_rankpoints, current_rank_tier, current_rank_division')
      .eq('id', playerId)
      .single();

    if (fetchError || !player) {
      console.error('Error fetching player data:', fetchError);
      return false;
    }

    const oldRP = player.rankpoints || 0;
    const newRP = Math.max(0, oldRP + rpChange); // RP can't go below 0
    
    const oldRankInfo = getRankInfo(oldRP);
    const newRankInfo = getRankInfo(newRP);

    // Update player's RP and rank
    const { error: updateError } = await supabase
      .from('players')
      .update({
        rankpoints: newRP,
        highest_rankpoints: Math.max(player.highest_rankpoints || 0, newRP),
        current_rank_tier: newRankInfo.tier,
        current_rank_division: newRankInfo.division,
        rank_updated_at: new Date().toISOString()
      })
      .eq('id', playerId);

    if (updateError) {
      console.error('Error updating player RP:', updateError);
      return false;
    }

    // Record rank history
    const { error: historyError } = await supabase
      .from('rank_history')
      .insert({
        player_id: playerId,
        match_id: matchId,
        rp_change: rpChange,
        rp_before: oldRP,
        rp_after: newRP,
        rank_before: oldRankInfo.division ? `${oldRankInfo.tier} ${oldRankInfo.division}` : oldRankInfo.tier,
        rank_after: newRankInfo.division ? `${newRankInfo.tier} ${newRankInfo.division}` : newRankInfo.tier,
        reason
      });

    if (historyError) {
      console.error('Error recording rank history:', historyError);
      // Don't fail the whole operation for history recording
    }

    return true;
  } catch (error) {
    console.error('Exception in updatePlayerRP:', error);
    return false;
  }
}

/**
 * Get ranking leaderboard
 */
export async function getRankingLeaderboard(limit: number = 50) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from('players')
      .select(`
        id,
        avatar_url,
        display_name, 
        equipped_avatar_border_id, 
        rankpoints, 
        highest_rankpoints, 
        current_rank_tier, 
        current_rank_division, 
        rank_updated_at, 
        level_experience,
        avatar_border:equipped_avatar_border_id(image_url),
        equipped_avatar_id,
        avatar_items:equipped_avatar_id(image_url)
        `)
      .gt('rankpoints', 0)
      .order('rankpoints', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching ranking leaderboard:', error);
      return [];
    }

    return data?.map(player => ({
      ...player,
      rankInfo: getRankInfo(player.rankpoints || 0)
    })) || [];

  } catch (error) {
    console.error('Exception in getRankingLeaderboard:', error);
    return [];
  }
}

/**
 * Format RP number for display
 */
export function formatRP(rp: number): string {
  if (rp >= 1000000) {
    return `${(rp / 1000000).toFixed(1)}M RP`;
  }
  if (rp >= 1000) {
    return `${(rp / 1000).toFixed(1)}K RP`;
  }
  return `${rp} RP`;
}

/**
 * Get rank display string
 */
export function getRankDisplayString(rankInfo: RankInfo): string {
  if (rankInfo.tier === 'Luminari') {
    return 'Luminari';
  }
  return `${rankInfo.tier} ${rankInfo.division}`;
}

/**
 * Get rank color classes
 */
export function getRankColorClasses(tier: string): { gradient: string; text: string } {
  const tierInfo = RANK_TIERS[tier as keyof typeof RANK_TIERS];
  return {
    gradient: tierInfo?.color || 'from-gray-400 to-gray-600',
    text: tierInfo?.textColor || 'text-gray-700'
  };
}
