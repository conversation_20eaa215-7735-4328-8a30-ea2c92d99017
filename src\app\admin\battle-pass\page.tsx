'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  Crown, 
  Plus, 
  Edit3, 
  Trash2, 
  Calendar, 
  Users, 
  Star, 
  Settings, 
  Eye, 
  EyeOff,
  Zap,
  ArrowLeft,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { 
  getBattlePassSeasons,
  createBattlePassSeason,
  updateBattlePassSeason,
  deleteBattlePassSeason,
  getBattlePassStats,
  BattlePassSeason 
} from '@/lib/admin-battle-pass-utils';

interface BattlePassStats {
  totalSeasons: number;
  activeSeasons: number;
  totalPlayers: number;
  premiumPlayers: number;
  totalTiers: number;
}

export default function AdminBattlePassPage() {
  const [seasons, setSeasons] = useState<BattlePassSeason[]>([]);
  const [stats, setStats] = useState<BattlePassStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editingSeasonId, setEditingSeasonId] = useState<string | null>(null);

  // Form state
  const [seasonForm, setSeasonForm] = useState({
    season_name: '',
    start_date: '',
    end_date: '',
    is_active: false
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [seasonsData, statsData] = await Promise.all([
        getBattlePassSeasons(),
        getBattlePassStats()
      ]);
      
      setSeasons(seasonsData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading battle pass data:', error);
      toast.error('Failed to load battle pass data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSeason = async () => {
    try {
      const success = await createBattlePassSeason({
        season_name: seasonForm.season_name,
        start_date: seasonForm.start_date,
        end_date: seasonForm.end_date,
        is_active: seasonForm.is_active
      });

      if (success) {
        toast.success('Battle pass season created successfully');
        setCreateDialogOpen(false);
        setSeasonForm({
          season_name: '',
          start_date: '',
          end_date: '',
          is_active: false
        });
        await loadData();
      } else {
        toast.error('Failed to create battle pass season');
      }
    } catch (error) {
      console.error('Error creating season:', error);
      toast.error('Failed to create battle pass season');
    }
  };

  const handleUpdateSeason = async (seasonId: string, updates: Partial<BattlePassSeason>) => {
    try {
      const success = await updateBattlePassSeason(seasonId, updates);
      if (success) {
        toast.success('Season updated successfully');
        await loadData();
      } else {
        toast.error('Failed to update season');
      }
    } catch (error) {
      console.error('Error updating season:', error);
      toast.error('Failed to update season');
    }
  };

  const handleDeleteSeason = async (seasonId: string) => {
    try {
      const success = await deleteBattlePassSeason(seasonId);
      if (success) {
        toast.success('Season deleted successfully');
        await loadData();
      } else {
        toast.error('Failed to delete season');
      }
    } catch (error) {
      console.error('Error deleting season:', error);
      toast.error('Failed to delete season');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const isSeasonActive = (season: BattlePassSeason) => {
    const now = new Date();
    const start = new Date(season.start_date);
    const end = new Date(season.end_date);
    return season.is_active && now >= start && now <= end;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link href="/admin">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Admin
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Crown className="h-8 w-8 text-amber-600" />
              <h1 className="text-3xl font-bold text-amber-900">Battle Pass Management</h1>
            </div>
            <p className="text-amber-700">
              Manage battle pass seasons, tiers, and rewards.
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href="/admin/battle-pass/analytics">
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
          </Link>
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-amber-600 hover:bg-amber-700">
                <Plus className="h-4 w-4 mr-2" />
                Create Season
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Battle Pass Season</DialogTitle>
                <DialogDescription>
                  Create a new battle pass season with custom duration and settings.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="season_name">Season Name</Label>
                  <Input
                    id="season_name"
                    value={seasonForm.season_name}
                    onChange={(e) => setSeasonForm(prev => ({ ...prev, season_name: e.target.value }))}
                    placeholder="Season 2: The Scholar's Quest"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start_date">Start Date</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={seasonForm.start_date}
                      onChange={(e) => setSeasonForm(prev => ({ ...prev, start_date: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="end_date">End Date</Label>
                    <Input
                      id="end_date"
                      type="date"
                      value={seasonForm.end_date}
                      onChange={(e) => setSeasonForm(prev => ({ ...prev, end_date: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={seasonForm.is_active}
                    onChange={(e) => setSeasonForm(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="is_active">Set as active season</Label>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateSeason}>
                    Create Season
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Seasons</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalSeasons}</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Seasons</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeSeasons}</p>
                </div>
                <Crown className="h-8 w-8 text-amber-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Players</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPlayers}</p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Premium Players</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.premiumPlayers}</p>
                  <p className="text-xs text-gray-500">
                    {stats.totalPlayers > 0 ? Math.round((stats.premiumPlayers / stats.totalPlayers) * 100) : 0}% conversion
                  </p>
                </div>
                <Star className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Tiers</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalTiers}</p>
                </div>
                <Zap className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Seasons List */}
      <Card>
        <CardHeader>
          <CardTitle>Battle Pass Seasons</CardTitle>
          <CardDescription>
            Manage all battle pass seasons and their configurations.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
            </div>
          ) : seasons.length === 0 ? (
            <div className="text-center py-8">
              <Crown className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No seasons found</h3>
              <p className="text-gray-600 mb-4">Get started by creating your first battle pass season.</p>
              <Button onClick={() => setCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Season
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {seasons.map((season) => {
                const isActive = isSeasonActive(season);
                
                return (
                  <div
                    key={season.id}
                    className={`border rounded-lg p-4 transition-all ${
                      isActive 
                        ? 'border-amber-300 bg-amber-50' 
                        : season.is_active 
                          ? 'border-blue-300 bg-blue-50' 
                          : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {season.season_name}
                          </h3>
                          <div className="flex gap-2">
                            {isActive && (
                              <Badge className="bg-green-100 text-green-800">
                                Active
                              </Badge>
                            )}
                            {season.is_active && !isActive && (
                              <Badge variant="outline">
                                Scheduled
                              </Badge>
                            )}
                            {!season.is_active && (
                              <Badge variant="secondary">
                                Inactive
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Start:</span> {formatDate(season.start_date)}
                          </div>
                          <div>
                            <span className="font-medium">End:</span> {formatDate(season.end_date)}
                          </div>
                          <div>
                            <span className="font-medium">Created:</span> {formatDate(season.created_at)}
                          </div>
                          <div>
                            <span className="font-medium">Status:</span> {season.is_active ? 'Active' : 'Inactive'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          title={season.is_active ? 'Deactivate' : 'Activate'}
                          onClick={() => handleUpdateSeason(season.id, { is_active: !season.is_active })}
                        >
                          {season.is_active ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                        <Link href={`/admin/battle-pass/${season.id}`}>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4 mr-2" />
                            Manage Tiers
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditingSeasonId(season.id);
                            setSeasonForm({
                              season_name: season.season_name,
                              start_date: season.start_date.split('T')[0],
                              end_date: season.end_date.split('T')[0],
                              is_active: season.is_active
                            });
                          }}
                        >
                          <Edit3 className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Season</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{season.season_name}"? This action cannot be undone and will also delete all associated tiers and player progress.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => handleDeleteSeason(season.id)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Season Dialog */}
      {editingSeasonId && (
        <Dialog open={!!editingSeasonId} onOpenChange={() => setEditingSeasonId(null)}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Battle Pass Season</DialogTitle>
              <DialogDescription>
                Update the season details and settings.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit_season_name">Season Name</Label>
                <Input
                  id="edit_season_name"
                  value={seasonForm.season_name}
                  onChange={(e) => setSeasonForm(prev => ({ ...prev, season_name: e.target.value }))}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit_start_date">Start Date</Label>
                  <Input
                    id="edit_start_date"
                    type="date"
                    value={seasonForm.start_date}
                    onChange={(e) => setSeasonForm(prev => ({ ...prev, start_date: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="edit_end_date">End Date</Label>
                  <Input
                    id="edit_end_date"
                    type="date"
                    value={seasonForm.end_date}
                    onChange={(e) => setSeasonForm(prev => ({ ...prev, end_date: e.target.value }))}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit_is_active"
                  checked={seasonForm.is_active}
                  onChange={(e) => setSeasonForm(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="edit_is_active">Set as active season</Label>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingSeasonId(null)}>
                  Cancel
                </Button>
                <Button onClick={async () => {
                  await handleUpdateSeason(editingSeasonId, seasonForm);
                  setEditingSeasonId(null);
                }}>
                  Update Season
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}