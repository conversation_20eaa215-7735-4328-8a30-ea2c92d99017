'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { seedDefaultItems } from '@/utils/customization-utils';
import { seedDefaultBattlePass } from '@/utils/battle-pass-utils';
import { toast } from 'sonner';
import { Loader2, Database, CheckCircle, XCircle, Package, Crown } from 'lucide-react';

export default function SeedPage() {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedStatus, setSeedStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [seedMessage, setSeedMessage] = useState('');
  const [battlePassStatus, setBattlePassStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [battlePassMessage, setBattlePassMessage] = useState('');

  const handleSeedItems = async () => {
    setIsSeeding(true);
    setSeedStatus('idle');
    setSeedMessage('');

    try {
      const result = await seedDefaultItems();

      if (result.success) {
        setSeedStatus('success');
        setSeedMessage(result.message + (result.itemsAdded !== undefined ? ` (${result.itemsAdded} items added)` : ''));
        toast.success('Default items seeded successfully!');
      } else {
        setSeedStatus('error');
        setSeedMessage(result.message);
        toast.error('Failed to seed items');
      }
    } catch (error) {
      setSeedStatus('error');
      setSeedMessage(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      toast.error('Failed to seed items');
    } finally {
      setIsSeeding(false);
    }
  };

  const handleSeedBattlePass = async () => {
    setIsSeeding(true);
    setBattlePassStatus('idle');
    setBattlePassMessage('');

    try {
      const result = await seedDefaultBattlePass();

      if (result.success) {
        setBattlePassStatus('success');
        setBattlePassMessage(result.message);
        toast.success('Battle pass seeded successfully!');
      } else {
        setBattlePassStatus('error');
        setBattlePassMessage(result.message);
        toast.error('Failed to seed battle pass');
      }
    } catch (error) {
      setBattlePassStatus('error');
      setBattlePassMessage(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      toast.error('Failed to seed battle pass');
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Items Seeding */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Seed Customization Items
            </CardTitle>
            <CardDescription>
              Initialize the database with default customization items (avatar borders, backgrounds, and titles).
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleSeedItems}
              disabled={isSeeding}
              className="w-full"
            >
              {isSeeding && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSeeding ? 'Seeding Items...' : 'Seed Default Items'}
            </Button>

          {seedStatus !== 'idle' && (
            <div className={`p-3 rounded-lg border flex items-center gap-2 ${
              seedStatus === 'success' 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              {seedStatus === 'success' ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              <span className="text-sm">{seedMessage}</span>
            </div>
          )}

          <div className="text-xs text-gray-600 mt-4">
            <p><strong>Note:</strong> This will populate the database with:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Default avatar borders</li>
              <li>Background themes</li>
              <li>Player titles</li>
            </ul>
            <p className="mt-2">Items that already exist will be skipped.</p>
          </div>
        </CardContent>
      </Card>

      {/* Battle Pass Seeding */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Seed Battle Pass
          </CardTitle>
          <CardDescription>
            Initialize the battle pass system with a default season and tiers.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={handleSeedBattlePass}
            disabled={isSeeding}
            className="w-full"
            variant="outline"
          >
            {isSeeding && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSeeding ? 'Seeding Battle Pass...' : 'Seed Battle Pass'}
          </Button>

          {battlePassStatus !== 'idle' && (
            <div className={`p-3 rounded-lg border flex items-center gap-2 ${
              battlePassStatus === 'success'
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              {battlePassStatus === 'success' ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              <span className="text-sm">{battlePassMessage}</span>
            </div>
          )}

          <div className="text-xs text-gray-600 mt-4">
            <p><strong>Note:</strong> This will create:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>A default battle pass season</li>
              <li>20 tiers with rewards</li>
              <li>Free and premium reward tracks</li>
            </ul>
            <p className="mt-2">Will skip if an active season already exists.</p>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  );
}
