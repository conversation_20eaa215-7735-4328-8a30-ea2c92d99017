'use client';

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { 
  Star, 
  Zap, 
  Crown, 
  Gift, 
  Sparkles, 
  Trophy,
  Lock,
  Check,
  Info,
  Eye,
  Palette,
  User,
  Frame,
  Image as ImageIcon
} from 'lucide-react';
import { Reward, getRarityColor, getTypeIcon } from '@/lib/item-utils';
import { formatRewardText } from '@/lib/item-utils';

interface RewardPreviewModalProps {
  reward: Reward;
  isOpen: boolean;
  showRequirements?: boolean;
  onClose: () => void;
  playerLevel?: number;
  playerRP?: number;
  hasBattlePass?: boolean;
  battlePassTier?: number;
  isUnlocked?: boolean;
  isEquipped?: boolean;
  unlockInfo?: any;
  onEquip?: () => void;
  onPreview?: () => void;
}

export function RewardPreviewModal({
  reward,
  isOpen,
  showRequirements = true,
  onClose,
  playerLevel = 1,
  playerRP = 0,
  hasBattlePass = false,
  battlePassTier = 0,
  isUnlocked = true,
  isEquipped = false,
  unlockInfo,
  onEquip,
  onPreview
}: RewardPreviewModalProps) {
  if (!reward) return null;

  const getRewardIcon = (type: string, size: 'sm' | 'md' | 'lg' = 'md') => {
    const iconClasses = {
      sm: 'h-4 w-4',
      md: 'h-6 w-6',
      lg: 'h-8 w-8'
    };

    switch (type) {
      case 'xp': return <Zap className={cn(iconClasses[size], 'text-blue-600')} />;
      case 'currency': return <Star className={cn(iconClasses[size], 'text-amber-600')} />;
      case 'item': return <Gift className={cn(iconClasses[size], 'text-purple-600')} />;
      default: return <Gift className={cn(iconClasses[size], 'text-gray-400')} />;
    }
  };

  const getItemTypeIcon = (type: string) => {
    switch (type) {
      case 'avatar': return <User className="h-5 w-5" />;
      case 'avatar_border': return <Frame className="h-5 w-5" />;
      case 'background': return <ImageIcon className="h-5 w-5" />;
      case 'title': return <Trophy className="h-5 w-5" />;
      default: return <Gift className="h-5 w-5" />;
    }
  };

  const getRarityInfo = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return { color: 'text-gray-600 bg-gray-100 border-gray-200', emoji: '⚪', name: 'Common' };
      case 'rare':
        return { color: 'text-blue-600 bg-blue-100 border-blue-200', emoji: '🔵', name: 'Rare' };
      case 'epic':
        return { color: 'text-purple-600 bg-purple-100 border-purple-200', emoji: '🟣', name: 'Epic' };
      case 'legendary':
        return { color: 'text-amber-600 bg-amber-100 border-amber-200', emoji: '🟡', name: 'Legendary' };
      default:
        return { color: 'text-gray-600 bg-gray-100 border-gray-200', emoji: '⚪', name: 'Common' };
    }
  };

  // Non-item rewards (XP, Currency)
  if (reward.type !== 'item') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {getRewardIcon(reward.type, 'md')}
              {reward.type === 'xp' ? 'Experience Points' : 'Game Currency'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Reward Icon */}
            <div className="flex justify-center">
              <div className={cn(
                'w-24 h-24 rounded-full flex items-center justify-center',
                reward.type === 'xp' ? 'bg-gradient-to-br from-blue-100 to-blue-200' : 'bg-gradient-to-br from-amber-100 to-amber-200'
              )}>
                {getRewardIcon(reward.type, 'lg')}
              </div>
            </div>

            {/* Amount Display */}
            <div className="text-center">
              <div className={cn(
                'text-3xl font-bold mb-2',
                reward.type === 'xp' ? 'text-blue-600' : 'text-amber-600'
              )}>
                {reward.amount.toLocaleString()}
              </div>
              <p className="text-gray-600">
                {reward.type === 'xp' ? 'Experience Points' : 'Coins'}
              </p>
            </div>

            {/* Description */}
            <div className={cn(
              'p-4 rounded-lg text-center',
              reward.type === 'xp' ? 'bg-blue-50' : 'bg-amber-50'
            )}>
              <p className={cn(
                'text-sm',
                reward.type === 'xp' ? 'text-blue-700' : 'text-amber-700'
              )}>
                {reward.type === 'xp' 
                  ? 'Experience points help you level up and unlock new features'
                  : 'Coins can be used to purchase items and upgrades'
                }
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }
  
  // Item rewards
  const item = reward.item;
  if (!item) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5 text-gray-400" />
              Unknown Item
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-gray-500">Item information not available</p>
            <p className="text-xs text-gray-400 mt-2">Item ID: {reward.item_id}</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const rarityInfo = getRarityInfo(item.rarity);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getItemTypeIcon(item.type)}
            {item.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Item Preview */}
          <div className="relative">
            <div className="flex justify-center">
              <div className={cn(
                'relative w-32 h-32 rounded-lg border-2 overflow-hidden',
                rarityInfo.color
              )}>
                {item.image_url ? (
                  <div className="relative w-full h-full">
                    <img
                      src={item.image_url}
                      alt={item.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="hidden w-full h-full bg-gradient-to-br from-purple-100 to-purple-200 items-center justify-center text-4xl text-purple-600">
                      {getTypeIcon(item.type)}
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center text-4xl text-purple-600">
                    {getTypeIcon(item.type)}
                  </div>
                )}
                
                {/* Rarity Corner */}
                <div className="absolute top-2 right-2">
                  <div className={cn(
                    'w-6 h-6 rounded-full flex items-center justify-center text-xs',
                    rarityInfo.color
                  )}>
                    {rarityInfo.emoji}
                  </div>
                </div>

                {/* Animated Badge */}
                {item.is_animated && (
                  <div className="absolute bottom-2 left-2">
                    <Badge variant="secondary" className="text-xs bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Animated
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Item Details */}
          <div className="space-y-4">
            {/* Basic Info */}
            <div className="flex items-center justify-between">
              {isEquipped && (
                <Badge className="bg-green-100 text-green-700 border-green-200">
                  <Check className="h-3 w-3 mr-1" />
                  Equipped
                </Badge>
              )}
            </div>

            {/* Description */}
            {item.description && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-gray-700">{item.description}</p>
                </div>
              </div>
            )}

            {/* Unlock Requirement */}
            {showRequirements && (
              <div className={cn(
                'p-4 rounded-lg border',
                unlockInfo?.unlocked 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-amber-50 border-amber-200'
              )}>
                <div className="flex items-center gap-2">
                  {unlockInfo?.unlocked ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Lock className="h-4 w-4 text-amber-600" />
                  )}
                  <p className={cn(
                    'text-sm font-medium',
                    unlockInfo?.unlocked ? 'text-green-700' : 'text-amber-700'
                  )}>
                    {unlockInfo?.unlocked ? 'Unlocked' : 'Locked'}
                  </p>
                </div>
                <p className={cn(
                  'text-xs mt-1',
                  unlockInfo?.unlocked ? 'text-green-600' : 'text-amber-600'
                )}>
                  {unlockInfo?.message}
                </p>
              </div>
            )}

            {/* Item Properties */}
            <Separator />
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Type</p>
                <p className="font-medium capitalize">{item.type.replace('_', ' ')}</p>
              </div>
              <div>
                <p className="text-gray-500">Rarity</p>
                <p className="font-medium">{rarityInfo.name}</p>
              </div>
              {item.is_default && (
                <div className="col-span-2">
                  <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                    Default Item
                  </Badge>
                </div>
              )}
            </div>
          </div>          
          {/* Action Buttons */}
          {isUnlocked && unlockInfo?.unlocked && (
            <div className="flex gap-2">
              {onPreview && (
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={onPreview}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              )}
              {onEquip && !isEquipped && (
                <Button
                  className="flex-1 bg-amber-600 hover:bg-amber-700"
                  onClick={() => {
                    onEquip();
                    // Close modal after successful equip
                    setTimeout(() => onClose(), 100);
                  }}
                >
                  <Palette className="h-4 w-4 mr-2" />
                  Equip Item
                </Button>
              )}
              {isEquipped && (
                <Button
                  variant="outline"
                  className="flex-1"
                  disabled
                >
                  <Check className="h-4 w-4 mr-2" />
                  Currently Equipped
                </Button>
              )}
            </div>
          )}

          {/* Close Button */}
          <Button
            variant="outline"
            className="w-full"
            onClick={onClose}
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
