'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Lock, Check, Crown, Star } from 'lucide-react';
import { cn } from '@/lib/utils';
import { createClient } from '../../supabase/client';
import { toast } from 'sonner';
import {
  PlayerItem,
  PlayerInventoryItem,
  RARITY_COLORS,
  AVATAR_BORDERS,
  BACKGROUND_THEMES,
  getAvailableItems,
  getPlayerInventory,
  equipItem,
  checkUnlockRequirement,
  unlockItem
  // Temporarily comment out these imports
  // getItemImageUrl,
  // isImageBasedItem,
  // getItemPreviewStyle
} from '@/utils/customization-utils';
import { RewardPreviewModal } from '@/components/reward-preview-modal';
import { Reward } from '@/lib/item-utils';

interface CustomizationPanelProps {
  playerId: string;
  playerLevel: number;
  playerRP: number;
  hasBattlePass?: boolean;
  battlePassTier?: number;
  currentDisplayName: string;
}

interface EquippedItems {
  avatar_border?: PlayerItem;
  background?: PlayerItem;
  title?: PlayerItem;
  avatar?: PlayerItem;
}

export default function CustomizationPanel({
  playerId,
  playerLevel,
  playerRP,
  hasBattlePass = false,
  battlePassTier = 0,
  currentDisplayName
}: CustomizationPanelProps) {
  const [availableItems, setAvailableItems] = useState<Record<string, PlayerItem[]>>({});
  const [inventory, setInventory] = useState<Record<string, PlayerInventoryItem[]>>({});
  const [equippedItems, setEquippedItems] = useState<EquippedItems>({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<PlayerItem['type']>('avatar');
  const [googleProfileAvatar, setGoogleProfileAvatar] = useState<string | null>(null);
  const [previewedReward, setPreviewedReward] = useState<Reward | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const supabase = createClient();

  // Temporary dummy functions - replace with imports once issue is resolved
  const getItemImageUrl = (item: PlayerItem): string | null => {
    return item.image_url || null;
  };

  const isImageBasedItem = (item: PlayerItem): boolean => {
    return !!(item.image_url);
  };

  const getItemPreviewStyle = (item: PlayerItem): string => {
    if (!item.image_url) {
      switch (item.type) {
        case 'avatar_border':
          return AVATAR_BORDERS[item.value as keyof typeof AVATAR_BORDERS] || '';
        case 'background':
          return BACKGROUND_THEMES[item.value as keyof typeof BACKGROUND_THEMES] || '';
        default:
          return '';
      }
    }
    return '';
  };  
  
  useEffect(() => {
    loadCustomizationData();
    getGoogleAvatar();

    // Set up realtime subscription for equipped items changes
    const equippedItemsChannel = supabase
      .channel('player-equipped-items')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'players',
          filter: `id=eq.${playerId}`
        },
        async (payload) => {
          await loadEquippedItems();
        }
      )
      .subscribe();

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(equippedItemsChannel);
    };
  }, []);

  const getGoogleAvatar = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user?.user_metadata?.avatar_url) {
        setGoogleProfileAvatar(user.user_metadata.avatar_url);
      }
    } catch (error) {
      console.error('Error fetching Google avatar:', error);
    }
  };

  // Separate function to load only equipped items (for realtime updates)
  const loadEquippedItems = async () => {
    try {
      const { data: playerData } = await supabase
        .from('players')
        .select(`
          equipped_avatar_id,
          equipped_avatar_border_id,
          equipped_background_id,
          equipped_title_id,
          equipped_avatar:equipped_avatar_id(
            id,
            name,
            description,
            type,
            rarity,
            value,
            unlock_requirement,
            is_default,
            image_url,
            is_animated
          ),
          equipped_avatar_border:equipped_avatar_border_id(
            id,
            name,
            description,
            type,
            rarity,
            value,
            unlock_requirement,
            is_default,
            image_url,
            is_animated
          ),
          equipped_background:equipped_background_id(
            id,
            name,
            description,
            type,
            rarity,
            value,
            unlock_requirement,
            is_default,
            image_url,
            is_animated
          ),
          equipped_title:equipped_title_id(
            id,
            name,
            description,
            type,
            rarity,
            value,
            unlock_requirement,
            is_default,
            image_url,
            is_animated
          )
        `)
        .eq('id', playerId)
        .single();

      if (playerData) {
        setEquippedItems({
          avatar: Array.isArray(playerData.equipped_avatar) 
            ? playerData.equipped_avatar[0] 
            : playerData.equipped_avatar,
          avatar_border: Array.isArray(playerData.equipped_avatar_border) 
            ? playerData.equipped_avatar_border[0] 
            : playerData.equipped_avatar_border,
          background: Array.isArray(playerData.equipped_background) 
            ? playerData.equipped_background[0] 
            : playerData.equipped_background,
          title: Array.isArray(playerData.equipped_title) 
            ? playerData.equipped_title[0] 
            : playerData.equipped_title
        });
      }
    } catch (error) {
      console.error('Error loading equipped items:', error);
    }
  };
  
  const loadCustomizationData = async () => {
    setLoading(true);
    
    try {
      // Load available items and player inventory for each type (now including avatar)
      const types: PlayerItem['type'][] = ['avatar', 'avatar_border', 'background', 'title'];
      const availableData: Record<string, PlayerItem[]> = {};
      const inventoryData: Record<string, PlayerInventoryItem[]> = {};
      for (const type of types) {
        availableData[type] = await getAvailableItems(type);
        inventoryData[type] = await getPlayerInventory(playerId, type);
      }
      setAvailableItems(availableData);
      setInventory(inventoryData);

      // Load currently equipped items initially
      await loadEquippedItems();
    } catch (error) {
      console.error('Error loading customization data:', error);
      toast.error('Failed to load customization options');
    } finally {
      setLoading(false);
    }
  };
  const handleEquipItem = async (item: PlayerItem) => {
    try {
      const success = await equipItem(playerId, item.id, item.type);
      
      if (success) {
        // Update local state immediately for instant feedback
        setEquippedItems(prev => ({
          ...prev,
          [item.type]: item
        }));
        toast.success(`Equipped ${item.name}!`);
        // No page reload needed - realtime subscription will handle updates to other components
      } else {
        toast.error('Failed to equip item');
      }
    } catch (error) {
      console.error('Error equipping item:', error);
      toast.error('Failed to equip item');
    }
  };

  const handleUnlockItem = async (item: PlayerItem) => {
    try {
      const success = await unlockItem(playerId, item.id);
      
      if (success) {
        // Refresh inventory
        const newInventory = await getPlayerInventory(playerId, item.type);
        setInventory(prev => ({
          ...prev,
          [item.type]: newInventory
        }));
        toast.success(`Unlocked ${item.name}!`);
      } else {
        toast.error('Failed to unlock item');
      }
    } catch (error) {
      console.error('Error unlocking item:', error);
      toast.error('Failed to unlock item');
    }
  };

  const isItemUnlocked = (item: PlayerItem): boolean => {
    return inventory[item.type]?.some(inv => inv.id === item.id) || false;
  };

  const isItemEquipped = (item: PlayerItem): boolean => {
    const itemType = item.type as keyof EquippedItems;
    return equippedItems[itemType]?.id === item.id;
  };  const handleEquipGoogleAvatar = async () => {
    if (!googleProfileAvatar) {
      toast.error('Google profile avatar not available');
      return;
    }

    try {
      // Update the player's avatar_url directly in the players table
      const { error } = await supabase
        .from('players')
        .update({ avatar_url: googleProfileAvatar })
        .eq('id', playerId);

      if (error) throw error;

      toast.success('Google profile avatar equipped!');
      
      // No page reload needed - realtime subscription will handle updates to other components
    } catch (error) {
      console.error('Error equipping Google avatar:', error);
      toast.error('Failed to equip Google avatar');
    }
  };

  const canUnlockItem = (item: PlayerItem): boolean => {
    return checkUnlockRequirement(
      item.unlock_requirement,
      playerLevel,
      playerRP,
      hasBattlePass,
      battlePassTier
    );
  };
  const getUnlockRequirementText = (requirement: PlayerItem['unlock_requirement']): string => {
    if (!requirement) return 'Unlocked by default';
    
    switch (requirement.type) {
      case 'level':
        return `Reach level ${requirement.value}`;
      case 'rp':
        return `Earn ${requirement.value} RP`;
      case 'battlepass':
        return `Battle Pass tier ${requirement.value}`;
      case 'achievement':
        return `Complete achievement: ${requirement.value}`;
      default:
        return 'Unknown requirement';
    }
  };

  // Helper function to convert PlayerItem to Reward format for modal compatibility
  const convertPlayerItemToReward = (item: PlayerItem): Reward => {
    return {
      type: 'item',
      id: item.id,
      item_id: item.id,
      item: {
        id: item.id,
        name: item.name,
        type: item.type,
        rarity: item.rarity,
        value: item.value,
        unlock_requirement: item.unlock_requirement ? JSON.stringify(item.unlock_requirement) : null,
        is_default: item.is_default,
        description: item.description || null,
        created_at: new Date().toISOString(),
        image_url: item.image_url || '',
        is_animated: item.is_animated || false
      }
    };
  };

  // Handle preview click
  const handlePreviewClick = (item: PlayerItem) => {
    const reward = convertPlayerItemToReward(item);
    setPreviewedReward(reward);
    setModalOpen(true);
  };

  const ItemCard = ({ item }: { item: PlayerItem }) => {
    const unlocked = isItemUnlocked(item);
    const equipped = isItemEquipped(item);
    const canUnlock = canUnlockItem(item);

    return (
      <Card className={cn(
        'relative transition-all cursor-pointer hover:shadow-md',
        equipped && 'ring-2 ring-amber-500',
        !unlocked && 'opacity-75'
      )}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">{item.name}</CardTitle>
            <Badge className={cn('text-xs', RARITY_COLORS[item.rarity])}>
              {item.rarity}
            </Badge>
          </div>
          {item.description && (
            <CardDescription className="text-xs">
              {item.description}
            </CardDescription>
          )}
        </CardHeader>
          <CardContent className="pt-0">
          {/* Preview */}
          <div 
            className="mb-3 flex justify-center cursor-pointer hover:opacity-80 transition-opacity" 
            onClick={() => handlePreviewClick(item)}
            title="Click to preview details"
          >
            {item.type === 'avatar' && (
              <div className="relative">
                <Avatar className="h-12 w-12 border-2 border-amber-200">
                  <AvatarImage 
                    src={getItemImageUrl(item) || `https://api.dicebear.com/7.x/avataaars/svg?seed=${currentDisplayName}`} 
                    alt={item.name} 
                  />
                  <AvatarFallback className="bg-amber-200 text-amber-800">
                    {currentDisplayName.substring(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {item.is_animated && (
                  <div className="absolute -top-1 -right-1 bg-purple-500 rounded-full p-1">
                    <Star size={8} className="text-white" />
                  </div>
                )}
              </div>
            )}

            {item.type === 'avatar_border' && (
              <div className="relative">
                {isImageBasedItem(item) ? (
                  <div className="relative">
                    <Avatar className="h-12 w-12">
                    </Avatar>
                    <div 
                      className="absolute inset-0 rounded-full bg-cover bg-center"
                      style={{ backgroundImage: `url(${getItemImageUrl(item)})` }}
                    />
                  </div>
                ) : (
                  <div className={cn(
                    'relative rounded-full',
                    getItemPreviewStyle(item)
                  )}>
                    <Avatar className="h-12 w-12">
                    </Avatar>
                  </div>
                )}
              </div>
            )}
            
            {item.type === 'background' && (
              <div className="w-full h-16 rounded-lg overflow-hidden">
                {isImageBasedItem(item) ? (
                  <div 
                    className="w-full h-full bg-cover bg-center"
                    style={{ backgroundImage: `url(${getItemImageUrl(item)})` }}
                  />
                ) : (
                  <div className={cn(
                    'w-full h-full',
                    getItemPreviewStyle(item)
                  )} />
                )}
              </div>
            )}
            
            {item.type === 'title' && (
              <div className="text-center p-2 bg-amber-50 rounded-lg">
                <span className="text-sm font-medium text-amber-900">{item.value}</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="space-y-2">
            {equipped && (
              <Button size="sm" className="w-full" disabled>
                <Check size={14} className="mr-1" />
                Equipped
              </Button>
            )}
            
            {!equipped && unlocked && (
              <Button 
                size="sm" 
                className="w-full bg-amber-600 hover:bg-amber-700"
                onClick={() => handleEquipItem(item)}
              >
                Equip
              </Button>
            )}
            
            {!unlocked && canUnlock && (
              <Button 
                size="sm" 
                variant="outline" 
                className="w-full"
                onClick={() => handleUnlockItem(item)}
              >
                <Star size={14} className="mr-1" />
                Unlock
              </Button>
            )}
            
            {!unlocked && !canUnlock && (
              <div className="text-center">
                <Lock size={14} className="mx-auto text-gray-400 mb-1" />
                <p className="text-xs text-gray-500">
                  {getUnlockRequirementText(item.unlock_requirement)}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-700"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as PlayerItem['type'])}>
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="avatar">Avatars</TabsTrigger>
        <TabsTrigger value="avatar_border">Borders</TabsTrigger>
        <TabsTrigger value="background">Backgrounds</TabsTrigger>
        <TabsTrigger value="title">Titles</TabsTrigger>
      </TabsList>        
      {(['avatar', 'avatar_border', 'background', 'title'] as const).map((type) => (
        <TabsContent key={type} value={type} className="mt-4">
        
        {/* Google Profile Avatar Option for Avatar Tab */}
        {type === 'avatar' && googleProfileAvatar && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12 border-2 border-blue-300">
                <AvatarImage src={googleProfileAvatar} alt="Google Profile" />
                <AvatarFallback>GP</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">Google Profile Avatar</h4>
                <p className="text-sm text-gray-600">Use your Google account profile picture</p>
              </div>
              <Button
                size="sm"
                onClick={handleEquipGoogleAvatar}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Equip
              </Button>
            </div>
          </div>
        )}
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {availableItems[type]
            ?.slice()
            .sort((a, b) => {
              // 1. Equipped items first
              const aEquipped = isItemEquipped(a);
              const bEquipped = isItemEquipped(b);
              if (aEquipped && !bEquipped) return -1;
              if (!aEquipped && bEquipped) return 1;
              
              // 2. Unlocked items next
              const aUnlocked = isItemUnlocked(a);
              const bUnlocked = isItemUnlocked(b);
              if (aUnlocked && !bUnlocked) return -1;
              if (!aUnlocked && bUnlocked) return 1;
              
              // 3. Sort by rarity (common to legendary)
              const rarityOrder = { 'common': 0, 'rare': 1, 'epic': 2, 'legendary': 3 };
              const aRarityOrder = rarityOrder[a.rarity as keyof typeof rarityOrder] ?? 0;
              const bRarityOrder = rarityOrder[b.rarity as keyof typeof rarityOrder] ?? 0;
              
              return aRarityOrder - bRarityOrder;
            })
            .map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
        </div>
        </TabsContent>
      ))}
      </Tabs>      
      {/* Reward Preview Modal */}
      {previewedReward && (
        <RewardPreviewModal 
          reward={previewedReward}
          isOpen={modalOpen} 
          onClose={() => setModalOpen(false)}
          playerLevel={playerLevel}
          playerRP={playerRP}
          hasBattlePass={hasBattlePass}
          battlePassTier={battlePassTier}

        />
      )}
    </div>
  )
}
