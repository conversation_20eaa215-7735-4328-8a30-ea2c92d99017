"use client";

import FloatingText from '@/components/floating-text';
import { useState } from 'react';

export default function FloatingTextTest() {
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);

  const triggerSuccess = () => {
    if (!showSuccess) { // Only trigger if not already showing
      console.log("Success triggered")
      setShowSuccess(true);
    }
  };

  const triggerError = () => {
    if (!showError) { // Only trigger if not already showing
      console.log("Error triggered")
      setShowError(true);
    }
  };

  const onSuccessComplete = () => {
    console.log("Success completed")
    setShowSuccess(false);
  };

  const onErrorComplete = () => {
    console.log("Error completed")
    setShowError(false);
  };

  return (
    <div className="min-h-screen bg-amber-50 flex flex-col items-center justify-center gap-8">
      <h1 className="text-3xl font-bold text-amber-900">Floating Text Test</h1>
      
      <div className="flex gap-4">
        <button
          onClick={triggerSuccess}
          className="px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 transition-colors"
        >
          Test Success (+965)
        </button>
        
        <button
          onClick={triggerError}
          className="px-6 py-3 bg-red-500 text-white rounded-lg font-semibold hover:bg-red-600 transition-colors"
        >
          Test Error (-1❤️)
        </button>
      </div>      <FloatingText
        text="+965"
        type="success"
        isVisible={showSuccess}
        onComplete={onSuccessComplete}
      />

      <FloatingText
        text="-1❤️"
        type="error"
        isVisible={showError}
        onComplete={onErrorComplete}
      />
    </div>
  );
}
