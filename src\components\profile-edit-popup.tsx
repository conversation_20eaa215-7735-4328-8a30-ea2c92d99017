'use client'

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Edit, Save, X, Palette, Crown, Sparkles, Lock, Check, User, Frame } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '../../supabase/client';
import { 
  PlayerItem, 
  PlayerInventoryItem,
  getAvailableItems,
  getPlayerInventory,
  checkUnlockRequirement,
  AVATAR_BORDERS,
  BACKGROUND_THEMES
} from '@/utils/customization-utils';
import RankDisplay from './rank-display';
import StyledAvatar from './styled-avatar';

interface ProfileEditPopupProps {
  children: React.ReactNode;
  currentDisplayName: string;
  currentTitle: PlayerItem;
  currentBackground: PlayerItem;
  currentAvatar: PlayerItem;
  currentBorder: PlayerItem;
  userLevel: number;
  userId: string;
  currentRP: any;
  currentXP: any;
}

export default function ProfileEditPopup({ 
  children, 
  currentDisplayName, 
  currentTitle, 
  currentBackground,
  currentAvatar,
  currentBorder,
  userLevel, 
  userId,
  currentRP,
  currentXP,
}: ProfileEditPopupProps) {
  const [open, setOpen] = useState(false);
  const [displayName, setDisplayName] = useState(currentDisplayName);
  const [selectedBackground, setSelectedBackground] = useState<PlayerItem>(currentBackground);
  const [selectedAvatar, setSelectedAvatar] = useState(currentAvatar || '');
  const [selectedBorder, setSelectedBorder] = useState<PlayerItem>(currentBorder);
  const [selectedTitle, setSelectedTitle] = useState<PlayerItem>(currentTitle);
  const [isUpdating, setIsUpdating] = useState(false);
  const [googleProfileAvatar, setGoogleProfileAvatar] = useState<string | null>(null);
    // Database items state
  const [availableItems, setAvailableItems] = useState<PlayerItem[]>([]);
  const [playerInventory, setPlayerInventory] = useState<PlayerInventoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  const supabase = createClient();

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      loadItemsData();
      getGoogleAvatar();
    }
  }, [open, userId]);  const loadItemsData = async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      // Get current user data
      const { data: userData, error: userError } = await supabase
        .from('players')
        .select(`avatar_url`)
        .eq('id', userId)
        .single();

      if (userError) throw userError;
      setCurrentUser(userData);

      // Load all available items by type
      const [avatars, borders, backgrounds, titles] = await Promise.all([
        getAvailableItems('avatar'),
        getAvailableItems('avatar_border'),
        getAvailableItems('background'),
        getAvailableItems('title')
      ]);
      
      const allItems = [...avatars, ...borders, ...backgrounds, ...titles];
      setAvailableItems(allItems);
      
      // Load player inventory for all types
      const [avatarInventory, borderInventory, backgroundInventory, titleInventory] = await Promise.all([
        getPlayerInventory(userId, 'avatar'),
        getPlayerInventory(userId, 'avatar_border'), 
        getPlayerInventory(userId, 'background'),
        getPlayerInventory(userId, 'title')
      ]);
      
      const allInventory = [...avatarInventory, ...borderInventory, ...backgroundInventory, ...titleInventory];
      setPlayerInventory(allInventory);
      
    } catch (error) {
      console.error('Error loading items:', error);
      toast.error('Failed to load customization options');
    } finally {
      setLoading(false);
    }
  };const getGoogleAvatar = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user?.user_metadata?.avatar_url) {
        setGoogleProfileAvatar(user.user_metadata.avatar_url);
      }
    } catch (error) {
      console.error('Error fetching Google avatar:', error);
    }
  };

  const handleSave = async () => {
    if (!displayName.trim()) {
      toast.error('Display name cannot be empty');
      return;
    }

    setIsUpdating(true);
    try {
      // Update basic profile info
      const { error: profileError } = await supabase
        .from('players')
        .update({ 
          display_name: displayName.trim(),
          equipped_avatar_border_id: selectedBorder?.id,
          equipped_background_id: selectedBackground?.id,
          equipped_title_id: selectedTitle?.id,
          equipped_avatar_id: selectedAvatar?.id ? selectedAvatar.id : null
        })
        .eq('id', userId);

      if (profileError) throw profileError;
      
      toast.success('Profile updated successfully!');
      setOpen(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('An error occurred while updating your profile.');
    } finally {
      setIsUpdating(false);
    }
  }; 

  const handleItemSelect = async (item: PlayerItem, type: 'avatar' | 'avatar_border' | 'background' | 'title') => {
    console.log(item);
    // Check if item is unlocked for player
    const inventoryItem = playerInventory.find(inv => inv.id === item.id);
    
    if (!inventoryItem) {
      // Check if item can be unlocked based on requirements
      const canUnlock = checkUnlockRequirement(
        item.unlock_requirement, 
        userLevel, 
        0, // RP - would need to get from user data
        false, // Battle pass - would need to get from user data  
        0 // Battle pass tier - would need to get from user data
      );
      if (!canUnlock) {
        toast.error(`Item locked! ${getUnlockText(item)}`);
        return;
      }
    }

    switch (type) {
      case 'avatar':
        setSelectedAvatar(item);
        break;
      case 'avatar_border':
        setSelectedBorder(item);
        break;
      case 'background':
        setSelectedBackground(item);
        break;
      case 'title':
        setSelectedTitle(item);
        break;
    }
  };

  const handleGoogleAvatarSelect = () => {
    if (googleProfileAvatar) {
      setSelectedAvatar({
        id: '',
        name: 'Google Profile Avatar',
        type: 'avatar',
        image_url: googleProfileAvatar,
        is_animated: false,
        value: 'google-avatar',
        rarity: 'common',
        unlock_requirement: null,
        is_default: false
      });
      toast.success('Google profile avatar selected!');
    }
  };

  const getUnlockText = (item: PlayerItem): string => {
    if (!item.unlock_requirement) return 'Always available';
    
    const req = item.unlock_requirement as any;
    switch (req.type) {
      case 'level':
        return `Reach Level ${req.value}`;
      case 'rp':
        return `Reach ${req.value} RP`;
      case 'battlepass':
        return `Battle Pass Tier ${req.value}`;
      default:
        return 'Special unlock required';
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 bg-gray-50';
      case 'rare': return 'border-blue-300 bg-blue-50';
      case 'epic': return 'border-purple-300 bg-purple-50';
      case 'legendary': return 'border-yellow-300 bg-yellow-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  const getRarityBadgeColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-200 text-gray-700';
      case 'rare': return 'bg-blue-200 text-blue-700';
      case 'epic': return 'bg-purple-200 text-purple-700';
      case 'legendary': return 'bg-yellow-200 text-yellow-700';
      default: return 'bg-gray-200 text-gray-700';
    }
  };
  const isItemUnlocked = (item: PlayerItem): boolean => {
    return playerInventory.some(inv => inv.id === item.id);
  };

  const isItemSelected = (item: PlayerItem, type: string): boolean => {
    switch (type) {
      case 'avatar':
        return selectedAvatar === item;
      case 'avatar_border':
        return selectedBorder === item;
      case 'background':
        return selectedBackground === item;
      case 'title':
        return selectedTitle === item;
      default:
        return false;
    }
  };
  const getItemsByType = (type: string) => {
    return availableItems.filter(item => item.type === type);
  };

  const renderItemPreview = (item: PlayerItem) => {
    switch (item.type) {
      case 'avatar':
        return (
          <Avatar className="h-12 w-12 border-2 border-amber-200">
            <AvatarImage 
              src={item.image_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${item.name}`} 
              alt={item.name} 
            />
            <AvatarFallback className="bg-amber-200 text-amber-800">
              {item.name.substring(0, 2)}
            </AvatarFallback>
          </Avatar>
        );
      case 'avatar_border':
        const borderStyle = item.image_url ? '' : AVATAR_BORDERS[item.value as keyof typeof AVATAR_BORDERS];
        return (
          <div className={cn("relative", borderStyle && "p-1 rounded-full")}>
            <Avatar className={cn("h-12 w-12", !item.image_url && borderStyle)}>
            </Avatar>
            {item.image_url && (
              <div 
                className="absolute inset-0 rounded-full bg-cover bg-center"
                style={{ backgroundImage: `url(${item.image_url})` }}
              />
            )}
          </div>
        );
      case 'background':
        console.log(item.image_url ? item.image_url : BACKGROUND_THEMES[item.value as keyof typeof BACKGROUND_THEMES])
        const bgStyle = item.image_url ? 
          { backgroundImage: `url(${item.image_url})` } : undefined ;
        return (
          <div 
            className={cn(
              "w-full h-24 rounded-t-lg relative overflow-hidden bg-cover bg-center",
              !item.image_url && BACKGROUND_THEMES[item.value as keyof typeof BACKGROUND_THEMES]
            )}
            style={bgStyle}
          />
        );
      case 'title':
        return (
          <div className="p-3 bg-amber-50 rounded-lg text-center border border-amber-200">
            <span className="text-sm font-medium text-amber-900">{item.value}</span>
          </div>
        );
      default:
        return <div className="w-12 h-12 bg-gray-200 rounded" />;
    }
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-700"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-amber-900">
            <Edit className="h-5 w-5 text-amber-600" />
            Edit Profile
          </DialogTitle>
        </DialogHeader>
        <div className="overflow-y-auto max-h-[calc(90vh-8rem)] pr-2">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList
              className="pl-20 flex w-full h-auto overflow-y-hidden overflow-x-auto scrollbar-thin scrollbar-thumb-amber-200 scrollbar-track-transparent gap-2"
              style={{ WebkitOverflowScrolling: 'touch' }}
            >
              <TabsTrigger
                value="basic"
                className="flex items-center gap-2 text-xs sm:text-base"
                style={{ flexShrink: 0 }}
                >
                <Edit className="h-4 w-4" />
                <span className="hidden sm:inline">Basic Info</span>
                <span className="sm:hidden">Info</span>
              </TabsTrigger>
              <TabsTrigger
                value="avatars"
                className="flex items-center gap-2 text-xs sm:text-base"
                style={{ flexShrink: 0 }}
                >
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">Avatars</span>
                <span className="sm:hidden">Avatar</span>
              </TabsTrigger>
                <TabsTrigger
                value="borders"
                className="flex items-center gap-2 text-xs sm:text-base"
                style={{ flexShrink: 0 }}
                >
                <Frame className="h-4 w-4" />
                <span className="hidden sm:inline">Borders</span>
                <span className="sm:hidden">Border</span>
                </TabsTrigger>
              <TabsTrigger
                value="background"
                className="flex items-center gap-2 text-xs sm:text-base"
                style={{ flexShrink: 0 }}
                >
                <Palette className="h-4 w-4" />
                <span className="hidden sm:inline">Background</span>
                <span className="sm:hidden">BG</span>
              </TabsTrigger>
              <TabsTrigger
                value="titles"
                className="flex items-center gap-2 text-xs sm:text-base"
                style={{ flexShrink: 0 }}
                >
                <Crown className="h-4 w-4" />
                <span className="hidden sm:inline">Titles</span>
                <span className="sm:hidden">Title</span>
              </TabsTrigger>
            </TabsList>


            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="displayName" className="text-sm font-medium text-gray-700">
                    Display Name
                  </Label>
                  <Input
                    id="displayName"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    placeholder="Enter your display name"
                    className="mt-1"
                    maxLength={15}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {displayName.length}/15 characters
                  </p>
                </div>
              </div>
            </TabsContent>            
            
            {/* Avatars Tab */}
            <TabsContent value="avatars" className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <Label className="text-sm font-medium text-gray-700">
                  Choose Avatar
                  </Label>
                </div>

                {/* Google Profile Avatar Option */}
                {googleProfileAvatar && (
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12 border-2 border-blue-300">
                    <AvatarImage src={googleProfileAvatar} alt="Google Profile" />
                    <AvatarFallback>GP</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Google Profile Avatar</h4>
                    <p className="text-sm text-gray-600">Use your Google account profile picture</p>
                    </div>
                    <Button
                    variant={selectedAvatar?.image_url === googleProfileAvatar ? "default" : "outline"}
                    size="sm"
                    onClick={handleGoogleAvatarSelect}
                    className={selectedAvatar?.image_url === googleProfileAvatar ? "bg-blue-600 hover:bg-blue-700" : ""}
                    >
                    {selectedAvatar?.image_url === googleProfileAvatar ? (
                      <>
                      <Check size={14} className="mr-1" />
                      Selected
                      </>
                    ) : (
                      'Use This'
                    )}
                    </Button>
                  </div>
                  </div>
                )}

                {/* Avatar Items Grid */}
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3 overflow-y-hidden p-2">
                  {getItemsByType('avatar').filter(item => isItemUnlocked(item)).map((item) => {
                  const selected = isItemSelected(item, 'avatar');

                  return (
                    <div
                      key={item.id}
                      className={cn(
                        "flex flex-col items-center cursor-pointer p-2 rounded-lg transition-all duration-200 border-2",
                        getRarityColor(item.rarity),
                        "hover:bg-gray-50 hover:scale-105",
                        selected && "ring-2 ring-amber-400 bg-amber-50"
                      )}
                      onClick={() => handleItemSelect(item, 'avatar')}
                    >
                    <div className="relative">
                      {renderItemPreview(item)}
                      
                      {selected && (
                      <div className="absolute -top-1 -right-1 bg-amber-500 rounded-full p-1">
                        <Check size={12} className="text-white" />
                      </div>
                      )}
                      
                      {/* Rarity badge */}
                      <div className={cn(
                      "absolute -bottom-1 left-1/2 transform -translate-x-1/2 px-1 py-0.5 rounded text-[8px] font-bold",
                      getRarityBadgeColor(item.rarity)
                      )}>
                      {item.rarity.charAt(0).toUpperCase()}
                      </div>
                    </div>
                    
                    <span className="text-[10px] sm:text-xs mt-1 text-center text-amber-800 font-medium">
                      {item.name}
                    </span>
                    </div>
                  );
                  })}
                </div>
              </div>
            </TabsContent>

            {/* Borders Tab */}
            <TabsContent value="borders" className="space-y-6">
              <div>
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium text-gray-700">
                  Choose Avatar Border
                </Label>
              </div>

              {/* Borders Items Grid */}
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3 overflow-y-hidden p-2">
                {getItemsByType('avatar_border').filter(item => isItemUnlocked(item)).map((item) => {
                const selected = isItemSelected(item, 'avatar_border');

                return (
                  <div
                  key={item.id}
                  className={cn(
                    "flex flex-col items-center cursor-pointer p-3 rounded-lg transition-all duration-200 border-2",
                    getRarityColor(item.rarity),
                    "hover:bg-gray-50 hover:scale-105",
                    selected && "ring-2 ring-amber-400 bg-amber-50"
                  )}
                  onClick={() => handleItemSelect(item, 'avatar_border')}
                  >
                  <div className="relative">
                    {renderItemPreview(item)}
                    
                    {selected && (
                    <div className="absolute -top-1 -right-1 bg-amber-500 rounded-full p-1">
                      <Check size={12} className="text-white" />
                    </div>
                    )}
                    
                    {/* Rarity badge */}
                    <div className={cn(
                    "absolute -bottom-1 left-1/2 transform -translate-x-1/2 px-1 py-0.5 rounded text-[8px] font-bold",
                    getRarityBadgeColor(item.rarity)
                    )}>
                    {item.rarity.charAt(0).toUpperCase()}
                    </div>
                  </div>
                  
                  <span className="text-[10px] sm:text-xs mt-2 text-center text-amber-800 font-medium">
                    {item.name}
                  </span>
                  </div>
                );
                })}
              </div>
              </div>
            </TabsContent>

            {/* Background Tab */}
            <TabsContent value="background" className="space-y-6">
              <div>
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium text-gray-700">
                  Choose Background
                </Label>
                
              </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 overflow-y-hidden p-2">
                  {getItemsByType('background').filter(item => isItemUnlocked(item)).map((item) => {
                  const selected = isItemSelected(item, 'background');
                
                return (
                  <div
                    key={item.id}
                    className={cn(
                      "relative cursor-pointer rounded-lg border-2 transition-all duration-200",
                      getRarityColor(item.rarity),
                      "hover:scale-105",
                      selected && "ring-2 ring-amber-400"
                    )}
                    onClick={() => handleItemSelect(item, 'background')}
                  >
                    {/* Background Preview */}
                    <div 
                      className="h-24 rounded-t-lg relative overflow-hidden">
                      {renderItemPreview(item)}
                      
                      {selected && (
                      <div className="absolute top-2 right-2 bg-amber-500 rounded-full p-1">
                        <Check size={12} className="text-white" />
                      </div>
                      )}
                      
                      {/* Rarity indicator */}
                      <div className={cn(
                        "absolute top-2 left-2 px-2 py-1 rounded text-xs font-bold",
                        getRarityBadgeColor(item.rarity)
                      )}>
                        {item.rarity.charAt(0).toUpperCase()}
                      </div>
                    </div>
                    
                    {/* Background Info */}
                    <div className="p-3">
                      <h4 className="font-medium text-sm text-gray-900 mb-1">
                      {item.name}
                      </h4>
                    </div>
                  </div>
                );
                })}
              </div>
              </div>
            </TabsContent>

            {/* Titles Tab */}
            <TabsContent value="titles" className="space-y-6">
              <div>
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium text-gray-700">
                  Choose Title
                </Label>
              </div>

              {/* Titles Items Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 overflow-y-hidden p-2">
                {getItemsByType('title').filter(item => isItemUnlocked(item)).map((item) => {
                const selected = isItemSelected(item, 'title');

                return (
                  <div
                  key={item.id}
                  className={cn(
                    "cursor-pointer p-4 rounded-lg transition-all duration-200 border-2",
                    getRarityColor(item.rarity),
                    "hover:bg-gray-50 hover:scale-105",
                    selected && "ring-2 ring-amber-400 bg-amber-50"
                  )}
                  onClick={() => handleItemSelect(item, 'title')}
                  >
                  <div className="relative">
                    {/* Title Preview */}
                    <div className="flex items-center justify-between mb-2">
                    {renderItemPreview(item)}
                    
                    {selected && (
                      <div className="bg-amber-500 rounded-full p-1">
                      <Check size={12} className="text-white" />
                      </div>
                    )}
                    </div>
                    
                    {/* Title Info */}
                    <div className="space-y-1">
                    <div className="flex items-center justify-between gap-2">
                      {item.description && (
                        <p className="text-xs text-gray-600">
                          {item.description}
                        </p>
                      )}

                      <div className={cn(
                        "px-2 py-1 rounded text-xs font-bold",
                        getRarityBadgeColor(item.rarity)
                      )}>
                        {item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)}
                      </div>
                    </div>
                    
                    </div>
                  </div>
                  </div>
                );
                })}
              </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Preview */}
          <div className="mt-6">
            <Label className="text-sm font-medium text-gray-700 mb-3 block">
              Preview
            </Label>
            <div 
              className={cn(
                "pb-4 rounded-lg sm:pb-6 relative bg-cover bg-center",
                !selectedBackground?.image_url && selectedBackground?.value ? BACKGROUND_THEMES[selectedBackground?.value as keyof typeof BACKGROUND_THEMES] : 'bg-gradient-to-br from-amber-100 via-amber-50 to-amber-100'
              )}
              style={
                selectedBackground?.image_url ? {
                  backgroundImage: `url(${selectedBackground.image_url})`
                } : undefined
              }
            >
            
              {/* Enhanced overlay for better text readability */}
              {selectedBackground?.image_url && (
                <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40 rounded-lg" />
              )}
              
              <div className="relative z-10 space-y-2 flex flex-col items-center justify-center p-4">
                  <div className='relative'>
                    <div 
                      className="absolute inset-0 rounded-full bg-cover bg-center scale-110"
                    />
                    <StyledAvatar
                      src={selectedAvatar?.image_url ? selectedAvatar?.image_url : currentUser?.avatar_url}
                      alt={displayName}
                      fallback={displayName.substring(0, 2).toUpperCase()}
                      size="2xl"
                      border={selectedBorder?.image_url ? selectedBorder?.image_url : currentBorder?.image_url}
                      totalXP={currentXP}
                      showLevel={false}
                    />
                  </div>

                <h3 className={cn(
                  "mt-3 sm:mt-4 text-xl sm:text-2xl font-bold",
                  selectedBackground?.image_url
                    ? "text-white drop-shadow-lg shadow-black/50" 
                    : "text-amber-900"
                )}>
                  {displayName || 'Your Name'}
                </h3>
                
                <div className={cn(
                  "text-center p-2 rounded-lg",
                  (selectedBackground?.image_url || currentBackground?.image_url)
                    ? "bg-white/95 backdrop-blur-sm border border-white/20 shadow-lg" 
                    : "bg-amber-50"
                )}>
                  <span className="text-sm font-medium text-amber-900">
                    {selectedTitle?.value ? selectedTitle?.value : (currentTitle?.value || 'Your Title')}
                  </span>
                </div>
                
                <div className={cn(
                  "flex justify-center w-full rounded-lg"
                )}>
                  <RankDisplay
                    rp={currentRP}
                    variant="compact"
                    showProgress={false}
                    showRP={false}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={isUpdating}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              disabled={isUpdating}
              className="bg-amber-600 hover:bg-amber-700"
            >
              {isUpdating ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
