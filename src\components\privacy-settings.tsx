'use client'

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Shield, Eye, EyeOff, Save, Info } from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '../../supabase/client';

interface PrivacySettingsProps {
  userId: string;
}

interface PrivacySettings {
  show_games_played: boolean;
  show_wins: boolean;
  show_win_rate: boolean;
  show_highest_score: boolean;
  show_longest_streak: boolean;
  show_survival_rate: boolean;
  show_match_history: boolean;
  show_level: boolean;
}

const defaultSettings: PrivacySettings = {
  show_games_played: true,
  show_wins: true,
  show_win_rate: true,
  show_highest_score: true,
  show_longest_streak: true,
  show_survival_rate: true,
  show_match_history: true,
  show_level: true,
};

export default function PrivacySettings({ userId }: PrivacySettingsProps) {
  const [settings, setSettings] = useState<PrivacySettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadPrivacySettings();
  }, [userId]);

  const loadPrivacySettings = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      
      const { data, error } = await supabase
        .from('players')
        .select('privacy_settings')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error loading privacy settings:', error);
        return;
      }

      if (data?.privacy_settings) {
        setSettings({ ...defaultSettings, ...data.privacy_settings });
      }
    } catch (error) {
      console.error('Exception loading privacy settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const savePrivacySettings = async () => {
    setSaving(true);
    try {
      const supabase = createClient();
      
      const { error } = await supabase
        .from('players')
        .update({ privacy_settings: settings })
        .eq('id', userId);

      if (error) {
        console.error('Error saving privacy settings:', error);
        toast.error('Failed to save privacy settings');
        return;
      }

      toast.success('Privacy settings saved successfully!');
    } catch (error) {
      console.error('Exception saving privacy settings:', error);
      toast.error('An error occurred while saving settings');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof PrivacySettings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const getVisibleCount = () => {
    return Object.values(settings).filter(Boolean).length;
  };

  const getTotalCount = () => {
    return Object.keys(settings).length;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const privacyOptions = [
    {
      key: 'show_games_played' as keyof PrivacySettings,
      label: 'Games Played',
      description: 'Total number of matches you\'ve participated in'
    },
    {
      key: 'show_wins' as keyof PrivacySettings,
      label: 'Wins',
      description: 'Number of matches you\'ve won'
    },
    {
      key: 'show_win_rate' as keyof PrivacySettings,
      label: 'Win Rate',
      description: 'Percentage of matches won'
    },
    {
      key: 'show_highest_score' as keyof PrivacySettings,
      label: 'Highest Score',
      description: 'Your best score in any match'
    },
    {
      key: 'show_longest_streak' as keyof PrivacySettings,
      label: 'Longest Streak',
      description: 'Your longest consecutive correct answers'
    },
    {
      key: 'show_survival_rate' as keyof PrivacySettings,
      label: 'Survival Rate',
      description: 'Percentage of matches where you weren\'t eliminated early'
    },
    {
      key: 'show_match_history' as keyof PrivacySettings,
      label: 'Match History',
      description: 'Your recent match results and performance'
    },
    {
      key: 'show_level' as keyof PrivacySettings,
      label: 'Level & Experience',
      description: 'Your current level and XP progress'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-amber-900">
          <Shield className="h-5 w-5 text-amber-600" />
          Privacy Settings
        </CardTitle>
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Control what other players can see in your profile
          </p>
          <Badge variant="outline" className="text-xs">
            {getVisibleCount()}/{getTotalCount()} visible
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-900 mb-1">Privacy Control</p>
              <p className="text-blue-700">
                These settings control what other players see when they view your profile. 
                You can always see your own complete stats regardless of these settings.
              </p>
            </div>
          </div>
        </div>

        {/* Privacy Options */}
        <div className="space-y-4">
          {privacyOptions.map((option) => (
            <div key={option.key} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-gray-900">{option.label}</h4>
                  {settings[option.key] ? (
                    <Eye className="h-4 w-4 text-green-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                </div>
                <p className="text-sm text-gray-600">{option.description}</p>
              </div>
              <Switch
                checked={settings[option.key]}
                onCheckedChange={(checked) => updateSetting(option.key, checked)}
                className="ml-4"
              />
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="flex gap-3 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSettings(defaultSettings)}
            className="flex-1"
          >
            Show All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSettings(Object.keys(defaultSettings).reduce((acc, key) => ({ ...acc, [key]: false }), {} as PrivacySettings))}
            className="flex-1"
          >
            Hide All
          </Button>
        </div>

        {/* Save Button */}
        <Button
          onClick={savePrivacySettings}
          disabled={saving}
          className="w-full bg-amber-600 hover:bg-amber-700"
        >
          {saving ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Saving...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Save Privacy Settings
            </div>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
