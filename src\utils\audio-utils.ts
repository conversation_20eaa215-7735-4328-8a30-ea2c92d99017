import { Difficulty } from '@/interfaces/interfaces';
import { useState, useEffect } from 'react';
import { Howl } from 'howler';

// Cache for loaded sounds
const soundCache: Record<string, Howl> = {};

/**
 * Get the audio path for a word based on its name
 * This assumes audio files are named exactly like the words (e.g., "Abstract.mp3")
 */
export const getWordAudioPath = (word: string, difficulty: Difficulty): string => {
  // Convert first letter to uppercase and rest to lowercase to match file naming
  const formattedWord = word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  return `/audio/${difficulty}/${formattedWord}.mp3`;
};

/**
 * Play audio for a word using Howler
 * @param word The word to play audio for
 * @param difficulty The difficulty level of the word
 * @param volume Volume level (0-1)
 * @returns Promise that resolves to true when audio finishes playing, false if error
 */
export const playWordAudio = (
  word: string,
  difficulty: Difficulty,
  volume: number = 0.8
): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      const audioPath = getWordAudioPath(word, difficulty);

      // Use cached Howl instance or create new one
      if (!soundCache[audioPath]) {
        soundCache[audioPath] = new Howl({
          src: [audioPath],
          preload: false,
          html5: true
        });
      }

      // Set the onend callback to resolve the promise when audio finishes
      soundCache[audioPath].off('end'); // Remove any existing end listeners
      soundCache[audioPath].on('end', () => {
        resolve(true); // Audio finished playing successfully
      });

      // Handle load errors
      soundCache[audioPath].off('loaderror');
      soundCache[audioPath].on('loaderror', (_, error) => {
        console.error('Error loading audio:', error);
        resolve(false); // Failed to load/play audio
      });

      // Set volume and play
      soundCache[audioPath].volume(volume);
      soundCache[audioPath].play();

    } catch (error) {
      console.error('Error playing audio:', error);
      resolve(false); // Failed to play audio
    }
  });
};

/**
 * Preload audio files using Howler
 * @param difficulty The difficulty level to preload
 * @param wordList Optional list of specific words to preload
 * @param progressCallback Callback to report loading progress (0-100)
 */
export const preloadAudioForDifficulty = (
  difficulty: Difficulty,
  wordList?: string[],
  progressCallback?: (progress: number) => void
) => {
  const wordsToPreload = wordList || getWordsForDifficulty(difficulty);
  const totalCount = wordsToPreload.length;
  
  if (totalCount === 0) {
    progressCallback?.(100);
    return;
  }
  
  let loadedCount = 0;
  
  wordsToPreload.forEach(word => {
    const audioPath = getWordAudioPath(word, difficulty);
    
    // Skip if already cached
    if (soundCache[audioPath]) {
      loadedCount++;
      progressCallback?.(Math.round((loadedCount / totalCount) * 100));
      return;
    }
    
    // Preload using Howler
    soundCache[audioPath] = new Howl({
      src: [audioPath],
      preload: true,
      onload: () => {
        loadedCount++;
        progressCallback?.(Math.round((loadedCount / totalCount) * 100));
      },
      onloaderror: () => {
        console.error(`Failed to preload audio: ${audioPath}`);
        loadedCount++;
        progressCallback?.(Math.round((loadedCount / totalCount) * 100));
      }
    });
  });
};

/**
 * Hook to preload audio files
 * @param difficulty The difficulty level to preload
 * @param wordList Optional list of specific words to preload
 * @returns Object with loading state and progress percentage
 */
export const usePreloadAudio = (
  difficulty: Difficulty,
  wordList?: string[]
): { isLoaded: boolean; progress: number } => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    preloadAudioForDifficulty(difficulty, wordList, (newProgress) => {
      setProgress(newProgress);
      if (newProgress === 100) {
        setIsLoaded(true);
      }
    });
  }, [difficulty, wordList]);

  return { isLoaded, progress };
};

// Default word lists for each difficulty level
export const DEFAULT_WORD_LISTS: Record<Difficulty, string[]> = {
  'easy': [
    'answer', 'basic', 'campus', 'career', 'class', 'classmate',
    'college', 'credit', 'degree', 'essay', 'fail', 'feedback',
    'focus', 'goal', 'grade', 'group', 'laptop', 'learn',
    'lesson', 'library', 'major', 'minor', 'note', 'notes',
    'online', 'paper', 'pass', 'present', 'project', 'question',
    'quiz', 'reading', 'report', 'research', 'result', 'review',
    'revision', 'schedule', 'skill', 'student', 'study', 'subject',
    'submit', 'success', 'test', 'time', 'topic', 'tutor'
  ],
  'medium': [
    'abstract', 'academic', 'achieve', 'analyze', 'argument', 'argumentative',
    'assignment', 'attendance', 'authentic', 'citation', 'clarification',
    'comprehension', 'conceptual', 'curriculum', 'debate', 'definition',
    'demonstration', 'discipline', 'evaluation', 'experiment', 'format',
    'grammar', 'hypothesis', 'interpretation', 'journal', 'knowledge',
    'laboratory', 'literature', 'methodology', 'observation', 'overview',
    'paragraph', 'participation', 'peer', 'presentation', 'principle',
    'psychology', 'quotation', 'reference', 'resource', 'revision',
    'schedule', 'statistic', 'strategy', 'summary', 'syllabus',
    'technology', 'thesis'
  ],
  'hard': [
    'acquisition', 'altruism', 'ambidextrous', 'ambiguous', 'anonymity', 'articulation',
    'bureaucracy', 'catastrophe', 'colloquial', 'connotation', 'conscientious',
    'constituent', 'contradiction', 'curriculum', 'deteriorate', 'dichotomy',
    'eccentricity', 'empirical', 'entrepreneur', 'epistemology', 'equilibrium',
    'exacerbate', 'exaggeration', 'extrapolate', 'facilitation', 'hierarchy',
    'hypothetical', 'ideological', 'implementation', 'incarceration', 'inconspicuous',
    'irreversible', 'juxtaposition', 'legislation', 'legitimacy', 'metaphorical',
    'miscellaneous', 'perseverance', 'phenomenologicalv', 'phenomenon', 'philosophical',
    'plausible', 'prejudice', 'prerequisite', 'procrastination', 'quantitative',
    'ramification', 'reciprocity', 'simultaneous', 'surveillance'
  ],
  'extreme': [
    'antediluvian', 'anthropocentric', 'anthropological', 'anthropomorphism', 'antithesis',
    'bioaccumulation', 'chronobiological', 'circumlocution', 'deconstructionism',
    'dichotomization', 'disenfranchisement', 'disproportionate', 'electroencephalogram',
    'epistemological', 'ethnographic', 'floccinaucinihilipilification', 'heterogeneous',
    'hypochondriac', 'incompatibility', 'incomprehensibility', 'incontrovertible',
    'indistinguishability', 'interdisciplinary', 'iridocyclitis', 'malfeasance',
    'metacognition', 'miscommunication', 'misinterpretation', 'multiplicative',
    'neoliberalism', 'onomatopoeia', 'parallelogram', 'photosynthesis',
    'polyphiloprogenitive', 'poststructuralism', 'prestidigitation', 'pseudointellectual',
    'pseudopseudohypoparathyroidism', 'psychoneuroimmunology', 'quintessential',
    'semiotics', 'sesquicentennial', 'sesquipedalian', 'soliloquy', 'subterranean',
    'thermodynamics', 'transcendentalism', 'triskaidekaphobia', 'uncharacteristically',
    'xenophobia'
  ]
};

/**
 * Get all words for a specific difficulty level
 * @param difficulty The difficulty level
 * @returns Array of words for that difficulty
 */
export const getWordsForDifficulty = (difficulty: Difficulty): string[] => {
  return DEFAULT_WORD_LISTS[difficulty] || [];
};

/**
 * Play a simple notification sound using Web Audio API
 * This creates a synthetic sound without requiring audio files
 */
export const playNotificationSound = (type: 'success' | 'error', volume: number = 0.3): void => {
  try {
    // Create audio context
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    if (type === 'success') {
      // Success sound: pleasant ascending tone
      const oscillator1 = audioContext.createOscillator();
      const oscillator2 = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator1.connect(gainNode);
      oscillator2.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator1.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
      oscillator2.frequency.setValueAtTime(659.25, audioContext.currentTime); // E5
      
      oscillator1.frequency.exponentialRampToValueAtTime(783.99, audioContext.currentTime + 0.1); // G5
      oscillator2.frequency.exponentialRampToValueAtTime(1046.5, audioContext.currentTime + 0.1); // C6
      
      gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
      
      oscillator1.start(audioContext.currentTime);
      oscillator2.start(audioContext.currentTime);
      oscillator1.stop(audioContext.currentTime + 0.2);
      oscillator2.stop(audioContext.currentTime + 0.2);
    } else {
      // Error sound: descending tone
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.15);
      
      gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.15);
    }
  } catch (error) {
    // Fallback: do nothing if Web Audio API is not supported
    console.warn('Could not play notification sound:', error);
  }
};
