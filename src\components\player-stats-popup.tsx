"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Trophy,
  Target,
  TrendingUp,
  Clock,
  Star,
  Shield,
  Eye,
  EyeOff,
  Calendar,
  Award,
  Flame,
  BarChart3,
  Flag,
  AlertTriangle,
  X,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { createClient } from "../../supabase/client";
import StyledAvatar from "./styled-avatar";
import RankDisplay from "./rank-display";
import { getPlayerStats, getUserMatchHistory } from "@/utils/profile-utils";
import { getRankInfo, formatRP } from "@/utils/ranking-system";
import { BACKGROUND_THEMES } from "@/utils/customization-utils";
import { submitPlayerReport } from "@/utils/report-utils";
import { toast } from "sonner";
import { getAvatarImageUrl } from "@/utils/avatar-utils";

interface PlayerStatsPopupProps {
  children: React.ReactNode;
  playerId: string;
  playerName: string;
  playerAvatar?: string;
  playerBorder?: string;
}

interface PlayerProfile {
  id: string;
  display_name: string;
  avatar_url?: string;
  avatar_border?: string;
  background_items?: any;
  title?: string;
  rankpoints: number;
  highest_rankpoints: number;
  current_rank_tier: string;
  current_rank_division: string;
  level_experience: number;
  created_at: string;
  privacy_settings?: {
    show_games_played?: boolean;
    show_wins?: boolean;
    show_win_rate?: boolean;
    show_highest_score?: boolean;
    show_longest_streak?: boolean;
    show_survival_rate?: boolean;
    show_match_history?: boolean;
    show_rank?: boolean;
  };
}

export default function PlayerStatsPopup({
  children,
  playerId,
  playerName,
  playerAvatar,
  playerBorder,
}: PlayerStatsPopupProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [playerProfile, setPlayerProfile] = useState<PlayerProfile | null>(
    null
  );
  const [playerStats, setPlayerStats] = useState<any>(null);
  const [recentMatches, setRecentMatches] = useState<any[]>([]);
  const [isOwnProfile, setIsOwnProfile] = useState(false);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportReason, setReportReason] = useState<string>("");
  const [customReason, setCustomReason] = useState("");
  const [isSubmittingReport, setIsSubmittingReport] = useState(false);

  useEffect(() => {
    if (open) {
      loadPlayerData();
    }
  }, [open, playerId]);

  const loadPlayerData = async () => {
    setLoading(true);
    try {
      const supabase = createClient();

      // Check if viewing own profile
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setIsOwnProfile(user?.id === playerId);

      // Get player profile data
      const { data: profile, error: profileError } = await supabase
        .from("players")
        .select(
          "*, background_items:equipped_background_id(id, name, type, image_url, is_animated, value)"
        )
        .eq("id", playerId)
        .single();

      if (profileError) {
        console.error("Error fetching player profile:", profileError);
        return;
      }

      setPlayerProfile(profile);

      // Get player stats and match history
      const [stats, matches] = await Promise.all([
        getPlayerStats(playerId),
        getUserMatchHistory(playerId, 5),
      ]);

      setPlayerStats(stats);
      setRecentMatches(matches);
    } catch (error) {
      console.error("Error loading player data:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };
  const getPrivacySetting = (
    setting: keyof NonNullable<PlayerProfile["privacy_settings"]>
  ) => {
    if (isOwnProfile) return true; // Always show own stats
    return playerProfile?.privacy_settings?.[setting] !== false; // Default to true if not set
  };

  const reportCategories = [
    {
      id: "inappropriate_name",
      label: "Inappropriate Name",
      description: "Offensive or inappropriate username",
    },
    {
      id: "cheating",
      label: "Cheating/Exploiting",
      description: "Using cheats or exploiting game mechanics",
    },
    {
      id: "harassment",
      label: "Harassment",
      description: "Bullying, harassment, or toxic behavior",
    },
    {
      id: "spam",
      label: "Spam",
      description: "Spamming chat or repetitive behavior",
    },
    {
      id: "other",
      label: "Other",
      description: "Other violations not listed above",
    },
  ];
  const handleSubmitReport = async () => {
    if (!reportReason || (reportReason === "other" && !customReason.trim())) {
      toast.error("Please select a reason for the report.");
      return;
    }

    setIsSubmittingReport(true);

    try {
      const finalReason =
        reportReason === "other"
          ? customReason.trim()
          : reportCategories.find((cat) => cat.id === reportReason)?.label ||
            reportReason;
      const additionalDetails =
        reportReason === "other" ? customReason.trim() : null;

      const result = await submitPlayerReport(
        playerId,
        reportReason,
        finalReason,
        additionalDetails || undefined
      );

      if (result.success) {
        toast.success(
          "Report submitted successfully. Thank you for helping keep the community safe."
        );
        setShowReportDialog(false);
        setReportReason("");
        setCustomReason("");
      } else {
        toast.error(
          result.error || "Failed to submit report. Please try again."
        );
      }
    } catch (error) {
      console.error("Error submitting report:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmittingReport(false);
    }
  };

  const rankInfo = playerProfile
    ? getRankInfo(playerProfile.rankpoints || 0)
    : null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>{" "}
      <DialogContent
        className={cn(
          "max-w-2xl max-h-[90vh] overflow-y-auto",
          "bg-cover bg-center",
          !playerProfile?.background_items?.image_url &&
            playerProfile?.background_items?.value
            ? BACKGROUND_THEMES[
                playerProfile.background_items
                  ?.value as keyof typeof BACKGROUND_THEMES
              ]
            : "bg-gradient-to-br from-amber-100 via-amber-50 to-amber-100",
          // Add class for enhanced close button styling on background images
          playerProfile?.background_items?.image_url && "dialog-with-bg-image"
        )}
        style={
          playerProfile?.background_items?.image_url
            ? {
                backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url(${playerProfile?.background_items?.image_url})`,
              }
            : undefined
        }
      >
        <DialogHeader>
          <DialogTitle className="sr-only">Player Profile</DialogTitle>
          {/* Custom Header with Background */}
          <div>
            {/* Fade overlay for top and bottom edges */}
            {playerProfile?.background_items?.image_url && (
              <>
                <div className="absolute inset-x-0 top-0 h-8 bg-gradient-to-b from-black/40 to-transparent pointer-events-none z-10" />
                <div className="absolute inset-x-0 bottom-0 h-8 bg-gradient-to-t from-black/40 to-transparent pointer-events-none z-10" />
              </>
            )}
            {/* Content */}
            <div className="relative z-20 flex items-center justify-between mb-3">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <StyledAvatar
                    src={playerAvatar ? getAvatarImageUrl(playerAvatar) : playerProfile?.avatar_url}
                    alt={playerName}
                    fallback={playerName.substring(0, 2).toUpperCase()}
                    size="xl"
                    border={playerBorder}
                    totalXP={playerProfile?.level_experience || 0}
                  />
                </div>
                <div className="flex-1">
                  <h2
                    className={cn(
                      "text-2xl font-bold",
                      playerProfile?.background_items?.image_url
                        ? "text-white drop-shadow-2xl"
                        : "text-amber-900"
                    )}
                    style={
                      playerProfile?.background_items?.image_url
                        ? {
                            textShadow:
                              "2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 8px rgba(0, 0, 0, 0.6)",
                          }
                        : undefined
                    }
                  >
                    {playerName}
                  </h2>

                  {playerProfile?.title && (
                    <p
                      className={cn(
                        "text-sm font-medium",
                        playerProfile?.background_items?.image_url
                          ? "text-white/90 drop-shadow-lg"
                          : "text-amber-700"
                      )}
                      style={
                        playerProfile?.background_items?.image_url
                          ? {
                              textShadow: "1px 1px 3px rgba(0, 0, 0, 0.8)",
                            }
                          : undefined
                      }
                    >
                      {playerProfile.title}
                    </p>
                  )}

                  {rankInfo && getPrivacySetting("show_rank") && (
                    <div className="mt-2">
                      <RankDisplay
                        rp={playerProfile?.rankpoints || 0}
                        variant="compact"
                        showProgress={false}
                        showRP={false}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Report Button - Only show if not viewing own profile */}
              {!isOwnProfile && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowReportDialog(true)}
                  className={cn(
                    "flex items-center gap-2",
                    playerProfile?.background_items?.image_url
                      ? "border-white/30 text-red-700 hover:bg-white/10 backdrop-blur-sm"
                      : "border-red-200 text-red-700 hover:bg-red-50"
                  )}
                >
                  <Flag className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="py-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
            <p
              className={cn(
                "text-lg font-medium",
                playerProfile?.background_items?.image_url
                  ? "text-white"
                  : "text-amber-900"
              )}
            >
              Loading player stats...
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Rank Progress Details */}
            {rankInfo && getPrivacySetting("show_rank") && (
              <Card
                className={cn(
                  playerProfile?.background_items?.image_url &&
                    "bg-black/30 backdrop-blur-sm border-white/20"
                )}
              >
                <CardHeader className="pb-3">
                  <CardTitle
                    className={cn(
                      "text-lg flex items-center gap-2",
                      playerProfile?.background_items?.image_url && "text-white"
                    )}
                  >
                    <Star
                      className={cn(
                        "h-5 w-5",
                        playerProfile?.background_items?.image_url
                          ? "text-amber-300"
                          : "text-amber-600"
                      )}
                    />
                    Rank Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <RankDisplay
                    rp={playerProfile?.rankpoints || 0}
                    variant="detailed"
                    showProgress={true}
                    showRP={true}
                    whiteText={
                      playerProfile?.background_items?.image_url ? true : false
                    }
                  />
                  <div
                    className={cn(
                      "mt-4 pt-4 border-t",
                      playerProfile?.background_items?.image_url
                        ? "border-white/20"
                        : "border-gray-200"
                    )}
                  >
                    <div className="flex justify-between text-sm">
                      <span
                        className={cn(
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Peak Rank:
                      </span>
                      <span
                        className={cn(
                          "font-medium",
                          playerProfile?.background_items?.image_url
                            ? "text-white"
                            : "text-gray-900"
                        )}
                      >
                        {formatRP(playerProfile?.highest_rankpoints || 0)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Stats Overview */}
            <Card
              className={cn(
                playerProfile?.background_items?.image_url &&
                  "bg-black/30 backdrop-blur-sm border-white/20"
              )}
            >
              <CardHeader className="pb-3">
                <CardTitle
                  className={cn(
                    "text-lg flex items-center gap-2",
                    playerProfile?.background_items?.image_url && "text-white"
                  )}
                >
                  <BarChart3
                    className={cn(
                      "h-5 w-5",
                      playerProfile?.background_items?.image_url
                        ? "text-amber-300"
                        : "text-amber-600"
                    )}
                  />
                  Statistics
                  {!isOwnProfile && (
                    <Badge
                      variant="outline"
                      className={cn(
                        "text-xs",
                        playerProfile?.background_items?.image_url &&
                          "border-white/30 text-white/90"
                      )}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Public View
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {getPrivacySetting("show_games_played") && (
                    <div
                      className={cn(
                        "text-center p-3 rounded-lg",
                        playerProfile?.background_items?.image_url
                          ? "bg-white/10 backdrop-blur-sm"
                          : "bg-amber-50"
                      )}
                    >
                      <p
                        className={cn(
                          "text-2xl font-bold",
                          playerProfile?.background_items?.image_url
                            ? "text-white"
                            : "text-amber-700"
                        )}
                      >
                        {playerStats?.total_matches || 0}
                      </p>
                      <p
                        className={cn(
                          "text-sm",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Games Played
                      </p>
                    </div>
                  )}

                  {getPrivacySetting("show_wins") && (
                    <div
                      className={cn(
                        "text-center p-3 rounded-lg",
                        playerProfile?.background_items?.image_url
                          ? "bg-white/10 backdrop-blur-sm"
                          : "bg-green-50"
                      )}
                    >
                      <p
                        className={cn(
                          "text-2xl font-bold",
                          playerProfile?.background_items?.image_url
                            ? "text-green-300"
                            : "text-green-700"
                        )}
                      >
                        {playerStats?.wins || 0}
                      </p>
                      <p
                        className={cn(
                          "text-sm",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Wins
                      </p>
                    </div>
                  )}

                  {getPrivacySetting("show_win_rate") && (
                    <div
                      className={cn(
                        "text-center p-3 rounded-lg",
                        playerProfile?.background_items?.image_url
                          ? "bg-white/10 backdrop-blur-sm"
                          : "bg-blue-50"
                      )}
                    >
                      <p
                        className={cn(
                          "text-2xl font-bold",
                          playerProfile?.background_items?.image_url
                            ? "text-blue-300"
                            : "text-blue-700"
                        )}
                      >
                        {playerStats?.win_rate || 0}%
                      </p>
                      <p
                        className={cn(
                          "text-sm",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Win Rate
                      </p>
                    </div>
                  )}

                  {getPrivacySetting("show_highest_score") && (
                    <div
                      className={cn(
                        "text-center p-3 rounded-lg",
                        playerProfile?.background_items?.image_url
                          ? "bg-white/10 backdrop-blur-sm"
                          : "bg-purple-50"
                      )}
                    >
                      <p
                        className={cn(
                          "text-2xl font-bold",
                          playerProfile?.background_items?.image_url
                            ? "text-purple-300"
                            : "text-purple-700"
                        )}
                      >
                        {playerStats?.highest_score?.toLocaleString() || 0}
                      </p>
                      <p
                        className={cn(
                          "text-sm",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Highest Score
                      </p>
                    </div>
                  )}

                  {getPrivacySetting("show_longest_streak") && (
                    <div
                      className={cn(
                        "text-center p-3 rounded-lg",
                        playerProfile?.background_items?.image_url
                          ? "bg-white/10 backdrop-blur-sm"
                          : "bg-orange-50"
                      )}
                    >
                      <p
                        className={cn(
                          "text-2xl font-bold",
                          playerProfile?.background_items?.image_url
                            ? "text-orange-300"
                            : "text-orange-700"
                        )}
                      >
                        {playerStats?.longest_streak || 0}
                      </p>
                      <p
                        className={cn(
                          "text-sm",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Longest Streak
                      </p>
                    </div>
                  )}

                  {getPrivacySetting("show_survival_rate") && (
                    <div
                      className={cn(
                        "text-center p-3 rounded-lg",
                        playerProfile?.background_items?.image_url
                          ? "bg-white/10 backdrop-blur-sm"
                          : "bg-red-50"
                      )}
                    >
                      <p
                        className={cn(
                          "text-2xl font-bold",
                          playerProfile?.background_items?.image_url
                            ? "text-red-300"
                            : "text-red-700"
                        )}
                      >
                        {playerStats?.survival_rate || 0}%
                      </p>
                      <p
                        className={cn(
                          "text-sm",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Survival Rate
                      </p>
                    </div>
                  )}
                </div>

                {/* Privacy Notice */}
                {!isOwnProfile && (
                  <div
                    className={cn(
                      "mt-4 p-3 rounded-lg border",
                      playerProfile?.background_items?.image_url
                        ? "bg-white/10 backdrop-blur-sm border-white/20"
                        : "bg-gray-50"
                    )}
                  >
                    <div className="flex items-center gap-2 text-sm">
                      <Shield
                        className={cn(
                          "h-4 w-4",
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      />
                      <span
                        className={cn(
                          playerProfile?.background_items?.image_url
                            ? "text-white/80"
                            : "text-gray-600"
                        )}
                      >
                        Some stats may be hidden based on player's privacy
                        settings
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Matches */}
            {getPrivacySetting("show_match_history") &&
              recentMatches.length > 0 && (
                <Card
                  className={cn(
                    playerProfile?.background_items?.image_url &&
                      "bg-black/30 backdrop-blur-sm border-white/20"
                  )}
                >
                  <CardHeader className="pb-3">
                    <CardTitle
                      className={cn(
                        "text-lg flex items-center gap-2",
                        playerProfile?.background_items?.image_url &&
                          "text-white"
                      )}
                    >
                      <Clock
                        className={cn(
                          "h-5 w-5",
                          playerProfile?.background_items?.image_url
                            ? "text-amber-300"
                            : "text-amber-600"
                        )}
                      />
                      Recent Matches
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {recentMatches.map((match, index) => (
                        <div
                          key={match.match_id}
                          className={cn(
                            "flex items-center justify-between p-3 rounded-lg",
                            playerProfile?.background_items?.image_url
                              ? "bg-white/10 backdrop-blur-sm"
                              : "bg-gray-50"
                          )}
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              {match.final_rank === 1 && (
                                <Trophy className="h-4 w-4 text-yellow-500" />
                              )}
                              {match.final_rank === 2 && (
                                <Award className="h-4 w-4 text-gray-400" />
                              )}
                              {match.final_rank === 3 && (
                                <Award className="h-4 w-4 text-amber-600" />
                              )}
                              {match.final_rank > 3 && (
                                <Target className="h-4 w-4 text-gray-500" />
                              )}
                              <span
                                className={cn(
                                  "font-medium text-sm",
                                  playerProfile?.background_items?.image_url
                                    ? "text-white"
                                    : "text-gray-900"
                                )}
                              >
                                #{match.final_rank}
                              </span>
                            </div>

                            <Badge
                              className={cn(
                                "text-xs capitalize",
                                match.difficulty === "easy" &&
                                  "bg-green-100 text-green-700",
                                match.difficulty === "medium" &&
                                  "bg-blue-100 text-blue-700",
                                match.difficulty === "hard" &&
                                  "bg-orange-100 text-orange-700",
                                match.difficulty === "extreme" &&
                                  "bg-purple-100 text-purple-700"
                              )}
                            >
                              {match.difficulty}
                            </Badge>

                            <span
                              className={cn(
                                "text-sm",
                                playerProfile?.background_items?.image_url
                                  ? "text-white/80"
                                  : "text-gray-600"
                              )}
                            >
                              {match.total_players} players
                            </span>
                          </div>

                          <div className="text-right">
                            <p
                              className={cn(
                                "font-medium text-sm",
                                playerProfile?.background_items?.image_url
                                  ? "text-white"
                                  : "text-gray-900"
                              )}
                            >
                              {match.score.toLocaleString()} pts
                            </p>
                            <p
                              className={cn(
                                "text-xs",
                                playerProfile?.background_items?.image_url
                                  ? "text-white/70"
                                  : "text-gray-500"
                              )}
                            >
                              {formatDate(match.ended_at)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
          </div>
        )}

        {/* Report Player Dialog */}
        <Dialog open={showReportDialog} onOpenChange={setShowReportDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-red-700">
                <Flag className="h-5 w-5" />
                Report Player: {playerName}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Please select the reason for reporting this player. All reports
                are reviewed by our moderation team.
              </p>

              <div className="space-y-2">
                {reportCategories.map((category) => (
                  <div key={category.id} className="space-y-1">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="reportReason"
                        value={category.id}
                        checked={reportReason === category.id}
                        onChange={(e) => setReportReason(e.target.value)}
                        className="text-red-600 focus:ring-red-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {category.label}
                        </div>
                        <div className="text-xs text-gray-500">
                          {category.description}
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>

              {reportReason === "other" && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Please describe the issue:
                  </label>
                  <Textarea
                    value={customReason}
                    onChange={(e) => setCustomReason(e.target.value)}
                    placeholder="Describe the violation in detail..."
                    rows={3}
                    className="resize-none"
                    maxLength={500}
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {customReason.length}/500
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-amber-600 flex-shrink-0" />
                <div className="text-xs text-amber-700">
                  False reports may result in penalties to your account. Please
                  only report genuine violations.
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReportDialog(false);
                    setReportReason("");
                    setCustomReason("");
                  }}
                  disabled={isSubmittingReport}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>

                <Button
                  onClick={handleSubmitReport}
                  disabled={
                    !reportReason ||
                    (reportReason === "other" && !customReason.trim()) ||
                    isSubmittingReport
                  }
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isSubmittingReport ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Flag className="h-4 w-4 mr-2" />
                      Submit Report
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
}
