import { createClient } from "../../supabase/client";

export interface ReportCategory {
  id: string;
  category_id: string;
  name: string;
  description: string;
  severity_level: number;
  is_active: boolean;
}

export interface PlayerReport {
  id: string;
  reporter_id: string;
  reported_player_id: string;
  category: string;
  reason: string;
  additional_details?: string;
  status: 'pending' | 'under_review' | 'resolved' | 'dismissed';
  moderator_id?: string;
  moderator_notes?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ModerationAction {
  id: string;
  player_id: string;
  moderator_id: string;
  action_type: 'warning' | 'mute' | 'temporary_ban' | 'permanent_ban' | 'name_change_forced';
  reason: string;
  duration_hours?: number;
  report_id?: string;
  is_active: boolean;
  expires_at?: string;
  created_at: string;
  notes?: string;
}

export interface PlayerRestriction {
  id: string;
  player_id: string;
  restriction_type: 'mute' | 'game_ban' | 'matchmaking_ban';
  reason: string;
  moderator_id?: string;
  expires_at?: string;
  is_permanent: boolean;
  created_at: string;
  moderation_action_id?: string;
}

/**
 * Submit a player report
 */
export async function submitPlayerReport(
  reportedPlayerId: string,
  category: string,
  reason: string,
  additionalDetails?: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'You must be logged in to submit a report.' };
    }

    if (user.id === reportedPlayerId) {
      return { success: false, error: 'You cannot report yourself.' };
    }

    // Check for existing report in the last 24 hours
    const { data: existingReport } = await supabase
      .from('player_reports')
      .select('id')
      .eq('reporter_id', user.id)
      .eq('reported_player_id', reportedPlayerId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .single();

    if (existingReport) {
      return { success: false, error: 'You have already reported this player in the last 24 hours.' };
    }

    const { error } = await supabase
      .from('player_reports')
      .insert([
        {
          reporter_id: user.id,
          reported_player_id: reportedPlayerId,
          category,
          reason,
          additional_details: additionalDetails,
          status: 'pending'
        }
      ]);

    if (error) {
      console.error('Error submitting report:', error);
      return { success: false, error: 'Failed to submit report. Please try again.' };
    }

    return { success: true };

  } catch (error) {
    console.error('Exception in submitPlayerReport:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Get report categories
 */
export async function getReportCategories(): Promise<ReportCategory[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('report_categories')
      .select('*')
      .eq('is_active', true)
      .order('severity_level', { ascending: true });

    if (error) {
      console.error('Error fetching report categories:', error);
      return [];
    }

    return data || [];

  } catch (error) {
    console.error('Exception in getReportCategories:', error);
    return [];
  }
}

/**
 * Check if a player is currently restricted
 */
export async function isPlayerRestricted(
  playerId: string,
  restrictionType?: string
): Promise<boolean> {
  const supabase = createClient();

  try {
    let query = supabase
      .from('active_player_restrictions')
      .select('id')
      .eq('player_id', playerId);

    if (restrictionType) {
      query = query.eq('restriction_type', restrictionType);
    }

    const { data, error } = await query.single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking player restrictions:', error);
      return false;
    }

    return !!data;

  } catch (error) {
    console.error('Exception in isPlayerRestricted:', error);
    return false;
  }
}

/**
 * Get player's moderation history (for admin/moderator use)
 */
export async function getPlayerModerationHistory(
  playerId: string
): Promise<ModerationAction[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('moderation_actions')
      .select('*')
      .eq('player_id', playerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching moderation history:', error);
      return [];
    }

    return data || [];

  } catch (error) {
    console.error('Exception in getPlayerModerationHistory:', error);
    return [];
  }
}

/**
 * Get reports submitted by a user (for user's own view)
 */
export async function getUserReports(): Promise<PlayerReport[]> {
  const supabase = createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    const { data, error } = await supabase
      .from('player_reports')
      .select('*')
      .eq('reporter_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user reports:', error);
      return [];
    }

    return data || [];

  } catch (error) {
    console.error('Exception in getUserReports:', error);
    return [];
  }
}

/**
 * Get all pending reports (for moderator/admin use)
 */
export async function getPendingReports(): Promise<PlayerReport[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('player_reports')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching pending reports:', error);
      return [];
    }

    return data || [];

  } catch (error) {
    console.error('Exception in getPendingReports:', error);
    return [];
  }
}

/**
 * Update report status (for moderator/admin use)
 */
export async function updateReportStatus(
  reportId: string,
  status: PlayerReport['status'],
  moderatorNotes?: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'Authentication required.' };
    }

    const updateData: any = {
      status,
      moderator_id: user.id,
      updated_at: new Date().toISOString()
    };

    if (moderatorNotes) {
      updateData.moderator_notes = moderatorNotes;
    }

    if (status === 'resolved' || status === 'dismissed') {
      updateData.resolved_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('player_reports')
      .update(updateData)
      .eq('id', reportId);

    if (error) {
      console.error('Error updating report status:', error);
      return { success: false, error: 'Failed to update report status.' };
    }

    return { success: true };

  } catch (error) {
    console.error('Exception in updateReportStatus:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Apply moderation action to a player
 */
export async function applyModerationAction(
  playerId: string,
  actionType: ModerationAction['action_type'],
  reason: string,
  durationHours?: number,
  reportId?: string,
  notes?: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'Authentication required.' };
    }

    // Calculate expiry date if duration is provided
    let expiresAt: string | undefined;
    if (durationHours && durationHours > 0) {
      expiresAt = new Date(Date.now() + durationHours * 60 * 60 * 1000).toISOString();
    }

    // Insert moderation action
    const { data: moderationAction, error: actionError } = await supabase
      .from('moderation_actions')
      .insert([
        {
          player_id: playerId,
          moderator_id: user.id,
          action_type: actionType,
          reason,
          duration_hours: durationHours,
          report_id: reportId,
          expires_at: expiresAt,
          notes
        }
      ])
      .select()
      .single();

    if (actionError) {
      console.error('Error creating moderation action:', actionError);
      return { success: false, error: 'Failed to apply moderation action.' };
    }

    // Create player restriction if applicable
    if (['mute', 'temporary_ban', 'permanent_ban'].includes(actionType)) {
      let restrictionType: PlayerRestriction['restriction_type'];
      
      switch (actionType) {
        case 'mute':
          restrictionType = 'mute';
          break;
        case 'temporary_ban':
        case 'permanent_ban':
          restrictionType = 'game_ban';
          break;
        default:
          restrictionType = 'game_ban';
      }

      const { error: restrictionError } = await supabase
        .from('player_restrictions')
        .insert([
          {
            player_id: playerId,
            restriction_type: restrictionType,
            reason,
            moderator_id: user.id,
            expires_at: expiresAt,
            is_permanent: actionType === 'permanent_ban',
            moderation_action_id: moderationAction.id
          }
        ]);

      if (restrictionError) {
        console.error('Error creating player restriction:', restrictionError);
        // Don't fail the whole operation, just log the error
      }
    }

    return { success: true };

  } catch (error) {
    console.error('Exception in applyModerationAction:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Remove player restriction (for admin use)
 */
export async function removePlayerRestriction(
  restrictionId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('player_restrictions')
      .delete()
      .eq('id', restrictionId);

    if (error) {
      console.error('Error removing player restriction:', error);
      return { success: false, error: 'Failed to remove restriction.' };
    }

    return { success: true };

  } catch (error) {
    console.error('Exception in removePlayerRestriction:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}
