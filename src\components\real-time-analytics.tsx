"use client";

import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock, 
  Zap,
  Activity,
  Award,
  Users,
  Timer,
  AlertCircle
} from "lucide-react";
import { Player, GameStateType } from "@/interfaces/interfaces";

interface RealTimeAnalyticsProps {
  players: Player[];
  currentRound: number;
  timeLeft: number;
  gameState: GameStateType;
  matchStartTime?: number;
  className?: string;
}

interface MatchMetrics {
  averageResponseTime: number;
  currentRoundAccuracy: number;
  eliminationRate: number;
  streakLeader: {
    name: string;
    streak: number;
  } | null;
  topPerformer: {
    name: string;
    score: number;
    accuracy: number;
  } | null;
  matchIntensity: 'Low' | 'Medium' | 'High' | 'Extreme';
  survivalPrediction: {
    playerId: string;
    probability: number;
  }[];
}

const RealTimeAnalytics = ({
  players,
  currentRound,
  timeLeft,
  gameState,
  matchStartTime,
  className
}: RealTimeAnalyticsProps) => {
    const metrics = useMemo((): MatchMetrics => {
    const activePlayers = players.filter(p => (p.lives || 0) > 0 && !p.is_spectator);
    const allAnswers = players.filter(p => (p.total_answers || 0) > 0);
      
    // Calculate average response time using actual data when available
    const playersWithResponseTimes = activePlayers.filter(p => p.responseTimes && p.responseTimes.length > 0);
    
    let avgResponseTime: number;
    if (playersWithResponseTimes.length === 0) {
      avgResponseTime = activePlayers.length > 0 ? Math.random() * 8 + 2 : 0; // Fallback for demo
    } else {
      const allResponseTimes = playersWithResponseTimes.flatMap(p => p.responseTimes || []);
      const totalTime = allResponseTimes.reduce((sum, time) => sum + time, 0);
      avgResponseTime = allResponseTimes.length > 0 ? totalTime / allResponseTimes.length : 0;
    }
    
    // Current round accuracy
    const currentCorrect = players.filter(p => p.lastAnswer === "correct").length;
    const currentTotal = players.filter(p => p.lastAnswer !== "pending").length;
    const currentRoundAccuracy = currentTotal > 0 ? (currentCorrect / currentTotal) * 100 : 0;
    
    // Elimination rate
    const totalPlayers = players.length;
    const eliminated = players.filter(p => (p.lives || 0) <= 0 || p.is_spectator).length;
    const eliminationRate = totalPlayers > 0 ? (eliminated / totalPlayers) * 100 : 0;
    
    // Streak leader
    const streakLeader = players.reduce((max, player) => {
      const streak = player.current_streak || 0;
      return streak > (max?.streak || 0) ? 
        { name: player.display_name || 'Unknown', streak } : max;
    }, null as { name: string; streak: number } | null);
    
    // Top performer (by score and accuracy)
    const topPerformer = allAnswers.reduce((best, player) => {
      const score = player.score || 0;
      const accuracy = (player.total_answers || 0) > 0 ? 
        ((player.correct_answers || 0) / (player.total_answers || 0)) * 100 : 0;
      
      if (!best || score > best.score || (score === best.score && accuracy > best.accuracy)) {
        return {
          name: player.display_name || 'Unknown',
          score,
          accuracy
        };
      }
      return best;
    }, null as { name: string; score: number; accuracy: number } | null);
    
    // Match intensity based on elimination rate and round progression
    let matchIntensity: 'Low' | 'Medium' | 'High' | 'Extreme' = 'Low';
    if (eliminationRate > 70 || currentRound > 15) matchIntensity = 'Extreme';
    else if (eliminationRate > 50 || currentRound > 10) matchIntensity = 'High';
    else if (eliminationRate > 30 || currentRound > 5) matchIntensity = 'Medium';
    
    // Survival prediction (simplified algorithm based on current performance)
    const survivalPrediction = activePlayers.map(player => {
      const accuracy = (player.total_answers || 0) > 0 ? 
        ((player.correct_answers || 0) / (player.total_answers || 0)) * 100 : 50;
      const livesRemaining = player.lives || 0;
      const streak = player.current_streak || 0;
      
      // Simple prediction algorithm
      let probability = (accuracy * 0.4) + (livesRemaining * 15) + (streak * 2);
      probability = Math.max(5, Math.min(95, probability)); // Clamp between 5-95%
      
      return {
        playerId: player.id,
        probability: Math.round(probability)
      };
    }).sort((a, b) => b.probability - a.probability);
    
    return {
      averageResponseTime: avgResponseTime,
      currentRoundAccuracy,
      eliminationRate,
      streakLeader,
      topPerformer,
      matchIntensity,
      survivalPrediction
    };
  }, [players, currentRound]);

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'Low': return 'text-green-600 bg-green-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'High': return 'text-orange-600 bg-orange-100';
      case 'Extreme': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const matchDuration = matchStartTime ? Math.floor((Date.now() - matchStartTime) / 1000) : 0;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Real-Time Match Metrics */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Activity className="w-5 h-5 text-blue-600" />
            Live Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Match Intensity */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Match Intensity</span>
            <Badge className={cn("font-semibold", getIntensityColor(metrics.matchIntensity))}>
              {metrics.matchIntensity}
            </Badge>
          </div>

          {/* Current Round Accuracy */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <Target className="w-3 h-3" />
                Round Accuracy
              </span>
              <span className="text-sm font-semibold text-blue-600">
                {metrics.currentRoundAccuracy.toFixed(0)}%
              </span>
            </div>
            <Progress value={metrics.currentRoundAccuracy} className="h-2" />
          </div>

          {/* Average Response Time */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 flex items-center gap-1">
              <Timer className="w-3 h-3" />
              Avg Response Time
            </span>
            <span className="text-sm font-semibold text-gray-800">
              {metrics.averageResponseTime.toFixed(1)}s
            </span>
          </div>

          {/* Elimination Rate */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                Elimination Rate
              </span>
              <span className="text-sm font-semibold text-red-600">
                {metrics.eliminationRate.toFixed(0)}%
              </span>
            </div>
            <Progress value={metrics.eliminationRate} className="h-2" />
          </div>

          {/* Match Duration */}
          {matchStartTime && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center gap-1">
                <Clock className="w-3 h-3" />
                Match Duration
              </span>
              <span className="text-sm font-semibold text-gray-800">
                {formatTime(matchDuration)}
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Highlights */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Award className="w-5 h-5 text-amber-600" />
            Performance Highlights
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Streak Leader */}
          {metrics.streakLeader && metrics.streakLeader.streak > 0 && (
            <div className="flex items-center justify-between p-2 bg-orange-50 rounded-lg">
              <div>
                <div className="text-sm font-medium text-orange-800">🔥 Streak Leader</div>
                <div className="text-xs text-orange-600">{metrics.streakLeader.name}</div>
              </div>
              <div className="text-lg font-bold text-orange-700">
                {metrics.streakLeader.streak}
              </div>
            </div>
          )}

          {/* Top Performer */}
          {metrics.topPerformer && (
            <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
              <div>
                <div className="text-sm font-medium text-green-800">🏆 Top Performer</div>
                <div className="text-xs text-green-600">{metrics.topPerformer.name}</div>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-green-700">
                  {metrics.topPerformer.score} pts
                </div>
                <div className="text-xs text-green-600">
                  {metrics.topPerformer.accuracy.toFixed(0)}% accuracy
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Survival Predictions */}
      {gameState !== "results" && metrics.survivalPrediction.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-purple-600" />
              Survival Predictions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {metrics.survivalPrediction.slice(0, 3).map((prediction, index) => {
                const player = players.find(p => p.id === prediction.playerId);
                if (!player) return null;

                return (
                  <div key={player.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        #{index + 1} {player.display_name}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress 
                        value={prediction.probability} 
                        className="w-16 h-2" 
                      />
                      <span className="text-sm font-semibold text-purple-600 min-w-[3rem]">
                        {prediction.probability}%
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>  );
};

export default RealTimeAnalytics;
