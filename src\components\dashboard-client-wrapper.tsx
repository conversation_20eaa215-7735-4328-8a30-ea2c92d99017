'use client';

import RoomCards from "@/components/room-cards";

// Define types for room counts
type RoomCounts = {
  easy: number;
  medium: number;
  hard: number;
  "extreme": number;
};

type DashboardClientWrapperProps = {
  initialRoomCounts: RoomCounts;
};

export default function DashboardClientWrapper({ initialRoomCounts }: DashboardClientWrapperProps) {
  return <RoomCards initialRoomCounts={initialRoomCounts} />;
}
