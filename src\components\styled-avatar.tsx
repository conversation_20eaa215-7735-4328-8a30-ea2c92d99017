'use client'

import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import LevelBadge from './level-badge';

interface StyledAvatarProps {
  src?: string | null;
  alt?: string;
  fallback?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  border?: string | null;
  className?: string;
  showLevel?: boolean;
  totalXP?: number;
}

export default function StyledAvatar({
  src,
  alt,
  fallback,
  size = 'md',
  border,
  className,
  showLevel = true,
  totalXP = 0,
}: StyledAvatarProps) {
  const sizeClasses = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
    '2xl': 'h-20 w-20'
  };

  const fallbackSizeClasses = {
    xs: 'text-xs',
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
    '2xl': 'text-xl'
  };

  const borderSizeClasses: Record<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl', { width: number; height: number; transform: string; transformOrigin: string; top: number; left: number; }> = {
    xs: { width: 350, height: 350, transform: `scale(.1)`, transformOrigin: "top left", top: -2, left: -2 },
    sm: { 
      width: 350, 
      height: 350, 
      transform: `scale(.13)`, 
      transformOrigin: "top left", 
      top: -4.5 ,
      left: -4.5 },
    md: { 
      width: 350, 
      height: 350, 
      transform: `scale(.16)`, 
      transformOrigin: "top left", 
      top: -5.5, 
      left: -5.5 },
    lg: { width: 350, 
      height: 350, 
      transform: `scale(.19)`, 
      transformOrigin: "top left", 
      top: -7, 
      left: -6.5 },
    xl: { width: 350, 
      height: 350, 
      transform: `scale(.25)`, 
      transformOrigin: "top left", 
      top: -8, 
      left: -8 },
    '2xl': { 
      width: 350, 
      height: 350, 
      transform: `scale(.32)`, 
      transformOrigin: "top left", 
      top: -12, 
      left: -10.5 }
  };

  const defaultBorder = 'border-2 border-white';
  const appliedBorder = border || defaultBorder;
  const hasGradientBorder = appliedBorder.includes('bg-gradient');

  // Map avatar size to level badge size
  const levelBadgeSize = {
    xs: 'xs' as const,
    sm: 'xs' as const,
    md: 'sm' as const,
    lg: 'md' as const,
    xl: 'lg' as const,
    '2xl': 'lg' as const
  };

  if (hasGradientBorder) {
    return (
      <div className={cn(
        "relative p-1 rounded-full",
        appliedBorder,
        className
      )}>
        <Avatar className={cn(sizeClasses[size])}>
          <AvatarImage src={src || ''} alt={alt} />
          <AvatarFallback className={cn(
            "bg-amber-200 text-amber-800",
            fallbackSizeClasses[size]
          )}>
            {fallback}
          </AvatarFallback>
        </Avatar>
        {showLevel && totalXP > 0 && (
          <LevelBadge
            totalXP={totalXP}
            size={levelBadgeSize[size]}
          />
        )}
      </div>
    );
  }

  return (
    <div>
      <Avatar className={cn(
        sizeClasses[size]
      )}>
        <AvatarImage src={src || ''} alt={alt} />
        <AvatarFallback className={cn(
          "bg-amber-200 text-amber-800",
          fallbackSizeClasses[size]
        )}>
          {fallback}
        </AvatarFallback>
      </Avatar>
      <div 
        style={{
            backgroundImage: `url(${border})`,
            ...borderSizeClasses[size]
        }}
        className="bg-no-repeat absolute"
      />
      {showLevel && totalXP > 0 && (
        <LevelBadge
          totalXP={totalXP}
          size={levelBadgeSize[size]}
        />
      )}
    </div>
  );
}
