"use client";

import { useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";
import { playNotificationSound } from "@/utils/audio-utils";

interface FloatingTextProps {
  text: string;
  type: "success" | "error";
  isVisible: boolean;
  onComplete: () => void;
}

export default function FloatingText({ text, type, isVisible, onComplete }: FloatingTextProps) {
  const [animationClass, setAnimationClass] = useState("");
  const hasTriggered = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    if (isVisible && !hasTriggered.current) {
      hasTriggered.current = true;
      
      // Play notification sound
      playNotificationSound(type, 0.4);
      
      // Start animation immediately
      setAnimationClass("animate-float-in");
      
      // Complete animation after 2 seconds
      timeoutRef.current = setTimeout(() => {
        hasTriggered.current = false;
        onComplete();
      }, 2000);
    } else if (!isVisible) {
      // Reset when animation is not visible
      hasTriggered.current = false;
      setAnimationClass("");
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isVisible, type]); // Removed onComplete from dependencies to prevent re-runs

  if (!isVisible) return null;

  return (
    <>      <style jsx global>{`
        @keyframes float-in {
          0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.3) translateY(20px);
          }
          15% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.5) translateY(10px);
          }
          25% {
            transform: translate(-50%, -50%) scale(0.95) translateY(5px);
          }
          35% {
            transform: translate(-50%, -50%) scale(1.05) translateY(0px);
          }
          45% {
            transform: translate(-50%, -50%) scale(1) translateY(0px);
          }
          75% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) translateY(-30px);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.7) translateY(-60px);
          }
        }
        
        .animate-float-in {
          animation: float-in 2s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }
        
        .floating-text-success {
          background: linear-gradient(45deg, #10b981, #059669, #22c55e);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          filter: drop-shadow(0 0 8px rgba(16, 185, 129, 0.4));
        }
        
        .floating-text-error {
          background: linear-gradient(45deg, #ef4444, #dc2626, #f87171);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          filter: drop-shadow(0 0 8px rgba(239, 68, 68, 0.4));
        }
      `}</style>
        <div
        className={cn(
          "fixed top-1/2 left-1/2 z-50 pointer-events-none select-none",
          "text-7xl font-bold",
          animationClass,
          type === "success" ? "floating-text-success" : "floating-text-error"
        )}
        style={{
          textShadow: type === "success" 
            ? "3px 3px 6px rgba(0,0,0,0.4), 0 0 15px rgba(34, 197, 94, 0.4), 0 0 25px rgba(34, 197, 94, 0.2)" 
            : "3px 3px 6px rgba(0,0,0,0.4), 0 0 15px rgba(239, 68, 68, 0.4), 0 0 25px rgba(239, 68, 68, 0.2)",
          fontFamily: "system-ui, -apple-system, sans-serif",
          fontWeight: "900",
          WebkitTextStroke: type === "success" ? "1px rgba(34, 197, 94, 0.3)" : "1px rgba(239, 68, 68, 0.3)"
        }}
      >
        {text}
      </div>
    </>
  );
}
