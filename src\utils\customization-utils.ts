// Customization utilities for player items and unlocks
import { createClient } from "../../supabase/client";

export interface PlayerItem {
  id: string;
  name: string;
  description?: string;
  type: 'avatar_border' | 'background' | 'avatar' | 'title';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  value: string;
  unlock_requirement: {
    type: 'level' | 'battlepass' | 'achievement' | 'rp';
    value: number | string;
  } | null;
  is_default: boolean;
  image_url?: string | null;
  is_animated?: boolean | null;
}

export interface PlayerInventoryItem extends PlayerItem {
  unlocked_at: string;
}

export const RARITY_COLORS = {
  common: 'text-gray-600 border-gray-300',
  rare: 'text-blue-600 border-blue-300',
  epic: 'text-purple-600 border-purple-300',
  legendary: 'text-amber-600 border-amber-300'
};

export const AVATAR_BORDERS = {
  none: '',
  bronze: 'ring-2 ring-amber-700',
  silver: 'ring-2 ring-gray-400',
  gold: 'ring-2 ring-amber-400',
  platinum: 'ring-2 ring-blue-400',
  diamond: 'ring-2 ring-cyan-400',
  champion: 'ring-4 ring-gradient-to-r from-amber-400 to-purple-600'
};

export const BACKGROUND_THEMES = {
  default: 'bg-gradient-to-br from-amber-100 via-amber-50 to-amber-100',
  forest: 'bg-gradient-to-br from-green-200 via-green-100 to-emerald-100',
  ocean: 'bg-gradient-to-br from-blue-200 via-cyan-100 to-blue-100',
  sunset: 'bg-gradient-to-br from-orange-200 via-red-100 to-pink-100',
  galaxy: 'bg-gradient-to-br from-purple-200 via-indigo-100 to-blue-100',
  royal: 'bg-gradient-to-br from-purple-300 via-purple-100 to-amber-100'
};

// Get all available items of a specific type
export async function getAvailableItems(type: PlayerItem['type']): Promise<PlayerItem[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('player_items')
    .select('*')
    .eq('type', type)
    .order('rarity', { ascending: false });
    
  if (error) {
    console.error('Error fetching available items:', error);
    return [];
  }
  
  return data || [];
}

// Get player's inventory for a specific type
export async function getPlayerInventory(playerId: string, type?: PlayerItem['type']): Promise<PlayerInventoryItem[]> {
  const supabase = createClient();

  // First ensure player has default items unlocked
  await ensureDefaultItemsUnlocked(playerId);

  let query = supabase
    .from('player_item_inventory')
    .select(`
      unlocked_at,
      player_items (
        id,
        name,
        description,
        type,
        rarity,
        value,
        unlock_requirement,
        is_default
      )
    `)
    .eq('player_id', playerId);

  if (type) {
    query = query.eq('player_items.type', type);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching player inventory:', error);
    return [];
  }  return data?.map(item => {
    if (!item.player_items) {
      return null; // Skip items with no player_items
    }

    const playerItem = Array.isArray(item.player_items) ? item.player_items[0] : item.player_items;
    return {
      ...playerItem,
      unlocked_at: item.unlocked_at
    } as PlayerInventoryItem;
  }).filter(item => item !== null) || [];
}

// Ensure player has all default items unlocked
export async function ensureDefaultItemsUnlocked(playerId: string): Promise<void> {
  const supabase = createClient();

  try {
    // Get all default items
    const { data: defaultItems } = await supabase
      .from('player_items')
      .select('id')
      .eq('is_default', true);

    if (!defaultItems || defaultItems.length === 0) return;

    // Get player's current inventory
    const { data: currentInventory } = await supabase
      .from('player_item_inventory')
      .select('item_id')
      .eq('player_id', playerId);

    const unlockedItemIds = new Set(currentInventory?.map(item => item.item_id) || []);

    // Find default items that aren't unlocked yet
    const itemsToUnlock = defaultItems.filter(item => !unlockedItemIds.has(item.id));

    if (itemsToUnlock.length > 0) {
      // Unlock missing default items
      const unlockData = itemsToUnlock.map(item => ({
        player_id: playerId,
        item_id: item.id
      }));

      await supabase
        .from('player_item_inventory')
        .insert(unlockData);
    }
  } catch (error) {
    console.error('Error ensuring default items unlocked:', error);
  }
}

// Check if player has unlocked specific item
export async function hasUnlockedItem(playerId: string, itemId: string): Promise<boolean> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('player_item_inventory')
    .select('item_id')
    .eq('player_id', playerId)
    .eq('item_id', itemId)
    .single();
    
  return !error && !!data;
}

// Unlock item for player
export async function unlockItem(playerId: string, itemId: string): Promise<boolean> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('player_item_inventory')
    .insert({
      player_id: playerId,
      item_id: itemId
    });
    
  if (error) {
    console.error('Error unlocking item:', error);
    return false;
  }
  
  return true;
}

// Equip item for player
export async function equipItem(playerId: string, itemId: string, type: PlayerItem['type']): Promise<boolean> {
  const supabase = createClient();
  
  const columnMap = {
    avatar_border: 'equipped_avatar_border_id',
    background: 'equipped_background_id',
    title: 'equipped_title_id',
    avatar: 'equipped_avatar_id'
  };
  
  const column = columnMap[type];
  if (!column) return false;
  
  const { error } = await supabase
    .from('players')
    .update({ [column]: itemId })
    .eq('id', playerId);
    
  if (error) {
    console.error('Error equipping item:', error);
    return false;
  }
  
  return true;
}

// Check unlock requirements
export function checkUnlockRequirement(
  requirement: PlayerItem['unlock_requirement'], 
  playerLevel: number, 
  playerRP: number,
  hasBattlePass: boolean,
  battlePassTier: number
): boolean {
  if (!requirement) return true; // No requirement means always unlocked
  
  switch (requirement.type) {
    case 'level':
      return playerLevel >= (requirement.value as number);
    case 'rp':
      return playerRP >= (requirement.value as number);
    case 'battlepass':
      return hasBattlePass && battlePassTier >= (requirement.value as number);
    case 'achievement':
      // This would need to be implemented based on your achievement system
      return false;
    default:
      return false;
  }
}

// Seed default items
export async function seedDefaultItems(): Promise<{ success: boolean; message: string; itemsAdded?: number }> {
  const supabase = createClient();
  
  try {
    const defaultItems: Omit<PlayerItem, 'id'>[] = [
      // Avatar Borders
      {
        name: 'No Border',
        description: 'Clean and simple look',
        type: 'avatar_border',
        rarity: 'common',
        value: 'none',
        unlock_requirement: null,
        is_default: true
      },
      {
        name: 'Bronze Border',
        description: 'A warm bronze frame for your avatar',
        type: 'avatar_border',
        rarity: 'common',
        value: 'bronze',
        unlock_requirement: { type: 'level', value: 5 },
        is_default: false
      },
      {
        name: 'Silver Border',
        description: 'Sleek silver border for dedicated players',
        type: 'avatar_border',
        rarity: 'rare',
        value: 'silver',
        unlock_requirement: { type: 'level', value: 15 },
        is_default: false
      },
      {
        name: 'Gold Border',
        description: 'Prestigious gold border for high achievers',
        type: 'avatar_border',
        rarity: 'epic',
        value: 'gold',
        unlock_requirement: { type: 'rp', value: 1000 },
        is_default: false
      },
      {
        name: 'Champion Border',
        description: 'Legendary border for true champions',
        type: 'avatar_border',
        rarity: 'legendary',
        value: 'champion',
        unlock_requirement: { type: 'battlepass', value: 10 },
        is_default: false
      },
      
      // Backgrounds
      {
        name: 'Default Theme',
        description: 'The classic Word Nook experience',
        type: 'background',
        rarity: 'common',
        value: 'default',
        unlock_requirement: null,
        is_default: true
      },
      {
        name: 'Forest Theme',
        description: 'Embrace nature with earthy green tones',
        type: 'background',
        rarity: 'rare',
        value: 'forest',
        unlock_requirement: { type: 'level', value: 10 },
        is_default: false
      },
      {
        name: 'Ocean Theme',
        description: 'Dive deep with calming blue waters',
        type: 'background',
        rarity: 'rare',
        value: 'ocean',
        unlock_requirement: { type: 'rp', value: 500 },
        is_default: false
      },
      {
        name: 'Galaxy Theme',
        description: 'Explore the cosmos with stellar colors',
        type: 'background',
        rarity: 'legendary',
        value: 'galaxy',
        unlock_requirement: { type: 'battlepass', value: 5 },
        is_default: false
      },
      
      // Titles
      {
        name: 'Word Wanderer',
        description: 'For those who love exploring vocabulary',
        type: 'title',
        rarity: 'common',
        value: 'Word Wanderer',
        unlock_requirement: null,
        is_default: true
      },
      {
        name: 'Spelling Sage',
        description: 'Wisdom comes with perfect spelling',
        type: 'title',
        rarity: 'rare',
        value: 'Spelling Sage',
        unlock_requirement: { type: 'level', value: 20 },
        is_default: false
      },
      {
        name: 'Lexicon Legend',
        description: 'Master of words and language',
        type: 'title',
        rarity: 'epic',
        value: 'Lexicon Legend',
        unlock_requirement: { type: 'rp', value: 2000 },
        is_default: false
      }
    ];
    
    // Check if items already exist to avoid duplicates
    const { data: existingItems } = await supabase
      .from('player_items')
      .select('name')
      .in('name', defaultItems.map(item => item.name));
    
    const existingItemNames = new Set(existingItems?.map(item => item.name) || []);
    const newItems = defaultItems.filter(item => !existingItemNames.has(item.name));
    
    if (newItems.length === 0) {
      return {
        success: true,
        message: 'All default items already exist in the database.',
        itemsAdded: 0
      };
    }
    
    const { error } = await supabase
      .from('player_items')
      .insert(newItems);
      
    if (error) {
      console.error('Error seeding default items:', error);
      return {
        success: false,
        message: `Error seeding items: ${error.message}`
      };
    }
    
    return {
      success: true,
      message: `Successfully added ${newItems.length} new default items to the database.`,
      itemsAdded: newItems.length
    };
    
  } catch (error) {
    console.error('Unexpected error seeding default items:', error);
    return {
      success: false,
      message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// Image-based item utilities
export function getItemImageUrl(item: PlayerItem): string | null {
  // If item has an image_url, use it
  if (item.image_url) {
    return item.image_url;
  }
  
  // For backward compatibility, generate CSS-based preview for older items
  return null;
}

export function isImageBasedItem(item: PlayerItem): boolean {
  return !!(item.image_url);
}

export function getItemPreviewStyle(item: PlayerItem): string {
  // For items without image_url, use CSS classes (backward compatibility)
  if (!item.image_url) {
    switch (item.type) {
      case 'avatar_border':
        return AVATAR_BORDERS[item.value as keyof typeof AVATAR_BORDERS] || '';
      case 'background':
        return BACKGROUND_THEMES[item.value as keyof typeof BACKGROUND_THEMES] || '';
      default:
        return '';
    }
  }
  
  return '';
}

// Image validation utilities
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload JPG, PNG, GIF, or WebP images.'
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Please upload images smaller than 5MB.'
    };
  }
  
  return { valid: true };
}

export function getImagePath(type: PlayerItem['type'], filename: string): string {
  const typeFolder = type === 'avatar_border' ? 'avatar_borders' : `${type}s`;
  return `/items/${typeFolder}/${filename}`;
}

// Create item with image
export async function createItemWithImage(
  itemData: Omit<PlayerItem, 'id'>,
  imageFile?: File
): Promise<{ success: boolean; error?: string; itemId?: string }> {
  const supabase = createClient();
  
  try {
    // Validate image if provided
    if (imageFile) {
      const validation = validateImageFile(imageFile);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }
    }
    
    // Create the item record
    const { data: item, error: insertError } = await supabase
      .from('player_items')
      .insert([itemData])
      .select()
      .single();
    
    if (insertError) {
      return { success: false, error: insertError.message };
    }
    
    return { success: true, itemId: item.id };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

// Update item with new image
export async function updateItemImage(
  itemId: string,
  imageUrl: string,
  isAnimated: boolean = false
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();
  
  try {
    const { error } = await supabase
      .from('player_items')
      .update({ 
        image_url: imageUrl,
        is_animated: isAnimated
      })
      .eq('id', itemId);
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

// Get player's equipped items with images
export async function getPlayerEquippedItems(playerId: string): Promise<{
  avatar?: PlayerItem;
  avatarBorder?: PlayerItem;
  background?: PlayerItem;
  title?: PlayerItem;
}> {
  const supabase = createClient();
  
  try {
    const { data: player, error } = await supabase
      .from('players')
      .select(`
        equipped_avatar_id,
        equipped_avatar_border_id,
        equipped_background_id,
        equipped_title_id,
        avatar_items:equipped_avatar_id(id, name, description, type, rarity, value, unlock_requirement, is_default, image_url, is_animated),
        border_items:equipped_avatar_border_id(id, name, description, type, rarity, value, unlock_requirement, is_default, image_url, is_animated),
        background_items:equipped_background_id(id, name, description, type, rarity, value, unlock_requirement, is_default, image_url, is_animated),
        title_items:equipped_title_id(id, name, description, type, rarity, value, unlock_requirement, is_default, image_url, is_animated)
      `)
      .eq('id', playerId)
      .single();
    
    if (error || !player) {
      console.error('Error fetching equipped items:', error);
      return {};
    }
    
    return {
      avatar: Array.isArray(player.avatar_items) ? player.avatar_items[0] : player.avatar_items,
      avatarBorder: Array.isArray(player.border_items) ? player.border_items[0] : player.border_items,
      background: Array.isArray(player.background_items) ? player.background_items[0] : player.background_items,
      title: Array.isArray(player.title_items) ? player.title_items[0] : player.title_items
    };
  } catch (error) {
    console.error('Error fetching equipped items:', error);
    return {};
  }
}

// Bulk operations for admin
export async function bulkUpdateItems(
  itemUpdates: Array<{ id: string; updates: Partial<PlayerItem> }>
): Promise<{ success: boolean; error?: string; updatedCount?: number }> {
  const supabase = createClient();
  
  try {
    let updatedCount = 0;
    
    for (const { id, updates } of itemUpdates) {
      const { error } = await supabase
        .from('player_items')
        .update(updates)
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      updatedCount++;
    }
    
    return { success: true, updatedCount };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

// Delete item and cleanup
export async function deleteItem(itemId: string): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();
  
  try {
    // First, unequip the item from all players
    const { error: unequipError } = await supabase
      .from('players')
      .update({
        equipped_avatar_id: null,
        equipped_avatar_border_id: null,
        equipped_background_id: null,
        equipped_title_id: null
      })
      .or(`equipped_avatar_id.eq.${itemId},equipped_avatar_border_id.eq.${itemId},equipped_background_id.eq.${itemId},equipped_title_id.eq.${itemId}`);
    
    if (unequipError) {
      console.warn('Warning: Error unequipping item:', unequipError);
    }
    
    // Remove from player inventories
    const { error: inventoryError } = await supabase
      .from('player_item_inventory')
      .delete()
      .eq('item_id', itemId);
    
    if (inventoryError) {
      console.warn('Warning: Error removing from inventories:', inventoryError);
    }
    
    // Finally, delete the item
    const { error: deleteError } = await supabase
      .from('player_items')
      .delete()
      .eq('id', itemId);
    
    if (deleteError) {
      return { success: false, error: deleteError.message };
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}
