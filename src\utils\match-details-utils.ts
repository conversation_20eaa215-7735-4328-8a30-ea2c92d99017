import { createClient } from "../../supabase/client";

export interface MatchPlayerDetails {
  player_id: string;
  display_name: string;
  avatar_url?: string;
  score: number;
  final_rank: number;
  elimination_round?: number;
  rp_gained?: number;
  rp_before_match?: number;
  rp_after_match?: number;
  correct_answers?: number;
  total_answers?: number;
  accuracy_percentage?: number;
  level_experience?: number;
  equipped_avatar?: string;
  equipped_avatar_border?: string;
}

export interface DetailedMatchHistory {
  match_id: string;
  difficulty: string;
  current_round: number;
  match_duration_seconds: number;
  created_at: string;
  ended_at: string;
  total_players: number;
  players: MatchPlayerDetails[];
  user_rank?: number;
  user_score?: number;
  match_statistics: {
    highest_score: number;
    lowest_score: number;
    average_score: number;
    total_eliminations: number;
    completion_rate: number;
  };
}

export interface MatchComparison {
  vs_average: {
    score_difference: number;
    rank_percentile: number;
    performance_rating: 'Excellent' | 'Good' | 'Average' | 'Below Average' | 'Poor';
  };
  vs_best_player: {
    score_gap: number;
    rank_difference: number;
  };
  player_insights: string[];
}

/**
 * Get detailed match history with all players and their performance data
 */
export async function getDetailedMatchHistory(
  userId: string, 
  limit: number = 15
): Promise<DetailedMatchHistory[]> {
  const supabase = createClient();

  try {
    // First, get ALL user's matches to manage history size
    const { data: allUserMatches, error: allMatchesError } = await supabase
      .from('match_history_players')
      .select(`
        match_id,
        difficulty,
        created_at,
        match_histories (
          current_round,
          match_duration_seconds,
          ended_at,
          created_at
        )
      `)
      .eq('player_id', userId)
      .order('created_at', { ascending: false });

    if (allMatchesError || !allUserMatches) {
      console.error('Error fetching all user matches:', allMatchesError);
      return [];
    }

    // If user has more than 16 matches, delete the oldest ones
    if (allUserMatches.length > 16) {
      const matchesToDelete = allUserMatches.slice(16);
      
      // Delete oldest match records from match_history_players
      const matchIdsToDelete = matchesToDelete.map(match => match.match_id);
      
      const { error: deleteError } = await supabase
        .from('match_history_players')
        .delete()
        .eq('player_id', userId)
        .in('match_id', matchIdsToDelete);

      if (deleteError) {
        console.error('Error deleting old matches:', deleteError);
        // Continue with the function even if deletion fails
      } else {
        console.log(`Deleted ${matchesToDelete.length} old match records for user ${userId}`);
      }
    }

    // Get the matches to return based on limit (from the kept matches)
    const matchesToReturn = allUserMatches.slice(0, Math.min(limit, 16));

    // Get detailed data for each match to return
    const detailedMatches = await Promise.all(
      matchesToReturn.map(async (match: any) => {
        return await getMatchDetails(match.match_id, userId);
      })
    );

    return detailedMatches.filter((match): match is DetailedMatchHistory => match !== null);
  } catch (error) {
    console.error('Exception in getDetailedMatchHistory:', error);
    return [];
  }
}

/**
 * Get complete details for a specific match including all players
 */
export async function getMatchDetails(
  matchId: string, 
  currentUserId?: string
): Promise<DetailedMatchHistory | null> {
  const supabase = createClient();

  try {
    // Get match basic info
    const { data: matchInfo, error: matchError } = await supabase
      .from('match_histories')
      .select('*')
      .eq('id', matchId)
      .single();

    if (matchError || !matchInfo) {
      console.error('Error fetching match info:', matchError);
      return null;
    }

    // Get all players in this match with their performance data
    const { data: playersData, error: playersError } = await supabase
      .from('match_history_players')
      .select(`
        player_id,
        score,
        final_rank,
        elimination_round,
        rp_gained,
        rp_before_match,
        rp_after_match,
        players (
          id,
          display_name,
          avatar_url,
          level_experience,
          equipped_avatar_id,
          equipped_avatar:equipped_avatar_id(image_url),
          equipped_avatar_border_id,
          equipped_border:equipped_avatar_border_id(image_url)
        )
      `)
      .eq('match_id', matchId)
      .order('final_rank', { ascending: true });

    if (playersError || !playersData) {
      console.error('Error fetching players data:', playersError);
      return null;
    }

    // Transform players data
    const players: MatchPlayerDetails[] = playersData.map((player: any) => ({
      player_id: player.player_id,
      display_name: player.players?.display_name || 'Unknown Player',
      avatar_url: player.players?.avatar_url,
      score: player.score || 0,
      final_rank: player.final_rank || 0,
      elimination_round: player.elimination_round,
      rp_gained: player.rp_gained,
      rp_before_match: player.rp_before_match,
      rp_after_match: player.rp_after_match,
      level_experience: player.players?.level_experience || 0,
      accuracy_percentage: 0, // We'll calculate this if needed
      equipped_avatar: player.players?.equipped_avatar?.image_url,
      equipped_avatar_border: player.players?.equipped_border?.image_url
    }));

    // Calculate match statistics
    const scores = players.map(p => p.score);
    const eliminations = players.filter(p => p.elimination_round && p.elimination_round > 0).length;
    
    const match_statistics = {
      highest_score: Math.max(...scores),
      lowest_score: Math.min(...scores),
      average_score: Math.round(scores.reduce((a, b) => a + b, 0) / scores.length),
      total_eliminations: eliminations,
      completion_rate: Math.round(((players.length - eliminations) / players.length) * 100)
    };

    // Find current user's performance
    const userPlayer = currentUserId ? players.find(p => p.player_id === currentUserId) : null;

    return {
      match_id: matchId,
      difficulty: matchInfo.difficulty,
      current_round: matchInfo.current_round,
      match_duration_seconds: matchInfo.match_duration_seconds,
      created_at: matchInfo.created_at,
      ended_at: matchInfo.ended_at,
      total_players: players.length,
      players,
      user_rank: userPlayer?.final_rank,
      user_score: userPlayer?.score,
      match_statistics
    };
  } catch (error) {
    console.error('Exception in getMatchDetails:', error);
    return null;
  }
}

/**
 * Get performance comparison for a user in a specific match
 */
export async function getMatchComparison(
  matchId: string, 
  userId: string
): Promise<MatchComparison | null> {
  const supabase = createClient();

  try {
    const matchDetails = await getMatchDetails(matchId, userId);
    if (!matchDetails) return null;

    const userPlayer = matchDetails.players.find(p => p.player_id === userId);
    if (!userPlayer) return null;

    const { match_statistics } = matchDetails;
    const bestPlayer = matchDetails.players[0]; // First player (rank 1)

    // Calculate percentile
    const rank_percentile = ((matchDetails.total_players - userPlayer.final_rank + 1) / matchDetails.total_players) * 100;

    // Determine performance rating
    let performance_rating: 'Excellent' | 'Good' | 'Average' | 'Below Average' | 'Poor';
    if (rank_percentile >= 90) performance_rating = 'Excellent';
    else if (rank_percentile >= 70) performance_rating = 'Good';
    else if (rank_percentile >= 50) performance_rating = 'Average';
    else if (rank_percentile >= 30) performance_rating = 'Below Average';
    else performance_rating = 'Poor';

    // Generate insights
    const player_insights: string[] = [];
    
    if (userPlayer.final_rank === 1) {
      player_insights.push("🏆 Perfect victory! You dominated this match.");
    } else if (userPlayer.final_rank <= 3) {
      player_insights.push("🥉 Great performance! You finished in the top 3.");
    }
    
    if (userPlayer.score > match_statistics.average_score * 1.2) {
      player_insights.push("⭐ Outstanding score - 20% above average!");
    } else if (userPlayer.score > match_statistics.average_score) {
      player_insights.push("👍 Above average performance");
    }
    
    if (!userPlayer.elimination_round) {
      player_insights.push("💪 Survived until the end!");
    } else if (userPlayer.elimination_round && userPlayer.elimination_round > matchDetails.current_round * 0.8) {
      player_insights.push("🔥 Strong endurance - lasted most of the match");
    }

    if (userPlayer.rp_gained && userPlayer.rp_gained > 0) {
      player_insights.push(`📈 Gained ${userPlayer.rp_gained} RP from this match`);
    } else if (userPlayer.rp_gained && userPlayer.rp_gained < 0) {
      player_insights.push(`📉 Lost ${Math.abs(userPlayer.rp_gained)} RP from this match`);
    }

    return {
      vs_average: {
        score_difference: userPlayer.score - match_statistics.average_score,
        rank_percentile: Math.round(rank_percentile),
        performance_rating
      },
      vs_best_player: {
        score_gap: bestPlayer.score - userPlayer.score,
        rank_difference: userPlayer.final_rank - 1
      },
      player_insights
    };
  } catch (error) {
    console.error('Exception in getMatchComparison:', error);
    return null;
  }
}

/**
 * Get match leaderboard for real-time display during matches
 */
export async function getMatchLeaderboard(matchId: string): Promise<MatchPlayerDetails[]> {
  const supabase = createClient();

  try {
    const { data: playersData, error } = await supabase
      .from('match_players')
      .select(`
        player_id,
        score,
        lives,
        current_streak,
        longest_streak,
        correct_answers,
        total_answers,
        is_spectator,
        players (
          id,
          display_name,
          avatar_url,
          avatar_border,
          level_experience
        )
      `)
      .eq('match_id', matchId)
      .order('score', { ascending: false });

    if (error || !playersData) {
      console.error('Error fetching match leaderboard:', error);
      return [];
    }

    return playersData.map((player: any, index: number) => ({
      player_id: player.player_id,
      display_name: player.players?.display_name || 'Unknown Player',
      avatar_url: player.players?.avatar_url,
      avatar_border: player.players?.avatar_border,
      score: player.score || 0,
      final_rank: index + 1, // Current rank based on score
      lives: player.lives,
      current_streak: player.current_streak,
      longest_streak: player.longest_streak,
      correct_answers: player.correct_answers || 0,
      total_answers: player.total_answers || 0,
      accuracy_percentage: player.total_answers > 0 
        ? Math.round((player.correct_answers / player.total_answers) * 100) 
        : 0,
      is_spectator: player.is_spectator,
      level_experience: player.players?.level_experience || 0
    }));
  } catch (error) {
    console.error('Exception in getMatchLeaderboard:', error);
    return [];
  }
}

/**
 * Format match duration for display
 */
export function formatMatchDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes === 0) {
    return `${remainingSeconds}s`;
  } else if (minutes < 60) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }
}

/**
 * Get difficulty color class for styling
 */
export function getDifficultyColor(difficulty: string): string {
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'medium':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'hard':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'extreme':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

/**
 * Get rank display with appropriate styling
 */
export function getRankDisplay(rank: number): string {
  if (rank === 1) return '🥇 1st';
  if (rank === 2) return '🥈 2nd';
  if (rank === 3) return '🥉 3rd';
  return `#${rank}`;
}
