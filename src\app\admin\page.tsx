'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Package, 
  Crown, 
  Users, 
  Database, 
  BarChart3, 
  Shield, 
  FileText,
  ChevronRight,
  Sparkles,
  MessageSquare,
  Flag,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { getAdminStats, getRecentActivity, AdminStats } from '@/utils/admin-stats-utils';
import { toast } from 'sonner';

const adminPages = [
  {
    title: 'Seed Database',
    description: 'Initialize the database with default items and battle pass content',
    icon: Database,
    href: '/admin/seed',
    color: 'bg-green-50 border-green-200 hover:bg-green-100',
    iconColor: 'text-green-600',
    status: 'Ready'
  },
  {
    title: 'Item Management',
    description: 'Manage customization items, avatar borders, backgrounds, and titles',
    icon: Package,
    href: '/admin/items',
    color: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
    iconColor: 'text-blue-600',
    status: 'Active'
  },
  {
    title: 'Feedback Management',
    description: 'Review and respond to user feedback, bug reports, and feature requests',
    icon: MessageSquare,
    href: '/admin/feedback',
    color: 'bg-amber-50 border-amber-200 hover:bg-amber-100',
    iconColor: 'text-amber-600',
    status: 'Active'
  },
  {
    title: 'Report Management',
    description: 'Handle player reports and apply moderation actions',
    icon: Flag,
    href: '/admin/reports',
    color: 'bg-red-50 border-red-200 hover:bg-red-100',
    iconColor: 'text-red-600',
    status: 'Active'
  },  {
    title: 'Battle Pass Management',
    description: 'Configure battle pass seasons, tiers, and rewards',
    icon: Crown,
    href: '/admin/battle-pass',
    color: 'bg-purple-50 border-purple-200 hover:bg-purple-100',
    iconColor: 'text-purple-600',
    status: 'Active'
  },
  {
    title: 'User Management',
    description: 'View and manage player accounts, stats, and progress',
    icon: Users,
    href: '/admin/users',
    color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100',
    iconColor: 'text-indigo-600',
    status: 'Coming Soon'
  },
  {
    title: 'Analytics',
    description: 'View game statistics, player metrics, and performance data',
    icon: BarChart3,
    href: '/admin/analytics',
    color: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100',
    iconColor: 'text-cyan-600',
    status: 'Coming Soon'
  },
  {
    title: 'System Settings',
    description: 'Configure global settings, maintenance mode, and system preferences',
    icon: Settings,
    href: '/admin/settings',
    color: 'bg-gray-50 border-gray-200 hover:bg-gray-100',
    iconColor: 'text-gray-600',
    status: 'Coming Soon'
  }
];

export default function AdminPage() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [statsData, activityData] = await Promise.all([
        getAdminStats(),
        getRecentActivity(5)
      ]);
      
      setStats(statsData);
      setRecentActivity(activityData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const past = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - past.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <Shield className="h-8 w-8 text-amber-600" />
              <h1 className="text-3xl font-bold text-amber-900">Admin Dashboard</h1>
              <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
                Overview
              </Badge>
            </div>
            <p className="text-amber-700 text-lg">
              Welcome to the Word Nook administration panel. Monitor your game server and manage content.
            </p>
          </div>
          <Button variant="outline" onClick={loadDashboardData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <Card className="bg-white/70 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Feedback</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? '--' : stats?.feedback.total || 0}
                  </p>
                  {stats && stats.feedback.pending > 0 && (
                    <p className="text-xs text-orange-600">
                      {stats.feedback.pending} pending
                    </p>
                  )}
                </div>
                <MessageSquare className="h-8 w-8 text-amber-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/70 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Player Reports</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? '--' : stats?.reports.total || 0}
                  </p>
                  {stats && stats.reports.pending > 0 && (
                    <p className="text-xs text-red-600">
                      {stats.reports.pending} pending
                    </p>
                  )}
                </div>
                <Flag className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/70 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Matches</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? '--' : stats?.matches.active || 0}
                  </p>
                  <p className="text-xs text-blue-600">
                    {stats?.matches.completed_today || 0} completed today
                  </p>
                </div>
                <Crown className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/70 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Players</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? '--' : stats?.players.total || 0}
                  </p>
                  {stats && stats.players.restricted > 0 && (
                    <p className="text-xs text-orange-600">
                      {stats.players.restricted} restricted
                    </p>
                  )}
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Battle Pass</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? '--' : stats?.battlePass?.active_seasons || 0}
                  </p>
                  <p className="text-xs text-purple-600">
                    {stats?.battlePass?.premium_players || 0} premium players
                  </p>
                </div>
                <Sparkles className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Admin Pages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {adminPages.map((page) => {
            const Icon = page.icon;
            const isAvailable = page.status !== 'Coming Soon';
            
            return (
              <Card 
                key={page.href} 
                className={`transition-all duration-200 ${
                  isAvailable 
                    ? `${page.color} cursor-pointer hover:shadow-lg transform hover:scale-105` 
                    : 'bg-gray-50 border-gray-200 opacity-75'
                }`}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Icon className={`h-6 w-6 ${isAvailable ? page.iconColor : 'text-gray-400'}`} />
                    <Badge 
                      variant={
                        page.status === 'Active' ? 'default' : 
                        page.status === 'Ready' ? 'secondary' : 
                        'outline'
                      }
                      className={
                        page.status === 'Active' ? 'bg-green-100 text-green-800' :
                        page.status === 'Ready' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-600'
                      }
                    >
                      {page.status}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    {page.title}
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    {page.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isAvailable ? (
                    <Link href={page.href}>
                      <Button className="w-full group" variant="outline">
                        Access Panel
                        <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  ) : (
                    <Button className="w-full" variant="outline" disabled>
                      Coming Soon
                    </Button>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>        {/* Recent Activity */}
        <Card className="mt-8 bg-white/70 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-amber-600" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest administrative actions and system events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {loading ? (
                <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-lg">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-600"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">Loading recent activity...</p>
                  </div>
                </div>
              ) : recentActivity.length === 0 ? (
                <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-lg">
                  <Sparkles className="h-4 w-4 text-amber-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Admin panel initialized</p>
                    <p className="text-xs text-gray-600">Welcome to the Word Nook administration interface</p>
                  </div>
                  <span className="text-xs text-gray-500">Just now</span>
                </div>
              ) : (
                recentActivity.map((activity, index) => {
                  const getActivityIcon = () => {
                    switch (activity.type) {
                      case 'feedback': return <MessageSquare className="h-4 w-4 text-amber-600" />;
                      case 'report': return <Flag className="h-4 w-4 text-red-600" />;
                      case 'player_join': return <Users className="h-4 w-4 text-blue-600" />;
                      case 'match': return <Crown className="h-4 w-4 text-purple-600" />;
                      default: return <Sparkles className="h-4 w-4 text-gray-600" />;
                    }
                  };

                  const getActivityBg = () => {
                    switch (activity.type) {
                      case 'feedback': return 'bg-amber-50';
                      case 'report': return 'bg-red-50';
                      case 'player_join': return 'bg-blue-50';
                      case 'match': return 'bg-purple-50';
                      default: return 'bg-gray-50';
                    }
                  };

                  return (
                    <div key={`${activity.type}-${activity.id}-${index}`} className={`flex items-center gap-3 p-3 ${getActivityBg()} rounded-lg`}>
                      {getActivityIcon()}
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                        <p className="text-xs text-gray-600">{activity.description}</p>
                        {activity.priority === 'critical' && (
                          <Badge variant="destructive" className="text-xs mt-1">Critical</Badge>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                    </div>
                  );
                })
              )}
              
              {!loading && recentActivity.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <p className="text-sm">No recent activity to display</p>
                  <p className="text-xs">Administrative actions will appear here</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>Word Nook Admin Panel v1.0 • Built with Next.js & Supabase</p>
        </div>
      </div>
    );
  }
