export type match_players_status = "active" | "left" | "eliminated" | "spectator";

export interface Player {
    id: string;
    display_name: string;
    avatar_url?: string;
    avatar_border?: string;
    lives?: number;
    score?: number;
    is_spectator?: boolean;
    has_answered?: boolean;
    lastAnswer?: string;
    longest_streak?: number;
    current_streak?: number;
    total_answers?: number;
    correct_answers?: number;
    responseTimes?: number[];
    status?: match_players_status;
    left_at?: string;
    image_url?: string;
}

export interface User {
    id: string;
    email?: string;
    user_metadata?: {
        [key: string]: any;
    };
    // Add other properties from Supabase user object as needed
}

export interface Match {
    id: string;
    current_state: GameStateType;
    difficulty: Difficulty;
    players: Player[];
    word_list: string[];
    current_word_index: number;
    start_time?: string;
    end_time?: string;
    host_id: string;
    room_id: string;
}

export type GameStateType = "spelling" | "break" | "gameOver" | "results" | "waiting";

export type Difficulty = 'easy' | 'medium' | 'hard' | 'extreme';

export interface currentWord {
    id: number;
    text: string;
    difficulty: Difficulty;
    audioUrl?: string;
}