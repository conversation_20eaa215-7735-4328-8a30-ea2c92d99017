'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { 
  Package, 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Palette,
  Frame,
  Type,
  Save,
  X,
  Upload,
  Image as ImageIcon,
  Copy
} from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '../../../../supabase/client';
import { cn } from '@/lib/utils';
import {
  PlayerItem,
  RARITY_COLORS,
  AVATAR_BORDERS,
  BACKGROUND_THEMES,
  getAvailableItems,
  getItemImageUrl,
  isImageBasedItem,
  getItemPreviewStyle,
  validateImageFile,
  createItemWithImage,
  updateItemImage,
  deleteItem
} from '@/utils/customization-utils';

type ItemFormData = {
  name: string;
  description: string;
  type: PlayerItem['type'];
  rarity: PlayerItem['rarity'];
  value: string;
  unlock_requirement: {
    type: 'level' | 'battlepass' | 'achievement' | 'rp';
    value: number | string;
  } | null;
  is_default: boolean;
  image_url?: string | null;
  is_animated?: boolean | null;
};

export default function ItemManagementPage() {
  const [items, setItems] = useState<Record<string, PlayerItem[]>>({});
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRarity, setFilterRarity] = useState<string>('all');  const [activeTab, setActiveTab] = useState<PlayerItem['type']>('avatar');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<PlayerItem | null>(null);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [formData, setFormData] = useState<ItemFormData>({
    name: '',
    description: '',
    type: 'avatar',
    rarity: 'common',
    value: '',
    unlock_requirement: null,
    is_default: false,
    image_url: null,
    is_animated: false
  });

  const supabase = createClient();

  useEffect(() => {
    loadItems();
  }, []);

  const loadItems = async () => {
    setLoading(true);    try {
      const types: PlayerItem['type'][] = ['avatar', 'avatar_border', 'background', 'title'];
      const itemsData: Record<string, PlayerItem[]> = {};

      for (const type of types) {
        itemsData[type] = await getAvailableItems(type);
      }

      setItems(itemsData);
    } catch (error) {
      console.error('Error loading items:', error);
      toast.error('Failed to load items');
    } finally {
      setLoading(false);
    }
  };  const handleAddItem = async () => {
    try {
      // For now, just add without image upload functionality
      // TODO: Implement actual image upload to storage
      const { error } = await supabase
        .from('player_items')
        .insert([{
          name: formData.name,
          description: formData.description,
          type: formData.type,
          rarity: formData.rarity,
          value: formData.value,
          unlock_requirement: formData.unlock_requirement,
          is_default: formData.is_default,
          image_url: formData.image_url,
          is_animated: formData.is_animated
        }]);

      if (error) {
        throw error;
      }

      toast.success('Item added successfully!');
      setShowAddDialog(false);
      resetForm();
      loadItems();
    } catch (error) {
      console.error('Error adding item:', error);
      toast.error('Failed to add item');
    }
  };
  const handleEditItem = async () => {
    if (!editingItem) return;

    try {
      const { error } = await supabase
        .from('player_items')
        .update({
          name: formData.name,
          description: formData.description,
          type: formData.type,
          rarity: formData.rarity,
          value: formData.value,
          unlock_requirement: formData.unlock_requirement,
          is_default: formData.is_default,
          image_url: formData.image_url,
          is_animated: formData.is_animated
        })
        .eq('id', editingItem.id);

      if (error) {
        throw error;
      }

      toast.success('Item updated successfully!');
      setEditingItem(null);
      resetForm();
      loadItems();
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error('Failed to update item');
    }
  };
  const handleDeleteItem = async (item: PlayerItem) => {
    if (!confirm(`Are you sure you want to delete "${item.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await deleteItem(item.id);
      
      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success('Item deleted successfully!');
      loadItems();
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Failed to delete item');
    }
  };
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: 'avatar',
      rarity: 'common',
      value: '',
      unlock_requirement: null,
      is_default: false,
      image_url: null,
      is_animated: false
    });
    setSelectedImageFile(null);
    setImagePreview(null);
  };
  const openEditDialog = (item: PlayerItem) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      description: item.description || '',
      type: item.type,
      rarity: item.rarity,
      value: item.value,
      unlock_requirement: item.unlock_requirement,
      is_default: item.is_default,
      image_url: item.image_url,
      is_animated: item.is_animated
    });
    setImagePreview(item.image_url || null);
    setSelectedImageFile(null);
  };

  const filteredItems = (type: PlayerItem['type']) => {
    return items[type]?.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesRarity = filterRarity === 'all' || item.rarity === filterRarity;
      
      return matchesSearch && matchesRarity;
    }) || [];
  };
  const getItemPreview = (item: PlayerItem) => {
    const imageUrl = getItemImageUrl(item);
    
    if (imageUrl) {
      // Image-based item
      switch (item.type) {
        case 'avatar':
          return (
            <Avatar className="h-12 w-12">
              <AvatarImage src={imageUrl} />
              <AvatarFallback>{item.name.charAt(0)}</AvatarFallback>
            </Avatar>
          );
        case 'avatar_border':
          return (
            <div className="relative">
              <Avatar className="h-12 w-12">
              </Avatar>
              <img 
                src={imageUrl} 
                alt={item.name}
                className="absolute inset-0 h-12 w-12 rounded-full object-cover"
                style={{ mixBlendMode: 'multiply' }}
              />
            </div>
          );
        case 'background':
          return (
            <div 
              className="w-full h-16 rounded-lg bg-cover bg-center"
              style={{ backgroundImage: `url(${imageUrl})` }}
            />
          );
        default:
          return (
            <img 
              src={imageUrl} 
              alt={item.name}
              className="w-12 h-12 object-cover rounded"
            />
          );
      }
    }
    
    // CSS-based item (backward compatibility)
    switch (item.type) {
      case 'avatar_border':
        return (
          <div className={cn(
            'relative rounded-full',
            item.value !== 'none' && AVATAR_BORDERS[item.value as keyof typeof AVATAR_BORDERS]
          )}>
            <Avatar className="h-12 w-12">
            </Avatar>
          </div>
        );
      case 'background':
        return (
          <div className={cn(
            'w-full h-16 rounded-lg',
            BACKGROUND_THEMES[item.value as keyof typeof BACKGROUND_THEMES] || 'bg-gray-200'
          )} />
        );
      case 'title':
        return (
          <div className="text-center p-2 bg-amber-50 rounded-lg">
            <span className="text-sm font-medium text-amber-900">{item.value}</span>
          </div>
        );
      default:
        return <div className="w-12 h-12 bg-gray-200 rounded" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-lg font-medium text-amber-900">Loading items...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <Package className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-amber-900">Item Management</h1>
            <p className="text-amber-700">Manage customization items and unlock requirements</p>
          </div>
        </div>
        
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Add New Item
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Add New Item</DialogTitle>
              <DialogDescription>
                Create a new customization item for players to unlock and equip.
              </DialogDescription>
            </DialogHeader>
            <div className="overflow-y-auto max-h-[calc(90vh-8rem)] pr-2">
              <ItemForm 
                formData={formData} 
                setFormData={setFormData} 
                onSave={handleAddItem}
                onCancel={() => { setShowAddDialog(false); resetForm(); }}
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <Card className="bg-white/70 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Object.values(items).reduce((total, typeItems) => total + typeItems.length, 0)}
                </p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avatars</p>
                <p className="text-2xl font-bold text-gray-900">{items.avatar?.length || 0}</p>
              </div>
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-indigo-100 text-indigo-600">A</AvatarFallback>
              </Avatar>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avatar Borders</p>
                <p className="text-2xl font-bold text-gray-900">{items.avatar_border?.length || 0}</p>
              </div>
              <Frame className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Backgrounds</p>
                <p className="text-2xl font-bold text-gray-900">{items.background?.length || 0}</p>
              </div>
              <Palette className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Titles</p>
                <p className="text-2xl font-bold text-gray-900">{items.title?.length || 0}</p>
              </div>
              <Type className="h-8 w-8 text-amber-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6 bg-white/70 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search items by name or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select value={filterRarity} onValueChange={setFilterRarity}>
                <SelectTrigger className="w-40">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by rarity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Rarities</SelectItem>
                  <SelectItem value="common">Common</SelectItem>
                  <SelectItem value="rare">Rare</SelectItem>
                  <SelectItem value="epic">Epic</SelectItem>
                  <SelectItem value="legendary">Legendary</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>      {/* Items Display */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as PlayerItem['type'])}>
        <TabsList className="grid w-full grid-cols-4 mb-6">
          <TabsTrigger value="avatar" className="flex items-center gap-2">
            <Avatar className="h-4 w-4">
              <AvatarFallback className="bg-indigo-100 text-indigo-600 text-xs">A</AvatarFallback>
            </Avatar>
            Avatars
          </TabsTrigger>
          <TabsTrigger value="avatar_border" className="flex items-center gap-2">
            <Frame className="h-4 w-4" />
            Borders
          </TabsTrigger>
          <TabsTrigger value="background" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Backgrounds
          </TabsTrigger>
          <TabsTrigger value="title" className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            Titles
          </TabsTrigger>
        </TabsList>

        {(['avatar', 'avatar_border', 'background', 'title'] as const).map((type) => (
          <TabsContent key={type} value={type}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredItems(type).map((item) => (
                <ItemCard 
                  key={item.id} 
                  item={item} 
                  onEdit={openEditDialog}
                  onDelete={handleDeleteItem}
                  preview={getItemPreview(item)}
                />
              ))}
            </div>
            
            {filteredItems(type).length === 0 && (
              <Card className="bg-white/70 backdrop-blur-sm">
                <CardContent className="p-8 text-center">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchQuery || filterRarity !== 'all' 
                      ? 'No items match your current filters.'
                      : `No ${type.replace('_', ' ')} items have been created yet.`
                    }
                  </p>
                  <Button onClick={() => setShowAddDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Item
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Edit Dialog */}
      <Dialog open={!!editingItem} onOpenChange={(open) => !open && setEditingItem(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Edit Item</DialogTitle>
            <DialogDescription>
              Update the item details and unlock requirements.
            </DialogDescription>
          </DialogHeader>          {editingItem && (
            <div className="overflow-y-auto max-h-[calc(90vh-8rem)] pr-2">
              <ItemForm 
                formData={formData} 
                setFormData={setFormData} 
                onSave={handleEditItem}
                onCancel={() => { setShowAddDialog(false); resetForm(); }}
                isEditing={true}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

function ItemCard({ 
  item, 
  onEdit, 
  onDelete, 
  preview 
}: { 
  item: PlayerItem; 
  onEdit: (item: PlayerItem) => void;
  onDelete: (item: PlayerItem) => void;
  preview: React.ReactNode;
}) {
  return (
    <Card className="bg-white/70 backdrop-blur-sm hover:shadow-lg transition-all duration-200">
      <CardHeader className="pb-3">        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">{item.name}</CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={cn('text-xs', RARITY_COLORS[item.rarity])}>
              {item.rarity}
            </Badge>
            {item.is_default && (
              <Badge variant="outline" className="text-xs">
                Default
              </Badge>
            )}
            {isImageBasedItem(item) && (
              <Badge variant="secondary" className="text-xs">
                <ImageIcon className="h-3 w-3 mr-1" />
                Image
              </Badge>
            )}
            {item.is_animated && (
              <Badge variant="secondary" className="text-xs text-purple-600">
                GIF
              </Badge>
            )}
          </div>
        </div>
        {item.description && (
          <CardDescription className="text-xs">
            {item.description}
          </CardDescription>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Preview */}
        <div className="mb-3 flex justify-center">
          {preview}
        </div>

        {/* Details */}
        <div className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span className="text-gray-600">Value:</span>
            <span className="font-medium">{item.value}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Unlock:</span>
            <span className="font-medium text-right">
              {item.unlock_requirement ? (
                item.unlock_requirement.type === 'level' ? `Level ${item.unlock_requirement.value}` :
                item.unlock_requirement.type === 'rp' ? `${item.unlock_requirement.value} RP` :
                item.unlock_requirement.type === 'battlepass' ? `BP Tier ${item.unlock_requirement.value}` :
                String(item.unlock_requirement.value)
              ) : 'Always available'}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2 mt-4">
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1"
            onClick={() => {navigator.clipboard.writeText(item.id);toast.success('Copied item ID to clipboard');}}
          >
            <Copy className="h-3 w-3 mr-1" />
            Copy Id
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1"
            onClick={() => onEdit(item)}
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={() => onDelete(item)}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function ItemForm({ 
  formData, 
  setFormData, 
  onSave, 
  onCancel, 
  isEditing = false 
}: {
  formData: ItemFormData;
  setFormData: React.Dispatch<React.SetStateAction<ItemFormData>>;
  onSave: () => void;
  onCancel: () => void;
  isEditing?: boolean;
}) {
  const [showUnlockRequirement, setShowUnlockRequirement] = useState(!!formData.unlock_requirement);
  const [imagePreview, setImagePreview] = useState<string | null>(formData.image_url || null);

  return (
    <div className="space-y-4">
      {/* Basic Information */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Name *</label>
          <Input
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter item name"
          />
        </div>
          <div className="space-y-2">
          <label className="text-sm font-medium">Type *</label>
          <Select 
            value={formData.type} 
            onValueChange={(value: PlayerItem['type']) => setFormData(prev => ({ ...prev, type: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="avatar">Avatar</SelectItem>
              <SelectItem value="avatar_border">Avatar Border</SelectItem>
              <SelectItem value="background">Background</SelectItem>
              <SelectItem value="title">Title</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>      <div className="space-y-2">
        <label className="text-sm font-medium">Description</label>
        <Textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Enter item description"
          rows={2}
        />
      </div>      {/* Image URL Section */}
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg border">
        <div className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4" />
          <label className="text-sm font-medium">Item Image URL</label>
          <span className="text-xs text-gray-500">(Optional - Leave empty for CSS-based styling)</span>
        </div>
        
        <div className="space-y-2">
          <Input
            value={formData.image_url || ''}
            onChange={(e) => {
              const url = e.target.value;
              setFormData(prev => ({ 
                ...prev, 
                image_url: url || null,
                is_animated: url.toLowerCase().includes('.gif')
              }));
              setImagePreview(url || null);
            }}
            placeholder="https://example.com/image.jpg or /items/avatars/avatar1.jpg"
          />
          
          {imagePreview && (
            <div className="relative inline-block">
              <img 
                src={imagePreview} 
                alt="Preview" 
                className="max-w-32 max-h-32 object-cover rounded-lg border"
                onError={() => setImagePreview(null)}
              />
              {formData.is_animated && (
                <Badge className="absolute -top-2 -right-2 text-xs bg-purple-100 text-purple-600">
                  GIF
                </Badge>
              )}
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="is_animated"
            checked={formData.is_animated || false}
            onChange={(e) => setFormData(prev => ({ ...prev, is_animated: e.target.checked }))}
            className="rounded"
          />
          <label htmlFor="is_animated" className="text-sm">
            Animated (GIF)
          </label>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Rarity *</label>
          <Select 
            value={formData.rarity} 
            onValueChange={(value: PlayerItem['rarity']) => setFormData(prev => ({ ...prev, rarity: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="common">Common</SelectItem>
              <SelectItem value="rare">Rare</SelectItem>
              <SelectItem value="epic">Epic</SelectItem>
              <SelectItem value="legendary">Legendary</SelectItem>
            </SelectContent>
          </Select>
        </div>        <div className="space-y-2">
          <label className="text-sm font-medium">Value *</label>
          <Input
            value={formData.value}
            onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
            placeholder={
              formData.type === 'title' ? 'Display text (e.g., "Word Master")' :
              formData.type === 'avatar' ? 'Avatar identifier or filename' :
              'CSS class or identifier'
            }
          />
          <p className="text-xs text-gray-500">
            {formData.type === 'title' && 'The text that will be displayed as the player\'s title'}
            {formData.type === 'avatar' && 'Unique identifier for this avatar'}
            {(formData.type === 'avatar_border' || formData.type === 'background') && 'CSS class name or identifier (for non-image items)'}
          </p>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="is_default"
          checked={formData.is_default}
          onChange={(e) => setFormData(prev => ({ ...prev, is_default: e.target.checked }))}
          className="rounded"
        />
        <label htmlFor="is_default" className="text-sm font-medium">
          Default item (auto-unlocked for new players)
        </label>
      </div>

      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="has_unlock_requirement"
          checked={showUnlockRequirement}
          onChange={(e) => {
            setShowUnlockRequirement(e.target.checked);
            if (!e.target.checked) {
              setFormData(prev => ({ ...prev, unlock_requirement: null }));
            }
          }}
          className="rounded"
        />
        <label htmlFor="has_unlock_requirement" className="text-sm font-medium">
          Has unlock requirement
        </label>
      </div>

      {showUnlockRequirement && (
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="space-y-2">
            <label className="text-sm font-medium">Requirement Type</label>
            <Select 
              value={formData.unlock_requirement?.type || 'level'} 
              onValueChange={(value: 'level' | 'battlepass' | 'achievement' | 'rp') => 
                setFormData(prev => ({ 
                  ...prev, 
                  unlock_requirement: { 
                    type: value, 
                    value: prev.unlock_requirement?.value || 1 
                  } 
                }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="level">Player Level</SelectItem>
                <SelectItem value="rp">Rank Points</SelectItem>
                <SelectItem value="battlepass">Battle Pass Tier</SelectItem>
                <SelectItem value="achievement">Achievement</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Requirement Value</label>
            {formData.unlock_requirement?.type === 'achievement' ? (
              <Input
                value={String(formData.unlock_requirement?.value || '')}
                onChange={(e) => 
                  setFormData(prev => ({ 
                    ...prev, 
                    unlock_requirement: prev.unlock_requirement ? {
                      ...prev.unlock_requirement,
                      value: e.target.value
                    } : null
                  }))
                }
                placeholder="Achievement name"
              />
            ) : (
              <Input
                type="number"
                value={Number(formData.unlock_requirement?.value || 0)}
                onChange={(e) => 
                  setFormData(prev => ({ 
                    ...prev, 
                    unlock_requirement: prev.unlock_requirement ? {
                      ...prev.unlock_requirement,
                      value: parseInt(e.target.value) || 0
                    } : null
                  }))
                }
                placeholder="Enter number"
              />
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={onSave}>
          <Save className="h-4 w-4 mr-2" />
          {isEditing ? 'Update Item' : 'Create Item'}
        </Button>
      </div>
    </div>
  );
}
