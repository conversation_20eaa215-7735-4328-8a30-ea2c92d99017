'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Crown, 
  Users, 
  Star, 
  TrendingUp, 
  Calendar, 
  ArrowLeft,
  Download,
  Filter,
  Eye,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import {
  getBattlePassAnalytics,
  getPlayerProgressDistribution,
  getTierCompletionRates,
  getPremiumConversionData
} from '@/lib/admin-battle-pass-analytics';

interface BattlePassAnalytics {
  totalPlayers: number;
  premiumPlayers: number;
  conversionRate: number;
  averageTier: number;
  totalXpEarned: number;
  activePlayers: number;
  seasonProgress: {
    seasonId: string;
    seasonName: string;
    playerCount: number;
    averageTier: number;
    premiumPercentage: number;
  }[];
}

interface PlayerProgressData {
  tierId: number;
  playerCount: number;
  premiumCount: number;
}

interface TierCompletionData {
  tierNumber: number;
  totalPlayers: number;
  completedPlayers: number;
  completionRate: number;
}

export default function BattlePassAnalyticsPage() {
  const [analytics, setAnalytics] = useState<BattlePassAnalytics | null>(null);
  const [progressDistribution, setProgressDistribution] = useState<PlayerProgressData[]>([]);
  const [tierCompletion, setTierCompletion] = useState<TierCompletionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSeason, setSelectedSeason] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<string>('30');

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedSeason, timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      const [analyticsData, distributionData, completionData] = await Promise.all([
        getBattlePassAnalytics(selectedSeason, timeRange),
        getPlayerProgressDistribution(selectedSeason),
        getTierCompletionRates(selectedSeason)
      ]);
      
      setAnalytics(analyticsData);
      setProgressDistribution(distributionData);
      setTierCompletion(completionData);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const exportData = async () => {
    try {
      const data = {
        analytics,
        progressDistribution,
        tierCompletion,
        exportedAt: new Date().toISOString(),
        filters: { selectedSeason, timeRange }
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `battle-pass-analytics-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('Analytics data exported successfully');
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export data');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link href="/admin/battle-pass">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3 mb-2">
              <BarChart3 className="h-8 w-8 text-purple-600" />
              <h1 className="text-3xl font-bold text-purple-900">Battle Pass Analytics</h1>
            </div>
            <p className="text-purple-700">
              Detailed insights into battle pass performance and player engagement.
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="season-select">Season</Label>
              <Select value={selectedSeason} onValueChange={setSelectedSeason}>
                <SelectTrigger>
                  <SelectValue placeholder="Select season" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Seasons</SelectItem>
                  <SelectItem value="current">Current Season</SelectItem>
                  <SelectItem value="previous">Previous Season</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="time-range">Time Range (days)</Label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={loadAnalyticsData} disabled={loading}>
                {loading ? 'Loading...' : 'Apply Filters'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Stats */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Players</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.totalPlayers.toLocaleString()}</p>
                  <p className="text-xs text-green-600">
                    {analytics.activePlayers} active
                  </p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Premium Players</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.premiumPlayers.toLocaleString()}</p>
                  <p className="text-xs text-purple-600">
                    {analytics.conversionRate.toFixed(1)}% conversion
                  </p>
                </div>
                <Star className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Tier</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.averageTier.toFixed(1)}</p>
                  <p className="text-xs text-amber-600">
                    Cross all players
                  </p>
                </div>
                <Crown className="h-8 w-8 text-amber-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total XP</p>
                  <p className="text-2xl font-bold text-gray-900">{(analytics.totalXpEarned / 1000).toFixed(1)}K</p>
                  <p className="text-xs text-green-600">
                    XP earned
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Progress Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Player Progress Distribution</CardTitle>
            <CardDescription>
              How players are distributed across battle pass tiers
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              </div>
            ) : progressDistribution.length > 0 ? (
              <div className="space-y-4">
                {progressDistribution.slice(0, 10).map((tier) => (
                  <div key={tier.tierId} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-sm font-bold text-purple-800">
                        {tier.tierId}
                      </div>
                      <span className="font-medium">Tier {tier.tierId}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">{tier.playerCount} players</p>
                        <p className="text-xs text-purple-600">
                          {tier.premiumCount} premium
                        </p>
                      </div>
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min(100, (tier.playerCount / Math.max(...progressDistribution.map(t => t.playerCount))) * 100)}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No data available</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tier Completion Rates</CardTitle>
            <CardDescription>
              Percentage of players completing each tier
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
              </div>
            ) : tierCompletion.length > 0 ? (
              <div className="space-y-4">
                {tierCompletion.slice(0, 10).map((tier) => (
                  <div key={tier.tierNumber} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Tier {tier.tierNumber}</span>
                      <span className="text-sm text-gray-600">
                        {tier.completionRate.toFixed(1)}% ({tier.completedPlayers}/{tier.totalPlayers})
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-amber-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${tier.completionRate}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No data available</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Season Performance */}
      {analytics && analytics.seasonProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Season Performance Comparison</CardTitle>
            <CardDescription>
              Compare performance across different battle pass seasons
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.seasonProgress.map((season) => (
                <div
                  key={season.seasonId}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">{season.seasonName}</h3>
                      <p className="text-sm text-gray-600">{season.playerCount} players participated</p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-sm font-medium">Avg Tier</p>
                          <p className="text-lg font-bold text-amber-600">{season.averageTier.toFixed(1)}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm font-medium">Premium %</p>
                          <p className="text-lg font-bold text-purple-600">{season.premiumPercentage.toFixed(1)}%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Data State */}
      {!loading && !analytics && (
        <Card>
          <CardContent className="text-center py-8">
            <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No analytics data available</h3>
            <p className="text-gray-600 mb-4">
              There's no battle pass data to analyze yet. Check back after players start engaging with the battle pass.
            </p>
            <Link href="/admin/battle-pass">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Manage Battle Pass
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
