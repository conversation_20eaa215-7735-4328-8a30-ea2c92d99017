"use client";

import { useEffect, useState } from "react";
import { createClient } from "../../supabase/client";
import { SupabaseClient } from "@supabase/supabase-js";
import { Difficulty } from "@/interfaces/interfaces";

// Define types for room counts
type RoomCounts = {
  easy: number;
  medium: number;
  hard: number;
  "extreme": number;
};

// Define type for match data
type MatchData = {
  id: string;
  difficulty: Difficulty | null;
  match_players: { player_id: string; status: string }[];
};

type RoomPlayerCounterProps = {
  initialCounts: RoomCounts;
  roomName: string;
};

export default function RoomPlayerCounter({ initialCounts, roomName }: RoomPlayerCounterProps) {
  const [playerCount, setPlayerCount] = useState<number>(
    initialCounts[roomName.toLowerCase() as keyof RoomCounts] || 0
  );
  const [supabase] = useState(() => createClient());
  // Function to get player counts for a specific room
  const getRoomPlayerCount = async (supabase: SupabaseClient, roomName: string): Promise<number> => {
    const { data, error } = await supabase
      .from('matches')
      .select(`
        id,
        difficulty,
        match_players!inner(player_id, status)
      `)
      .in('status', ['waiting', 'ongoing'])
      .ilike('difficulty', roomName)
      .eq('match_players.status', 'active');

    if (error) {
      console.error('Error fetching room count:', error);
      return 0;
    }

    let count = 0;
    (data as MatchData[]).forEach(match => {
      count += match.match_players.length;
    });

    return count;
  };

  useEffect(() => {
    // Set up realtime subscription to keep counts updated
    const channel = supabase
      .channel('room-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'match_players'
        },
        async () => {
          // Refresh room count when match_players changes
          const count = await getRoomPlayerCount(supabase, roomName);
          setPlayerCount(count);
        }
      )
      .subscribe();

    // Clean up subscription on unmount
    return () => {
      supabase.removeChannel(channel);
    };
  }, [supabase, roomName]);

  return (
    <div className="text-center">
      <p className="text-4xl font-bold text-amber-800">
        {playerCount}
      </p>
      <p className="text-xs text-amber-600">players inside</p>
    </div>
  );
}
