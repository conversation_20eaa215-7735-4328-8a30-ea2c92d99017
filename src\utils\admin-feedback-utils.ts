import { createClient } from '../../supabase/client';

export interface AdminFeedback {
  id: string;
  user_id: string;
  category: string;
  title: string;
  description: string;
  rating?: number;
  status: 'pending' | 'in_review' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  browser_info?: any;
  device_info?: any;
  created_at: string;
  updated_at: string;
  admin_notes?: string;
  resolved_at?: string;
  moderator_id?: string;
  // Joined user data
  user_display_name?: string;
  user_email?: string;
}

export interface FeedbackStats {
  total: number;
  pending: number;
  in_review: number;
  resolved: number;
  closed: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  by_category: {
    bug_report: number;
    feature_request: number;
    gameplay: number;
    ui_ux: number;
    performance: number;
    other: number;
  };
}

/**
 * Get all feedback for admin management
 */
export async function getAllFeedback(
  status?: string,
  category?: string,
  priority?: string,
  limit = 50,
  offset = 0
): Promise<AdminFeedback[]> {
  const supabase = createClient();  try {
    let query = supabase
      .from('feedback')
      .select(`
        *
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }
    if (priority && priority !== 'all') {
      query = query.eq('priority', priority);
    }    const { data, error } = await query;

    if (error) {
      console.error('Error fetching feedback:', error);
      return [];
    }

    // Get unique user IDs to fetch player data
    const userIds = Array.from(new Set((data || []).map(item => item.user_id).filter(Boolean)));
    
    // Fetch player data separately
    let playersData: any[] = [];
    if (userIds.length > 0) {
      const { data: players } = await supabase
        .from('players')
        .select('id, display_name')
        .in('id', userIds);
      
      playersData = players || [];
    }

    // Map feedback with player data
    return (data || []).map(item => {
      const playerData = playersData.find(p => p.id === item.user_id);
      return {
        ...item,
        user_display_name: playerData?.display_name || 'Anonymous User'
      };
    });

  } catch (error) {
    console.error('Exception in getAllFeedback:', error);
    return [];
  }
}

/**
 * Get feedback statistics for dashboard
 */
export async function getFeedbackStats(): Promise<FeedbackStats> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('feedback')
      .select('status, priority, category, created_at');

    if (error) {
      console.error('Error fetching feedback stats:', error);
      return getDefaultStats();
    }

    const feedback = data || [];
    
    const stats: FeedbackStats = {
      total: feedback.length,
      pending: feedback.filter(f => f.status === 'pending').length,
      in_review: feedback.filter(f => f.status === 'in_review').length,
      resolved: feedback.filter(f => f.status === 'resolved').length,
      closed: feedback.filter(f => f.status === 'closed').length,
      critical: feedback.filter(f => f.priority === 'critical').length,
      high: feedback.filter(f => f.priority === 'high').length,
      medium: feedback.filter(f => f.priority === 'medium').length,
      low: feedback.filter(f => f.priority === 'low').length,
      by_category: {
        bug_report: feedback.filter(f => f.category === 'bug_report').length,
        feature_request: feedback.filter(f => f.category === 'feature_request').length,
        gameplay: feedback.filter(f => f.category === 'gameplay').length,
        ui_ux: feedback.filter(f => f.category === 'ui_ux').length,
        performance: feedback.filter(f => f.category === 'performance').length,
        other: feedback.filter(f => f.category === 'other').length,
      }
    };

    return stats;

  } catch (error) {
    console.error('Exception in getFeedbackStats:', error);
    return getDefaultStats();
  }
}

/**
 * Update feedback status and add admin notes
 */
export async function updateFeedbackStatus(
  feedbackId: string,
  status: 'pending' | 'in_review' | 'resolved' | 'closed',
  adminNotes?: string,
  priority?: 'low' | 'medium' | 'high' | 'critical'
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'Authentication required.' };
    }

    const updateData: any = {
      status,
      moderator_id: user.id,
      updated_at: new Date().toISOString()
    };

    if (adminNotes) {
      updateData.admin_notes = adminNotes;
    }

    if (priority) {
      updateData.priority = priority;
    }

    if (status === 'resolved' || status === 'closed') {
      updateData.resolved_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('feedback')
      .update(updateData)
      .eq('id', feedbackId);

    if (error) {
      console.error('Error updating feedback:', error);
      return { success: false, error: 'Failed to update feedback.' };
    }

    return { success: true };

  } catch (error) {
    console.error('Exception in updateFeedbackStatus:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Bulk update feedback items
 */
export async function bulkUpdateFeedback(
  feedbackIds: string[],
  updates: {
    status?: 'pending' | 'in_review' | 'resolved' | 'closed';
    priority?: 'low' | 'medium' | 'high' | 'critical';
  }
): Promise<{ success: boolean; error?: string; updated?: number }> {
  const supabase = createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'Authentication required.' };
    }

    const updateData: any = {
      ...updates,
      moderator_id: user.id,
      updated_at: new Date().toISOString()
    };

    if (updates.status === 'resolved' || updates.status === 'closed') {
      updateData.resolved_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('feedback')
      .update(updateData)
      .in('id', feedbackIds);

    if (error) {
      console.error('Error bulk updating feedback:', error);
      return { success: false, error: 'Failed to update feedback items.' };
    }

    return { success: true, updated: feedbackIds.length };

  } catch (error) {
    console.error('Exception in bulkUpdateFeedback:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Delete feedback (admin only)
 */
export async function deleteFeedback(feedbackId: string): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('feedback')
      .delete()
      .eq('id', feedbackId);

    if (error) {
      console.error('Error deleting feedback:', error);
      return { success: false, error: 'Failed to delete feedback.' };
    }

    return { success: true };

  } catch (error) {
    console.error('Exception in deleteFeedback:', error);
    return { success: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Get feedback details with user information
 */
export async function getFeedbackDetails(feedbackId: string): Promise<AdminFeedback | null> {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from('feedback')
      .select('*')
      .eq('id', feedbackId)
      .single();

    if (error) {
      console.error('Error fetching feedback details:', error);
      return null;
    }

    // Fetch player data separately if user_id exists
    let playerData = null;
    if (data.user_id) {
      const { data: player } = await supabase
        .from('players')
        .select('id, display_name')
        .eq('id', data.user_id)
        .single();
      
      playerData = player;
    }

    return {
      ...data,
      user_display_name: playerData?.display_name || 'Anonymous User'
    };

  } catch (error) {
    console.error('Exception in getFeedbackDetails:', error);
    return null;
  }
}

/**
 * Get recent feedback activity for dashboard
 */
export async function getRecentFeedback(limit = 5): Promise<AdminFeedback[]> {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from('feedback')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);    if (error) {
      console.error('Error fetching recent feedback:', error);
      return [];
    }

    // Get unique user IDs to fetch player data
    const userIds = Array.from(new Set((data || []).map(item => item.user_id).filter(Boolean)));
    
    // Fetch player data separately
    let playersData: any[] = [];
    if (userIds.length > 0) {
      const { data: players } = await supabase
        .from('players')
        .select('id, display_name')
        .in('id', userIds);
      
      playersData = players || [];
    }

    return (data || []).map(item => {
      const playerData = playersData.find(p => p.id === item.user_id);
      return {
        ...item,
        user_display_name: playerData?.display_name || 'Anonymous User'
      };
    });

  } catch (error) {
    console.error('Exception in getRecentFeedback:', error);
    return [];
  }
}

function getDefaultStats(): FeedbackStats {
  return {
    total: 0,
    pending: 0,
    in_review: 0,
    resolved: 0,
    closed: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    by_category: {
      bug_report: 0,
      feature_request: 0,
      gameplay: 0,
      ui_ux: 0,
      performance: 0,
      other: 0,
    }
  };
}
