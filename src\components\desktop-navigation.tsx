'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  Crown,
  Trophy,
  Star,
  User,
  TrendingUp,
  Medal,
  Zap,
  Users
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  color: string;
  bgColor: string;
  hoverColor: string;
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Hall of Fame',
    href: '/hall-of-fame',
    icon: Trophy,
    description: 'View top players',
    color: 'text-amber-600',
    bgColor: 'bg-amber-50',
    hoverColor: 'hover:bg-amber-100'
  },
  {
    name: 'Levels',
    href: '/levels',
    icon: Star,
    description: 'Track your progress',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    hoverColor: 'hover:bg-blue-100'
  },
  {
    name: 'Battle Pass',
    href: '/battle-pass',
    icon: Crown,
    description: 'Seasonal rewards',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    hoverColor: 'hover:bg-purple-100'
  },
  {
    name: 'Profile',
    href: '/profile',
    icon: User,
    description: 'Your achievements',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    hoverColor: 'hover:bg-green-100'
  }
];

export default function DesktopNavigation() {
  const pathname = usePathname();

  return (
    <div className="hidden md:block lg:block">
      <div className="bg-white/80 backdrop-blur-sm border border-amber-200 rounded-2xl p-6 shadow-lg">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-br from-amber-400 to-amber-600 rounded-lg">
            <TrendingUp className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="font-bold text-amber-900">Quick Access</h2>
            <p className="text-xs text-amber-700">Explore Word Nook features</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'group relative p-4 rounded-xl border-2 transition-all duration-200',
                  'flex flex-col items-center text-center space-y-2',
                  isActive
                    ? `${item.bgColor} border-current ${item.color} shadow-md`
                    : `bg-white border-gray-200 hover:border-gray-300 ${item.hoverColor}`,
                  'hover:shadow-md hover:scale-[1.02] active:scale-[0.98]'
                )}
              >
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-amber-400 to-amber-600 rounded-full border-2 border-white shadow-sm" />
                )}

                {/* Icon */}
                <div className={cn(
                  'p-3 rounded-lg transition-colors',
                  isActive 
                    ? `${item.bgColor} ${item.color}` 
                    : 'bg-gray-50 text-gray-600 group-hover:bg-gray-100'
                )}>
                  <Icon className="h-6 w-6" />
                </div>

                {/* Content */}
                <div className="space-y-1">
                  <h3 className={cn(
                    'font-semibold text-sm transition-colors',
                    isActive ? item.color : 'text-gray-900 group-hover:text-gray-800'
                  )}>
                    {item.name}
                  </h3>
                  <p className={cn(
                    'text-xs transition-colors',
                    isActive ? 'text-gray-600' : 'text-gray-500 group-hover:text-gray-600'
                  )}>
                    {item.description}
                  </p>
                </div>

                {/* Hover effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
              </Link>
            );
          })}
        </div>

        {/* Stats or additional info */}
        <div className="mt-6 pt-4 border-t border-amber-100">
          <div className="flex items-center justify-between text-xs text-amber-700">
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>Community</span>
            </div>
            <div className="flex items-center gap-1">
              <Medal className="h-3 w-3" />
              <span>Compete</span>
            </div>
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <span>Progress</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
