/**
 * Enhanced Match Rewards Popup Component
 *
 * Displays comprehensive match completion rewards including:
 * - Rank Points (RP) changes and rank progression
 * - Level XP gained and level progression
 * - Battle Pass XP and tier progression
 *
 * Features:
 * - Multi-step UI flow: Loading → Rewards Display → Level Up (if applicable)
 * - Cozy, card-based design with color-coded sections
 * - Integration with level system and battle pass utilities
 * - Animated level-up celebration screen
 * - Responsive layout with hover effects
 *
 * Exported as both MatchRewardsPopup (default) and RPGainPopup (backward compatibility)
 */
"use client";

import { useEffect, useState } from "react";
import { createClient } from "../../supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Trophy,
  TrendingUp,
  TrendingDown,
  Crown,
  Star,
  ArrowUp,
  ArrowDown,
  Zap,
  Award,
  Sparkles,
  Users,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  getRankInfo,
  calculateRPChange,
  getRankDisplayString,
  getRankColorClasses,
  formatRP,
} from "@/utils/ranking-system";
import {
  calculateMatchXP,
  getLevelInfo,
  formatXP,
  getLevelTitle,
} from "@/utils/level-system";
import {
  getCurrentSeason,
  getPlayerBattlePassProgress,
} from "@/utils/battle-pass-utils";

interface MatchRewardsPopupProps {
  matchId: string;
  finalRank: number;
  totalPlayers: number;
  score: number;
  difficulty: string;
  eliminationRound?: number;
  isVisible: boolean;
  onClose: () => void;
  isValidMatch: boolean;
}

interface MatchRewardsData {
  // RP Data
  rpChange: number;
  oldRP: number;
  newRP: number;
  oldRank: string;
  newRank: string;
  rpBreakdown: string[];
  rankUp: boolean;
  rankDown: boolean;

  // Level XP Data
  xpGained: number;
  xpBreakdown: string[];
  oldLevel: number;
  newLevel: number;
  leveledUp: boolean;
  oldLevelXP: number;
  newLevelXP: number;

  // Battle Pass Data
  battlePassXP: number;
  oldBattlePassXP: number;
  newBattlePassXP: number;
  oldBattlePassTier: number;
  newBattlePassTier: number;
  battlePassTierUp: boolean;
  hasBattlePass: boolean;
  battlePassSeasonName: string | null;
}

export default function MatchRewardsPopup({
  matchId,
  finalRank,
  totalPlayers,
  score,
  difficulty,
  eliminationRound,
  isVisible,
  onClose,
  isValidMatch,
}: MatchRewardsPopupProps) {
  const [rewardsData, setRewardsData] = useState<MatchRewardsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<
    "loading" | "rp" | "level" | "battlepass" | "levelup"
  >("loading");
  const [animatingProgress, setAnimatingProgress] = useState(false);
  const [animatedRP, setAnimatedRP] = useState(0);
  const [animatedLevelXP, setAnimatedLevelXP] = useState(0);
  const [animatedBattlePassXP, setAnimatedBattlePassXP] = useState(0);

  useEffect(() => {
    if (!isVisible || !matchId) return;

    const fetchMatchRewardsData = async () => {
      try {
        setLoading(true);
        const supabase = createClient();

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          setError("User not authenticated");
          return;
        }

        // Get player's current data
        const { data: playerData } = await supabase
          .from("players")
          .select("rankpoints, level_experience")
          .eq("id", user.id)
          .single();

        const currentRP = playerData?.rankpoints || 0;
        const currentLevelXP = playerData?.level_experience || 0;
        const currentLevelInfo = getLevelInfo(currentLevelXP);

        // Calculate XP gain
        const xpGainData = isValidMatch
          ? calculateMatchXP(
              finalRank,
              totalPlayers,
              score,
              difficulty,
              eliminationRound
            )
          : {
              baseXP: 0,
              rankBonus: 0,
              performanceBonus: 0,
              difficultyMultiplier: 1.0,
              totalXP: 0,
              breakdown: [],
            };

        // Get match history data for RP (if available)
        const { data: matchHistoryData } = await supabase
          .from("match_history_players")
          .select("rp_gained, rp_before_match, rp_after_match")
          .eq("match_id", matchId)
          .eq("player_id", user.id)
          .single();

        let rpData;
        if (matchHistoryData) {
          // Use actual RP data from database
          const oldRankInfo = getRankInfo(matchHistoryData.rp_before_match);
          const newRankInfo = getRankInfo(matchHistoryData.rp_after_match);

          rpData = {
            rpChange: matchHistoryData.rp_gained,
            oldRP: matchHistoryData.rp_before_match,
            newRP: matchHistoryData.rp_after_match,
            oldRank: getRankDisplayString(oldRankInfo),
            newRank: getRankDisplayString(newRankInfo),
            rankUp:
              newRankInfo.tierOrder > oldRankInfo.tierOrder ||
              (newRankInfo.tierOrder === oldRankInfo.tierOrder &&
                newRankInfo.divisionOrder > oldRankInfo.divisionOrder),
            rankDown:
              newRankInfo.tierOrder < oldRankInfo.tierOrder ||
              (newRankInfo.tierOrder === oldRankInfo.tierOrder &&
                newRankInfo.divisionOrder < oldRankInfo.divisionOrder),
          };
        } else {
          // Fallback calculation
          const { data: matchData } = await supabase
            .from("match_histories")
            .select("match_duration_seconds")
            .eq("id", matchId)
            .single();

          const durationMinutes =
            (matchData?.match_duration_seconds || 300) / 60;
          const averageScore = score * 0.8;

          const rpChange = isValidMatch ? calculateRPChange(
            currentRP,
            finalRank,
            totalPlayers,
            durationMinutes,
            difficulty,
            score,
            averageScore
          ) :
          {
            baseRP: 0,
            rankBonus: 0,
            performanceBonus: 0,
            durationBonus: 0,
            difficultyMultiplier: 1.0,
            totalRP: 0,
            breakdown: [],
          }

          const oldRankInfo = getRankInfo(currentRP);
          const newRankInfo = getRankInfo(
            Math.max(0, currentRP + rpChange.totalRP)
          );

          rpData = {
            rpChange: rpChange.totalRP,
            oldRP: currentRP,
            newRP: Math.max(0, currentRP + rpChange.totalRP),
            oldRank: getRankDisplayString(oldRankInfo),
            newRank: getRankDisplayString(newRankInfo),
            rankUp:
              newRankInfo.tierOrder > oldRankInfo.tierOrder ||
              (newRankInfo.tierOrder === oldRankInfo.tierOrder &&
                newRankInfo.divisionOrder > oldRankInfo.divisionOrder),
            rankDown:
              newRankInfo.tierOrder < oldRankInfo.tierOrder ||
              (newRankInfo.tierOrder === oldRankInfo.tierOrder &&
                newRankInfo.divisionOrder < oldRankInfo.divisionOrder),
          };
        }

        // Get battle pass data
        const battlePassXP = Math.round(xpGainData.totalXP * 0.5);
        const currentSeason = await getCurrentSeason();
        let battlePassData = {
          battlePassXP,
          oldBattlePassXP: 0,
          newBattlePassXP: battlePassXP,
          oldBattlePassTier: 1,
          newBattlePassTier: 1,
          battlePassTierUp: false,
          hasBattlePass: false,
          battlePassSeasonName: currentSeason?.season_name || null,
        };

        if (currentSeason) {
          const battlePassProgress = await getPlayerBattlePassProgress(user.id);
          if (battlePassProgress) {
            battlePassData = {
              ...battlePassData,
              oldBattlePassXP: battlePassProgress.current_xp,
              newBattlePassXP: battlePassProgress.current_xp + battlePassXP,
              oldBattlePassTier: battlePassProgress.current_tier,
              newBattlePassTier: battlePassProgress.current_tier, // This would be calculated if tier up occurred
              hasBattlePass: true,
            };
          }
        }

        // Calculate new level after XP gain
        const newTotalXP = currentLevelXP + xpGainData.totalXP;
        const newLevelInfo = getLevelInfo(newTotalXP);
        const leveledUp =
          newLevelInfo.currentLevel > currentLevelInfo.currentLevel;

        setRewardsData({
          // RP Data
          rpChange: rpData.rpChange,
          oldRP: rpData.oldRP,
          newRP: rpData.newRP,
          oldRank: rpData.oldRank,
          newRank: rpData.newRank,
          rpBreakdown: [], // Could add RP breakdown if needed
          rankUp: rpData.rankUp,
          rankDown: rpData.rankDown,

          // Level XP Data
          xpGained: xpGainData.totalXP,
          xpBreakdown: xpGainData.breakdown,
          oldLevel: currentLevelInfo.currentLevel,
          newLevel: newLevelInfo.currentLevel,
          leveledUp,
          oldLevelXP: currentLevelXP,
          newLevelXP: newTotalXP,

          // Battle Pass Data
          ...battlePassData,
        });

        setCurrentStep(leveledUp ? "levelup" : "rp");
      } catch (err) {
        console.error("Error fetching match rewards data:", err);
        setError("Failed to load rewards data");
      } finally {
        setLoading(false);
      }
    };

    fetchMatchRewardsData();
  }, [
    isVisible,
    matchId,
    finalRank,
    totalPlayers,
    score,
    difficulty,
    eliminationRound,
  ]);
  const handleContinue = () => {
    // Optional: Add sound effect for step transitions
    // playRewardStepSound();

    if (currentStep === "rp") {
      setCurrentStep("level");
    } else if (currentStep === "level") {
      setCurrentStep("battlepass");
    } else if (currentStep === "battlepass") {
      if (rewardsData?.leveledUp) {
        setCurrentStep("levelup");
      } else {
        onClose();
      }
    } else {
      onClose();
    }
  };

  const handleLevelUpContinue = () => {
    onClose();
  };

  // Animation effect for progress bars
  const animateProgress = (
    currentValue: number,
    targetValue: number,
    setter: (value: number) => void,
    duration: number = 2000
  ) => {
    setAnimatingProgress(true);
    const startTime = Date.now();
    const difference = targetValue - currentValue;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeOutExpo = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);
      const current = currentValue + difference * easeOutExpo;

      setter(Math.round(current));

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setAnimatingProgress(false);
      }
    };

    requestAnimationFrame(animate);
  };
  // Trigger animations when entering each step
  useEffect(() => {
    if (!rewardsData) return;

    if (currentStep === "rp") {
      // Initialize at old value, then animate to new value
      setAnimatedRP(rewardsData.oldRP);
      setTimeout(() => {
        animateProgress(
          rewardsData.oldRP,
          rewardsData.newRP,
          setAnimatedRP,
          1500
        );
      }, 300);
    } else if (currentStep === "level") {
      setAnimatedLevelXP(rewardsData.oldLevelXP);
      setTimeout(() => {
        animateProgress(
          rewardsData.oldLevelXP,
          rewardsData.newLevelXP,
          setAnimatedLevelXP,
          1500
        );
      }, 300);
    } else if (currentStep === "battlepass") {
      setAnimatedBattlePassXP(rewardsData.oldBattlePassXP);
      setTimeout(() => {
        animateProgress(
          rewardsData.oldBattlePassXP,
          rewardsData.newBattlePassXP,
          setAnimatedBattlePassXP,
          1500
        );
      }, 300);
    }
  }, [currentStep, rewardsData]);

  const getLevelBadgeStyle = (level: number) => {
    if (level >= 50)
      return "bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg";
    if (level >= 25)
      return "bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-md";
    if (level >= 15)
      return "bg-gradient-to-r from-blue-400 to-cyan-500 text-white shadow-md";
    if (level >= 5)
      return "bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-sm";
    return "bg-gradient-to-r from-gray-400 to-gray-500 text-white";
  };
  if (!isVisible) return null;

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-6 w-6 text-yellow-500" />;
    if (rank <= 3) return <Trophy className="h-6 w-6 text-amber-500" />;
    return <Star className="h-6 w-6 text-gray-500" />;
  };

  const getPerformanceColor = (rank: number, total: number) => {
    const percentile = (total - rank + 1) / total;
    if (percentile >= 0.8) return "text-green-600 bg-green-50";
    if (percentile >= 0.6) return "text-blue-600 bg-blue-50";
    if (percentile >= 0.4) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-lg mx-auto shadow-2xl border-2">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            {getRankIcon(finalRank)}
            <CardTitle className="text-2xl">Match Complete!</CardTitle>
          </div>
          <div className="flex items-center justify-center gap-2">
            <Badge
              variant="outline"
              className={cn(
                "text-sm",
                getPerformanceColor(finalRank, totalPlayers)
              )}
            >
              Rank #{finalRank} of {totalPlayers}
            </Badge>
            <Badge variant="outline" className="capitalize">
              {difficulty}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {" "}
          {currentStep === "loading" && (
            <div className="text-center py-8">
              <div className="relative mx-auto w-16 h-16 mb-4">
                <div className="absolute inset-0 rounded-full border-4 border-amber-200"></div>
                <div className="absolute inset-0 rounded-full border-4 border-amber-600 border-t-transparent animate-spin"></div>
                <div className="absolute inset-2 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 flex items-center justify-center">
                  <Sparkles className="h-6 w-6 text-white animate-pulse" />
                </div>
              </div>
              <p className="text-lg font-medium text-gray-700 mb-2">
                Calculating your rewards...
              </p>
              <p className="text-sm text-gray-500">Processing match results</p>
            </div>
          )}
          {error && (
            <div className="text-center py-8">
              <div className="mx-auto w-16 h-16 mb-4 rounded-full bg-red-100 flex items-center justify-center">
                <TrendingDown className="h-8 w-8 text-red-600" />
              </div>
              <p className="text-lg font-medium text-red-600 mb-2">
                Oops! Something went wrong
              </p>
              <p className="text-sm text-gray-600 mb-4">{error}</p>
              <Button
                onClick={onClose}
                variant="outline"
                className="border-red-300 text-red-600 hover:bg-red-50"
              >
                Close
              </Button>
            </div>
          )}
          {currentStep === "rp" && rewardsData && (
            <div className="animate-in slide-in-from-right-5 fade-in-0 duration-500">
              {/* RP Reward Screen */}
              <div className="bg-gradient-to-br from-purple-50 via-purple-100 to-indigo-100 rounded-xl p-6 border border-purple-200 shadow-lg">
                <div className="text-center mb-6">
                  <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                    <Trophy className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-purple-900 mb-2">
                    Rank Points Earned!
                  </h2>
                  <div
                    className={cn(
                      "text-4xl font-bold mb-2",
                      rewardsData.rpChange > 0
                        ? "text-green-600"
                        : "text-red-600"
                    )}
                  >
                    {rewardsData.rpChange > 0 ? "+" : ""}
                    {rewardsData.rpChange} RP
                  </div>
                </div>

                {/* Rank Progression */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="text-center">
                      <p className="text-sm text-purple-600 font-medium">
                        {rewardsData.oldRank}
                      </p>
                      <p className="text-xs text-gray-600">
                        {formatRP(rewardsData.oldRP)}
                      </p>
                    </div>

                    <div className="flex-1 px-4">
                      <div className="flex items-center gap-2">
                        {rewardsData.rankUp ? (
                          <ArrowUp className="h-5 w-5 text-green-600" />
                        ) : rewardsData.rankDown ? (
                          <ArrowDown className="h-5 w-5 text-red-600" />
                        ) : (
                          <div className="h-5 w-5"></div>
                        )}
                        <Progress
                          value={
                            ((animatedRP - rewardsData.oldRP) /
                              Math.max(Math.abs(rewardsData.rpChange), 1)) *
                            100
                          }
                          className="flex-1 h-3 bg-purple-200"
                        />
                        {(rewardsData.rankUp || rewardsData.rankDown) && (
                          <Badge
                            variant={
                              rewardsData.rankUp ? "default" : "destructive"
                            }
                            className="text-xs animate-bounce"
                          >
                            {rewardsData.rankUp ? "Rank Up!" : "Rank Down"}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="text-center">
                      <p className="text-sm text-purple-600 font-medium">
                        {rewardsData.newRank}
                      </p>
                      <p className="text-xs text-gray-600">
                        {formatRP(animatedRP)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Step indicator */}
                <div className="flex justify-center items-center gap-2 mt-6 mb-4">
                  <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                </div>

                <Button
                  onClick={handleContinue}
                  className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white shadow-lg"
                  disabled={animatingProgress}
                >
                  Next: Level Experience{" "}
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          )}
          {currentStep === "level" && rewardsData && (
            <div className="animate-in slide-in-from-right-5 fade-in-0 duration-500">
              {/* Level XP Reward Screen */}
              <div className="bg-gradient-to-br from-emerald-50 via-green-100 to-emerald-100 rounded-xl p-6 border border-emerald-200 shadow-lg">
                <div className="text-center mb-6">
                  <div className="mx-auto w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-emerald-900 mb-2">
                    Experience Gained!
                  </h2>
                  <div className="text-4xl font-bold text-emerald-600 mb-2">
                    +{formatXP(rewardsData.xpGained)}
                  </div>
                </div>

                {/* Level Progression */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className={getLevelBadgeStyle(rewardsData.oldLevel)}>
                      Level {rewardsData.oldLevel}
                    </Badge>

                    <div className="flex-1 px-4">
                      <div className="flex items-center gap-2">
                        {rewardsData.leveledUp && (
                          <ArrowUp className="h-5 w-5 text-green-600" />
                        )}
                        <div className="flex-1">
                          <Progress
                            value={
                              ((animatedLevelXP - rewardsData.oldLevelXP) /
                                Math.max(rewardsData.xpGained, 1)) *
                              100
                            }
                            className="h-3 bg-emerald-200"
                          />
                          <div className="text-xs text-emerald-700 mt-1 text-center">
                            {animatedLevelXP.toLocaleString()} XP
                          </div>
                        </div>
                        {rewardsData.leveledUp && (
                          <Badge className="bg-emerald-100 text-emerald-800 text-xs animate-bounce">
                            Level Up!
                          </Badge>
                        )}
                      </div>
                    </div>

                    {rewardsData.leveledUp && (
                      <Badge
                        className={getLevelBadgeStyle(rewardsData.newLevel)}
                      >
                        Level {rewardsData.newLevel}
                      </Badge>
                    )}
                  </div>

                  {/* XP Breakdown */}
                  <div className="bg-emerald-50 rounded-lg p-3 border border-emerald-200">
                    <h4 className="text-sm font-medium text-emerald-800 mb-2">
                      XP Breakdown:
                    </h4>
                    <div className="space-y-1">
                      {rewardsData.xpBreakdown.map((line, index) => (
                        <p key={index} className="text-xs text-emerald-700">
                          {line}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Step indicator */}
                <div className="flex justify-center items-center gap-2 mt-6 mb-4">
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                </div>

                <Button
                  onClick={handleContinue}
                  className="w-full bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white shadow-lg"
                  disabled={animatingProgress}
                >
                  Next: Battle Pass <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          )}
          {currentStep === "battlepass" && rewardsData && (
            <div className="animate-in slide-in-from-right-5 fade-in-0 duration-500">
              {/* Battle Pass XP Reward Screen */}
              <div className="bg-gradient-to-br from-amber-50 via-orange-100 to-amber-100 rounded-xl p-6 border border-amber-200 shadow-lg">
                <div className="text-center mb-6">
                  <div className="mx-auto w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                    <Crown className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-amber-900 mb-2">
                    Battle Pass Progress!
                  </h2>
                  <div className="text-4xl font-bold text-amber-600 mb-2">
                    +{rewardsData.battlePassXP} XP
                  </div>
                </div>

                {/* Battle Pass Progression */}
                <div className="space-y-4">
                  {rewardsData.battlePassSeasonName ? (
                    <>
                      <div className="text-center">
                        <p className="text-lg font-medium text-amber-800 mb-1">
                          {rewardsData.battlePassSeasonName}
                        </p>
                        <p className="text-sm text-amber-600">
                          Tier{" "}
                          {rewardsData.hasBattlePass
                            ? rewardsData.oldBattlePassTier
                            : 1}
                        </p>
                      </div>

                      <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
                        <div className="flex items-center gap-2 mb-2">
                          <Crown className="h-4 w-4 text-amber-600" />
                          <span className="text-sm font-medium text-amber-800">
                            Progress
                          </span>
                          {rewardsData.battlePassTierUp && (
                            <Badge className="bg-amber-100 text-amber-800 text-xs animate-bounce">
                              Tier Up!
                            </Badge>
                          )}
                        </div>

                        <Progress
                          value={
                            rewardsData.hasBattlePass
                              ? ((animatedBattlePassXP -
                                  rewardsData.oldBattlePassXP) /
                                  Math.max(rewardsData.battlePassXP, 1)) *
                                100
                              : 50
                          }
                          className="h-3 bg-amber-200 mb-2"
                        />

                        <div className="text-xs text-amber-700 text-center">
                          {rewardsData.hasBattlePass ? (
                            <span>
                              {animatedBattlePassXP.toLocaleString()} XP
                            </span>
                          ) : (
                            <span>
                              Join the battle pass to start earning rewards!
                            </span>
                          )}
                        </div>
                      </div>

                      {!rewardsData.hasBattlePass && (
                        <div className="bg-gradient-to-r from-amber-100 to-orange-100 rounded-lg p-4 border border-amber-300">
                          <div className="text-center">
                            <Sparkles className="h-6 w-6 text-amber-600 mx-auto mb-2" />
                            <p className="text-sm font-medium text-amber-800 mb-1">
                              Unlock Premium Rewards!
                            </p>
                            <p className="text-xs text-amber-600">
                              Purchase the battle pass to earn exclusive items,
                              avatars, and more!
                            </p>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-amber-600">
                        No active battle pass season
                      </p>
                    </div>
                  )}
                </div>

                {/* Step indicator */}
                <div className="flex justify-center items-center gap-2 mt-6 mb-4">
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                  <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                </div>

                <Button
                  onClick={handleContinue}
                  className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg"
                  disabled={animatingProgress}
                >
                  {rewardsData.leveledUp ? "See Level Up!" : "Finish"}
                  {rewardsData.leveledUp ? (
                    <ChevronRight className="h-4 w-4 ml-2" />
                  ) : (
                    <Sparkles className="h-4 w-4 ml-2" />
                  )}
                </Button>
              </div>
            </div>
          )}{" "}
          {currentStep === "levelup" && rewardsData && (
            <div className="py-6 text-center animate-in zoom-in-95 fade-in-0 duration-700">
              <div className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 -mx-6 -mt-6 pt-8 pb-6 px-6 rounded-t-lg relative overflow-hidden">
                {/* Background sparkles */}
                <div className="absolute inset-0 overflow-hidden">
                  <div className="absolute top-4 left-8 animate-ping">
                    <Sparkles className="h-4 w-4 text-yellow-200 opacity-75" />
                  </div>
                  <div className="absolute top-8 right-12 animate-ping delay-300">
                    <Sparkles className="h-3 w-3 text-white opacity-60" />
                  </div>
                  <div className="absolute bottom-8 left-16 animate-ping delay-700">
                    <Sparkles className="h-5 w-5 text-yellow-300 opacity-50" />
                  </div>
                  <div className="absolute bottom-4 right-8 animate-ping delay-500">
                    <Sparkles className="h-3 w-3 text-orange-200 opacity-75" />
                  </div>
                  <div className="absolute top-16 left-1/2 transform -translate-x-1/2 animate-ping delay-1000">
                    <Star className="h-4 w-4 text-yellow-100 opacity-80" />
                  </div>
                </div>

                <div className="relative z-10">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Award className="h-12 w-12 text-white animate-bounce" />
                    <div>
                      <h2 className="text-3xl font-bold text-white drop-shadow-lg animate-pulse">
                        LEVEL UP!
                      </h2>
                      <p className="text-xl text-white drop-shadow-md">
                        Level {rewardsData.newLevel}
                      </p>
                    </div>
                    <Award
                      className="h-12 w-12 text-white animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    />
                  </div>

                  <p className="text-lg text-white mb-2 drop-shadow-sm">
                    You are now a
                  </p>
                  <p className="text-2xl font-bold text-white mb-4 drop-shadow-lg animate-pulse">
                    {getLevelTitle(rewardsData.newLevel)}
                  </p>
                </div>
              </div>

              <div className="py-6">
                <p className="text-lg font-medium text-gray-700 mb-4">
                  New rewards unlocked!
                </p>

                {rewardsData.newLevel >= 5 && rewardsData.newLevel < 15 && (
                  <div className="space-y-2">
                    <Badge className="bg-blue-100 text-blue-800">
                      Rare Content
                    </Badge>
                    <p className="text-sm text-gray-600">
                      Check your profile to see new avatars and backgrounds!
                    </p>
                  </div>
                )}

                {rewardsData.newLevel >= 15 && rewardsData.newLevel < 50 && (
                  <div className="space-y-2">
                    <Badge className="bg-purple-100 text-purple-800">
                      Epic Content
                    </Badge>
                    <p className="text-sm text-gray-600">
                      Check your profile to see new epic avatars, backgrounds
                      and borders!
                    </p>
                  </div>
                )}

                {rewardsData.newLevel >= 50 && (
                  <div className="space-y-2">
                    <Badge className="bg-yellow-100 text-yellow-800">
                      Legendary Content
                    </Badge>
                    <p className="text-sm text-gray-600">
                      Check your profile to see your new legendary content!
                    </p>
                  </div>
                )}
              </div>

              <Button
                onClick={handleLevelUpContinue}
                className="bg-amber-600 hover:bg-amber-700"
              >
                Awesome!
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Export with both names for backward compatibility
export { MatchRewardsPopup as RPGainPopup };
