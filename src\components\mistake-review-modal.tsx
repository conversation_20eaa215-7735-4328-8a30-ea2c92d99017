"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  X,
  Crown,
  Eye,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Volume2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { playWordAudio } from "@/utils/audio-utils";

interface MistakeReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  userAnswer: string;
  correctAnswer: string;
  wordDifficulty: string;
  hasBattlePass: boolean;
}

export default function MistakeReviewModal({
  isOpen,
  onClose,
  userAnswer,
  correctAnswer,
  wordDifficulty,
  hasBattlePass,
}: MistakeReviewModalProps) {
  const [isPlayingAudio, setIsPlayingAudio] = React.useState(false);

  const handlePlayAudio = async () => {
    if (!correctAnswer || isPlayingAudio) return;

    setIsPlayingAudio(true);
    try {
      await playWordAudio(correctAnswer, wordDifficulty as any);
    } catch (error) {
      console.error("Error playing audio:", error);
    } finally {
      setIsPlayingAudio(false);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "easy":
        return "bg-green-100 text-green-800 border-green-200";
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "hard":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "extreme":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getLetterComparison = () => {
    if (!userAnswer || !correctAnswer) return [];

    const userLetters = userAnswer.toLowerCase().split("");
    const correctLetters = correctAnswer.toLowerCase().split("");
    const maxLength = Math.max(userLetters.length, correctLetters.length);

    const comparison = [];
    for (let i = 0; i < maxLength; i++) {
      const userLetter = userLetters[i] || "";
      const correctLetter = correctLetters[i] || "";

      let status: "correct" | "incorrect" | "missing" | "extra" = "correct";

      if (userLetter && !correctLetter) {
        status = "extra";
      } else if (!userLetter && correctLetter) {
        status = "missing";
      } else if (userLetter !== correctLetter) {
        status = "incorrect";
      }

      comparison.push({
        position: i,
        userLetter,
        correctLetter,
        status,
      });
    }

    return comparison;
  };

  const getSpecificTips = () => {
    if (!userAnswer || !correctAnswer) return [];

    const tips = [];
    const userLower = userAnswer.toLowerCase();
    const correctLower = correctAnswer.toLowerCase();

    // Length difference tips
    if (userAnswer.length < correctAnswer.length) {
      tips.push(
        "• You may have missed some letters - try to listen for all syllables"
      );
    } else if (userAnswer.length > correctAnswer.length) {
      tips.push(
        "• You may have added extra letters - focus on the core sounds"
      );
    }

    // Common mistake patterns
    if (correctLower.includes("ph") && userLower.includes("f")) {
      tips.push("• Remember: 'ph' makes the 'f' sound in many words");
    }
    if (correctLower.includes("tion") && !userLower.includes("tion")) {
      tips.push(
        "• Words ending in 'tion' are very common - listen for that ending"
      );
    }
    if (correctLower.includes("ght") && !userLower.includes("ght")) {
      tips.push(
        "• 'ght' combinations are silent letters that are often missed"
      );
    }

    // Difficulty-specific tips
    switch (wordDifficulty.toLowerCase()) {
      case "extreme":
        tips.push(
          "• Extreme words often have unusual letter combinations or silent letters"
        );
        break;
      case "hard":
        tips.push(
          "• Hard words may have tricky consonant clusters or vowel patterns"
        );
        break;
      case "medium":
        tips.push(
          "• Medium words often test common but confusing spelling rules"
        );
        break;
      case "easy":
        tips.push(
          "• Even easy words can be tricky - take your time with each letter"
        );
        break;
    }
    return tips.slice(0, 4); // Limit to 4 tips max
  };

  const specificTips = getSpecificTips();
  const letterComparison = getLetterComparison();

  // Calculate similarity percentage
  const calculateSimilarity = () => {
    if (!userAnswer || !correctAnswer) return 0;

    const userLower = userAnswer.toLowerCase();
    const correctLower = correctAnswer.toLowerCase();

    // Simple Levenshtein distance approach
    const maxLength = Math.max(userLower.length, correctLower.length);
    if (maxLength === 0) return 100;

    let matches = 0;
    const minLength = Math.min(userLower.length, correctLower.length);

    for (let i = 0; i < minLength; i++) {
      if (userLower[i] === correctLower[i]) {
        matches++;
      }
    }

    return Math.round((matches / maxLength) * 100);
  };

  const similarity = calculateSimilarity();

  if (!hasBattlePass) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md bg-gradient-to-br from-amber-50 to-orange-50 border-2 border-amber-200">
          <DialogHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-amber-100 rounded-full w-fit">
              <Crown className="h-8 w-8 text-amber-600" />
            </div>
            <DialogTitle className="text-2xl font-bold text-amber-900">
              Premium Feature
            </DialogTitle>
            <DialogDescription className="text-amber-700 text-base">
              Mistake Review is available with Battle Pass Premium
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Card className="border-amber-200 bg-white/50">
              <CardContent className="p-4">
                <h3 className="font-semibold text-amber-900 mb-3">
                  ✨ Premium Benefits:
                </h3>
                <ul className="text-sm text-amber-800 space-y-2">
                  <li className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-amber-600" />
                    <span>Review your mistakes vs correct answers</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Volume2 className="h-4 w-4 text-amber-600" />
                    <span>Hear correct pronunciations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4 text-amber-600" />
                    <span>Letter-by-letter comparison</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Crown className="h-4 w-4 text-amber-600" />
                    <span>Access to all premium rewards</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                className="flex-1 border-amber-300 text-amber-700 hover:bg-amber-50"
              >
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl bg-white border-2 border-amber-200 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-full">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold text-gray-900">
                  Mistake Review
                </DialogTitle>
                <DialogDescription className="text-gray-600">
                  Learn from your spelling mistakes
                </DialogDescription>
              </div>
            </div>
            <Badge
              className={cn(
                "text-xs font-medium border",
                getDifficultyColor(wordDifficulty)
              )}
            >
              {wordDifficulty.charAt(0).toUpperCase() + wordDifficulty.slice(1)}
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {" "}
          {/* Side-by-side comparison */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Your Answer */}
            <Card className="border-red-200 bg-red-50/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <X className="h-4 w-4 text-red-600" />
                  <h3 className="font-semibold text-red-900">Your Answer</h3>
                </div>
                <div className="text-2xl font-mono font-bold text-red-800 bg-white p-3 rounded-lg border border-red-200 min-h-[60px] flex items-center justify-center">
                  {userAnswer || (
                    <span className="text-gray-400 text-lg font-normal">
                      No answer provided
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Correct Answer */}
            <Card className="border-green-200 bg-green-50/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <h3 className="font-semibold text-green-900">
                    Correct Answer
                  </h3>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex-1 text-2xl font-mono font-bold text-green-800 bg-white p-3 rounded-lg border border-green-200 min-h-[60px] flex items-center justify-center">
                    {correctAnswer}
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handlePlayAudio}
                    disabled={isPlayingAudio}
                    className="border-green-300 text-green-700 hover:bg-green-50 flex-shrink-0"
                    title="Play pronunciation"
                  >
                    <Volume2
                      className={cn(
                        "h-4 w-4",
                        isPlayingAudio && "animate-pulse"
                      )}
                    />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Letter-by-letter comparison */}
          {userAnswer && correctAnswer && (
            <Card className="border-amber-200 bg-amber-50/30">
              <CardContent className="p-4">
                <h3 className="font-semibold text-amber-900 mb-3 flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Letter Comparison
                </h3>
                <div className="flex flex-wrap gap-1 justify-center">
                  {letterComparison.map((item, index) => (
                    <div key={index} className="text-center">
                      <div
                        className={cn(
                          "w-10 h-10 flex items-center justify-center text-lg font-mono font-bold rounded-lg border-2 mb-1",
                          item.status === "correct" &&
                            "bg-green-100 border-green-300 text-green-800",
                          item.status === "incorrect" &&
                            "bg-red-100 border-red-300 text-red-800",
                          item.status === "missing" &&
                            "bg-yellow-100 border-yellow-300 text-yellow-800",
                          item.status === "extra" &&
                            "bg-orange-100 border-orange-300 text-orange-800"
                        )}
                      >
                        {item.status === "missing"
                          ? item.correctLetter
                          : item.userLetter}
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.position + 1}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex flex-wrap gap-4 justify-center mt-4 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                    <span className="text-gray-600">Correct</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
                    <span className="text-gray-600">Wrong Letter</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded"></div>
                    <span className="text-gray-600">Missing Letter</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
                    <span className="text-gray-600">Extra Letter</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}{" "}
          {/* Learning Tips */}
          <Card className="border-blue-200 bg-blue-50/30">
            <CardContent className="p-4">
              <h3 className="font-semibold text-blue-900 mb-3">
                💡 Personalized Learning Tips
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                {specificTips.length > 0 ? (
                  specificTips.map((tip, index) => <li key={index}>{tip}</li>)
                ) : (
                  <>
                    <li>• Pay attention to common letter patterns</li>
                    <li>• Listen carefully to the pronunciation</li>
                    <li>• Break down complex words into syllables</li>
                    <li>• Practice similar words to build muscle memory</li>
                  </>
                )}
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={onClose}
            className="bg-amber-600 hover:bg-amber-700 text-white"
          >
            Got it, thanks!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
