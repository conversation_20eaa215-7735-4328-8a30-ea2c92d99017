// Analytics utilities for battle pass management
import { createClient } from "../../supabase/client";

const supabase = createClient();

export interface BattlePassAnalytics {
  totalPlayers: number;
  premiumPlayers: number;
  conversionRate: number;
  averageTier: number;
  totalXpEarned: number;
  activePlayers: number;
  seasonProgress: {
    seasonId: string;
    seasonName: string;
    playerCount: number;
    averageTier: number;
    premiumPercentage: number;
  }[];
}

export interface PlayerProgressData {
  tierId: number;
  playerCount: number;
  premiumCount: number;
}

export interface TierCompletionData {
  tierNumber: number;
  totalPlayers: number;
  completedPlayers: number;
  completionRate: number;
}

export interface ConversionData {
  date: string;
  newPlayers: number;
  premiumUpgrades: number;
  conversionRate: number;
}

/**
 * Get comprehensive battle pass analytics
 */
export async function getBattlePassAnalytics(
  seasonFilter: string = 'all', 
  timeRangeDays: string = '30'
): Promise<BattlePassAnalytics> {
  try {
    const timeRange = parseInt(timeRangeDays);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - timeRange);

    // Get all player battle pass data
    let query = supabase
      .from('player_battle_pass')
      .select(`
        *,
        battle_pass_seasons!inner(
          id,
          season_name,
          is_active
        )
      `);

    // Apply season filter
    if (seasonFilter === 'current') {
      query = query.eq('battle_pass_seasons.is_active', true);
    }

    // Apply time filter
    if (timeRange < 365) {
      query = query.gte('last_updated', cutoffDate.toISOString());
    }

    const { data: playerData, error } = await query;

    if (error) {
      console.error('Error fetching analytics data:', error);
      return getDefaultAnalytics();
    }

    const players = playerData || [];
    
    // Calculate basic metrics
    const totalPlayers = players.length;
    const premiumPlayers = players.filter(p => p.has_premium).length;
    const conversionRate = totalPlayers > 0 ? (premiumPlayers / totalPlayers) * 100 : 0;
    const averageTier = totalPlayers > 0 ? players.reduce((sum, p) => sum + p.current_tier, 0) / totalPlayers : 0;
    const totalXpEarned = players.reduce((sum, p) => sum + p.current_xp, 0);
    
    // Calculate active players (updated in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const activePlayers = players.filter(p => new Date(p.last_updated) >= sevenDaysAgo).length;

    // Group by season for season progress
    const seasonGroups = players.reduce((acc, player) => {
      const season = player.battle_pass_seasons;
      if (!season) return acc;
      
      if (!acc[season.id]) {
        acc[season.id] = {
          seasonId: season.id,
          seasonName: season.season_name,
          players: []
        };
      }
      acc[season.id].players.push(player);
      return acc;
    }, {} as Record<string, any>);

    const seasonProgress = Object.values(seasonGroups).map((group: any) => ({
      seasonId: group.seasonId,
      seasonName: group.seasonName,
      playerCount: group.players.length,
      averageTier: group.players.reduce((sum: number, p: any) => sum + p.current_tier, 0) / group.players.length,
      premiumPercentage: (group.players.filter((p: any) => p.has_premium).length / group.players.length) * 100
    }));

    return {
      totalPlayers,
      premiumPlayers,
      conversionRate,
      averageTier,
      totalXpEarned,
      activePlayers,
      seasonProgress
    };

  } catch (error) {
    console.error('Exception in getBattlePassAnalytics:', error);
    return getDefaultAnalytics();
  }
}

/**
 * Get player progress distribution across tiers
 */
export async function getPlayerProgressDistribution(seasonFilter: string = 'all'): Promise<PlayerProgressData[]> {
  try {
    let query = supabase
      .from('player_battle_pass')
      .select(`
        current_tier,
        has_premium,
        battle_pass_seasons!inner(is_active)
      `);

    if (seasonFilter === 'current') {
      query = query.eq('battle_pass_seasons.is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching progress distribution:', error);
      return [];
    }

    const players = data || [];
    
    // Group by tier
    const tierGroups = players.reduce((acc, player) => {
      const tier = player.current_tier;
      if (!acc[tier]) {
        acc[tier] = {
          tierId: tier,
          playerCount: 0,
          premiumCount: 0
        };
      }
      acc[tier].playerCount++;
      if (player.has_premium) {
        acc[tier].premiumCount++;
      }
      return acc;
    }, {} as Record<number, PlayerProgressData>);

    return Object.values(tierGroups).sort((a, b) => a.tierId - b.tierId);

  } catch (error) {
    console.error('Exception in getPlayerProgressDistribution:', error);
    return [];
  }
}

/**
 * Get tier completion rates
 */
export async function getTierCompletionRates(seasonFilter: string = 'all'): Promise<TierCompletionData[]> {
  try {
    // Get current season ID if needed
    let seasonId = null;
    if (seasonFilter === 'current') {
      const { data: currentSeason } = await supabase
        .from('battle_pass_seasons')
        .select('id')
        .eq('is_active', true)
        .single();
      
      if (currentSeason) {
        seasonId = currentSeason.id;
      }
    }

    // Get tiers for the season
    let tiersQuery = supabase
      .from('battle_pass_tiers')
      .select('tier_number, season_id')
      .order('tier_number');

    if (seasonId) {
      tiersQuery = tiersQuery.eq('season_id', seasonId);
    }

    const { data: tiers, error: tiersError } = await tiersQuery;

    if (tiersError) {
      console.error('Error fetching tiers:', tiersError);
      return [];
    }

    // Get player progress
    let playersQuery = supabase
      .from('player_battle_pass')
      .select('current_tier, season_id');

    if (seasonId) {
      playersQuery = playersQuery.eq('season_id', seasonId);
    }

    const { data: players, error: playersError } = await playersQuery;

    if (playersError) {
      console.error('Error fetching player progress:', playersError);
      return [];
    }

    const playerProgress = players || [];
    const tierList = tiers || [];

    // Calculate completion rates
    const completionRates = tierList.map(tier => {
      const totalPlayers = playerProgress.filter(p => p.season_id === tier.season_id).length;
      const completedPlayers = playerProgress.filter(p => 
        p.season_id === tier.season_id && p.current_tier >= tier.tier_number
      ).length;
      
      return {
        tierNumber: tier.tier_number,
        totalPlayers,
        completedPlayers,
        completionRate: totalPlayers > 0 ? (completedPlayers / totalPlayers) * 100 : 0
      };
    });

    // Group by tier number and average across seasons if needed
    const tierGroups = completionRates.reduce((acc, tier) => {
      if (!acc[tier.tierNumber]) {
        acc[tier.tierNumber] = {
          tierNumber: tier.tierNumber,
          totalPlayers: 0,
          completedPlayers: 0,
          completionRate: 0
        };
      }
      acc[tier.tierNumber].totalPlayers += tier.totalPlayers;
      acc[tier.tierNumber].completedPlayers += tier.completedPlayers;
      return acc;
    }, {} as Record<number, TierCompletionData>);

    // Recalculate completion rates
    return Object.values(tierGroups).map(tier => ({
      ...tier,
      completionRate: tier.totalPlayers > 0 ? (tier.completedPlayers / tier.totalPlayers) * 100 : 0
    })).sort((a, b) => a.tierNumber - b.tierNumber);

  } catch (error) {
    console.error('Exception in getTierCompletionRates:', error);
    return [];
  }
}

/**
 * Get premium conversion data over time
 */
export async function getPremiumConversionData(days: number = 30): Promise<ConversionData[]> {
  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get daily player registrations and premium upgrades
    const { data: players, error } = await supabase
      .from('player_battle_pass')
      .select('has_premium, last_updated')
      .gte('last_updated', startDate.toISOString())
      .lte('last_updated', endDate.toISOString());

    if (error) {
      console.error('Error fetching conversion data:', error);
      return [];
    }

    const playerData = players || [];
    
    // Group by date
    const dailyData = playerData.reduce((acc, player) => {
      const date = new Date(player.last_updated).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = {
          date,
          newPlayers: 0,
          premiumUpgrades: 0,
          conversionRate: 0
        };
      }
      acc[date].newPlayers++;
      if (player.has_premium) {
        acc[date].premiumUpgrades++;
      }
      return acc;
    }, {} as Record<string, ConversionData>);

    // Calculate conversion rates
    return Object.values(dailyData).map(day => ({
      ...day,
      conversionRate: day.newPlayers > 0 ? (day.premiumUpgrades / day.newPlayers) * 100 : 0
    })).sort((a, b) => a.date.localeCompare(b.date));

  } catch (error) {
    console.error('Exception in getPremiumConversionData:', error);
    return [];
  }
}

/**
 * Get revenue analytics (placeholder - requires payment integration)
 */
export async function getRevenueAnalytics(days: number = 30): Promise<{
  totalRevenue: number;
  dailyRevenue: { date: string; revenue: number }[];
  averageRevenuePerUser: number;
}> {
  // This would integrate with payment systems
  // For now, return placeholder data
  return {
    totalRevenue: 0,
    dailyRevenue: [],
    averageRevenuePerUser: 0
  };
}

/**
 * Default analytics data for fallback
 */
function getDefaultAnalytics(): BattlePassAnalytics {
  return {
    totalPlayers: 0,
    premiumPlayers: 0,
    conversionRate: 0,
    averageTier: 0,
    totalXpEarned: 0,
    activePlayers: 0,
    seasonProgress: []
  };
}
