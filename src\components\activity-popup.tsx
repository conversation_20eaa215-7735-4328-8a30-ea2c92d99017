'use client'

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, Trophy, Target, Flame, Calendar, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getUserMatchHistory, type MatchHistoryEntry } from '@/utils/profile-utils';
import { createClient } from '../../supabase/client';

interface ActivityPopupProps {
  children: React.ReactNode;
}

export default function ActivityPopup({ children }: ActivityPopupProps) {
  const [recentActivity, setRecentActivity] = useState<MatchHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      loadRecentActivity();
    }
  }, [open]);

  const loadRecentActivity = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const activity = await getUserMatchHistory(user.id, 10);
        setRecentActivity(activity);
      }
    } catch (error) {
      console.error('Error loading recent activity:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return `${Math.floor(diffInHours / 168)}w ago`;
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-4 w-4 text-yellow-500" />;
      case 2: return <Trophy className="h-4 w-4 text-gray-400" />;
      case 3: return <Trophy className="h-4 w-4 text-amber-600" />;
      default: return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-700 border-green-200';
      case 'medium': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'hard': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'extreme': return 'bg-purple-100 text-purple-700 border-purple-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-amber-900">
            <Clock className="h-5 w-5 text-amber-600" />
            Recent Activity
          </DialogTitle>
        </DialogHeader>
        
        <div className="overflow-y-auto max-h-[60vh] pr-2">
          {loading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : recentActivity.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Recent Activity</h3>
              <p className="text-gray-500">Start playing to see your match history here!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentActivity.map((activity, index) => (
                <Card key={activity.match_id} className={cn(
                  "transition-all duration-200 hover:shadow-md cursor-pointer",
                  index === 0 ? "ring-2 ring-amber-200 bg-amber-50/50" : "hover:bg-gray-50"
                )}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="flex-shrink-0 mt-1">
                          {getRankIcon(activity.final_rank)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={cn(
                              "text-xs font-medium capitalize",
                              getDifficultyColor(activity.difficulty)
                            )}>
                              {activity.difficulty}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              {activity.total_players} players
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <Trophy className="h-3 w-3 text-amber-600" />
                              <span className="font-medium">
                                {activity.final_rank === 1 ? '🥇 Winner!' : 
                                 activity.final_rank === 2 ? '🥈 2nd Place' :
                                 activity.final_rank === 3 ? '🥉 3rd Place' :
                                 `#${activity.final_rank}`}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3 text-blue-600" />
                              <span>{activity.score.toLocaleString()} pts</span>
                            </div>
                            
                            {activity.elimination_round && (
                              <div className="flex items-center gap-1">
                                <Flame className="h-3 w-3 text-red-500" />
                                <span>Round {activity.elimination_round}</span>
                              </div>
                            )}
                          </div>
                          
                          <div className="mt-2 text-xs text-gray-500">
                            Lasted {activity.current_round} rounds • {Math.floor(activity.match_duration_seconds / 60)}m {activity.match_duration_seconds % 60}s
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex flex-col items-end text-right ml-4">
                        <Badge variant="outline" className="text-xs mb-1">
                          {formatTimeAgo(activity.ended_at)}
                        </Badge>
                        {index === 0 && (
                          <Badge className="text-xs bg-amber-100 text-amber-800 border-amber-200">
                            Latest
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
        
        <div className="flex justify-center pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={() => {
              setOpen(false);
              window.location.href = '/profile';
            }}
            className="text-amber-700 border-amber-200 hover:bg-amber-50"
          >
            View Full Profile
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
