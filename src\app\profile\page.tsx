'use client';

import React, { useEffect, useState } from 'react';
import { createClient } from '../../../supabase/client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Trophy,
  History,
  Flame,
  Award,
  Settings,
  Calendar,
  BarChart4,
  Star,
  Volume2,
  VolumeX,
  Edit,
  Crown,
  Target,
  TrendingUp,
  Users,
  Clock,
  Medal,
  Zap,
  Shield,
  Lock,
  Check,
  BookOpen,
  Palette,
  Bell
} from "lucide-react";
import StyledAvatar from "@/components/styled-avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { getUserMatchHistory, getPlayerStats, formatMatchDuration, getRankDisplay, getDifficultyColor } from "@/utils/profile-utils";
import { BACKGROUND_THEMES, getPlayerEquippedItems, PlayerItem } from "@/utils/customization-utils";
import ProfileEditPopup from "@/components/profile-edit-popup";
import RankDisplay from "@/components/rank-display";
import PrivacySettings from "@/components/privacy-settings";
import CustomizationPanel from "@/components/customization-panel";
import { getRankInfo } from "@/utils/ranking-system";
import MatchHistorySection from "@/components/match-history-section";
import { Skeleton } from "@/components/ui/skeleton";

export default function ProfilePage() {
  const [user, setUser] = useState<any>(null);
  const [playerData, setPlayerData] = useState<any>(null);
  const [matchHistory, setMatchHistory] = useState<any[]>([]);
  const [playerStats, setPlayerStats] = useState<any>({
    total_matches: 0,
    wins: 0,
    win_rate: 0,
    highest_score: 0,
    longest_streak: 0,
    total_score: 0,
    avg_score: 0,
    favorite_difficulty: 'easy',
    total_eliminations: 0,
    survival_rate: 0
  });
  const [equippedItems, setEquippedItems] = useState<{
    avatar?: PlayerItem;
    avatarBorder?: PlayerItem;
    background?: PlayerItem;
    title?: PlayerItem;
  }>({});
  const [loading, setLoading] = useState(true);
  
  const router = useRouter();
  const supabase = createClient();
  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (!user?.id) return;

    // Set up realtime subscription for equipped items changes
    const equippedItemsChannel = supabase
      .channel('profile-equipped-items')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'players',
          filter: `id=eq.${user.id}`
        },
        async (payload) => {
          console.log('Player equipped items updated in profile:', payload);
          
          // Reload equipped items when changes are detected
          await loadEquippedItems();
          
          // Also update player data for RP/level changes
          setPlayerData(payload.new);
        }
      )
      .subscribe();

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(equippedItemsChannel);
    };
  }, [user?.id]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        router.push("/sign-in");
        return;
      }

      setUser(currentUser);

      // Get user's player data
      const { data: userData } = await supabase
        .from('players')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      setPlayerData(userData);

      // Get real data and equipped items
      const [matchHistoryData, playerStatsData, equippedItemsData] = await Promise.all([
        getUserMatchHistory(currentUser.id, 20),
        getPlayerStats(currentUser.id),
        getPlayerEquippedItems(currentUser.id)
      ]);

      setMatchHistory(matchHistoryData);
      setPlayerStats(playerStatsData);
      setEquippedItems(equippedItemsData);

    } catch (error) {
      console.error('Error loading profile data:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const loadEquippedItems = async () => {
    if (!user?.id) return;

    try {
      const updatedEquippedItems = await getPlayerEquippedItems(user.id);
      setEquippedItems(updatedEquippedItems);
    } catch (error) {
      console.error('Error loading equipped items:', error);
    }
  };
  if (loading) {
    return (
      <>
        <DashboardNavbar />
        <main className="w-full bg-gradient-to-br from-amber-50/60 via-orange-50/30 to-yellow-50/40 min-h-screen">
          <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-6 md:gap-8">
            {/* Header Section Skeleton */}
            <header className="flex flex-col gap-2 sm:gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-amber-100 rounded-xl border border-amber-200">
                  <Skeleton className="h-5 w-5" />
                </div>
                <Skeleton className="h-8 w-32" />
              </div>
              <div className="bg-gradient-to-r from-amber-100 to-orange-100 p-3 sm:p-4 px-4 sm:px-5 rounded-xl border border-amber-200">
                <div className="flex gap-3 items-center">
                  <div className="p-1.5 bg-amber-200 rounded-lg">
                    <Skeleton className="h-3.5 w-3.5" />
                  </div>
                  <Skeleton className="h-4 w-80" />
                </div>
              </div>
            </header>

            {/* Profile Layout Grid Skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left Column - Profile Card Skeleton */}
              <div className="lg:col-span-1">
                <div className="bg-white/95 rounded-lg border border-amber-200 overflow-hidden shadow-lg">
                  {/* Profile Header with Background */}
                  <div className="bg-gradient-to-br from-amber-100 via-amber-50 to-amber-100 pb-4 sm:pb-6 relative">
                    {/* Edit Button */}
                    <div className="absolute top-2 sm:top-4 right-2 sm:right-4 z-10">
                      <Skeleton className="h-8 w-8 sm:h-9 sm:w-9 rounded-full" />
                    </div>

                    <div className="flex flex-col items-center pt-4 sm:pt-6">
                      {/* Avatar */}
                      <div className="relative">
                        <Skeleton className="h-20 w-20 sm:h-24 sm:w-24 rounded-full" />
                        <Skeleton className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full" />
                      </div>
                      
                      {/* Name */}
                      <Skeleton className="mt-3 sm:mt-4 h-6 sm:h-8 w-40" />
                      
                      {/* Title */}
                      <div className="mt-2 p-2 rounded-lg bg-amber-50">
                        <Skeleton className="h-4 w-32" />
                      </div>
                      
                      {/* Rank Display */}
                      <div className="mt-2">
                        <Skeleton className="h-8 w-24" />
                      </div>
                    </div>
                  </div>

                  {/* Profile Stats */}
                  <div className="pt-4 sm:pt-6 px-3 sm:px-6 pb-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-3 w-32" />
                    </div>

                    <div className="space-y-3 sm:space-y-4 mt-4 sm:mt-6">
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 w-28" />
                      </div>

                      <div className="space-y-3">
                        {Array.from({ length: 5 }).map((_, index) => (
                          <div key={index}>
                            <div className="flex justify-between text-sm mb-1">
                              <Skeleton className="h-4 w-20" />
                              <Skeleton className="h-4 w-12" />
                            </div>
                            <Skeleton className="h-2 w-full rounded-full" />
                          </div>
                        ))}
                      </div>

                      {/* Rank Progress */}
                      <div className="mt-6 pt-4 border-t border-amber-200">
                        <div className="space-y-3">
                          <Skeleton className="h-4 w-24" />
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <Skeleton className="h-3 w-16" />
                              <Skeleton className="h-3 w-20" />
                            </div>
                            <Skeleton className="h-2 w-full rounded-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Tabs Skeleton */}
              <div className="lg:col-span-2">
                <div className="w-full">
                  {/* Tabs Header */}
                  <div className="grid grid-cols-4 bg-amber-100/50 p-1 sm:p-2 rounded-lg mb-6 h-auto">
                    {Array.from({ length: 4 }).map((_, index) => (
                      <Skeleton key={index} className="h-8 sm:h-10 mx-1" />
                    ))}
                  </div>

                  {/* Tab Content */}
                  <div className="border shadow-sm rounded-lg bg-white">
                    {/* Tab Header */}
                    <div className="pb-2 px-3 sm:px-6 pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Skeleton className="h-5 w-5" />
                        <Skeleton className="h-6 w-32" />
                      </div>
                      <Skeleton className="h-4 w-64" />
                    </div>

                    {/* Tab Content Area */}
                    <div className="px-3 sm:px-6 pb-6">
                      {/* Achievements Grid Skeleton */}
                      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4 mt-2 sm:mt-4">
                        {Array.from({ length: 6 }).map((_, index) => (
                          <div key={index} className="flex flex-col items-center p-2 sm:p-4 rounded-lg border border-amber-200">
                            <div className="p-2 sm:p-3 rounded-full mb-1 sm:mb-2 bg-amber-200">
                              <Skeleton className="h-4 w-4 sm:h-6 sm:w-6" />
                            </div>
                            <Skeleton className="h-4 w-16 mb-1" />
                            <Skeleton className="h-3 w-20" />
                          </div>
                        ))}
                      </div>

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </>
    );
  }

  if (!user || !playerData) {
    return null;
  }

  // Get user's display name and customization data
  const displayName = playerData?.display_name || 'Anonymous Player';
  
  // Use equipped avatar from new system or fallback to old system
  const currentAvatar = equippedItems.avatar as PlayerItem;
  
  const currentTitle = equippedItems.title as PlayerItem;

  // Use equipped background from new system or fallback to old system
  const currentBackground = equippedItems.background as PlayerItem;
  
  // Use equipped border from new system or fallback to old system  
  const currentBorder = equippedItems.avatarBorder as PlayerItem;
    
  const currentRP = playerData?.rankpoints || 0;
  const currentXP = playerData?.level_experience || 0;

  // Get rank information from RP
  const rankInfo = getRankInfo(currentRP);
  const userLevel = rankInfo.tierOrder; // Use tier order for compatibility with existing code
  const joinDate = new Date(user.created_at).toLocaleDateString('en-US', {
    month: 'long',
    year: 'numeric'
  });

  // Generate title based on stats
  const generateTitle = () => {
    if (playerStats.total_matches === 0) return "New Player";
    if (playerStats.win_rate >= 80) return "Champion ⭐ | Undefeated";
    if (playerStats.win_rate >= 60) return "Spell Master ⭐";
    if (playerStats.win_rate >= 40) return "Word Warrior";
    if (playerStats.total_matches >= 50) return "Veteran Player";
    return "Rising Speller";
  };

  // Badge system
  const achievements = [
    {
      name: "First Victory",
      icon: Trophy,
      unlocked: playerStats.wins >= 1,
      description: "Win your first match",
      rarity: "common"
    },
    {
      name: "Speed Demon",
      icon: Zap,
      unlocked: playerStats.total_matches >= 10,
      description: "Play 10 matches",
      rarity: "common"
    },
    {
      name: "Word Wizard",
      icon: BookOpen,
      unlocked: playerStats.highest_score >= 1000,
      description: "Score over 1000 points",
      rarity: "rare"
    },
    {
      name: "Streak Master",
      icon: Flame,
      unlocked: playerStats.longest_streak >= 10,
      description: "Achieve a 10-word streak",
      rarity: "rare"
    },
    {
      name: "Champion",
      icon: Crown,
      unlocked: playerStats.wins >= 10,
      description: "Win 10 matches",
      rarity: "epic"
    },
    {
      name: "Perfect Game",
      icon: Star,
      unlocked: playerStats.win_rate >= 90 && playerStats.total_matches >= 10,
      description: "Maintain 90%+ win rate",
      rarity: "legendary"
    }
  ];

  return (
    <>
      <DashboardNavbar />      <main className="w-full bg-gradient-to-br from-amber-50/60 via-orange-50/30 to-yellow-50/40 min-h-screen">
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-6 md:gap-8">
          {/* Header Section */}
          <header className="flex flex-col gap-2 sm:gap-4">
            <h1 className="text-2xl sm:text-3xl font-bold text-amber-900 flex items-center gap-3">
              <div className="p-2 bg-amber-100 rounded-xl border border-amber-200">
                <BookOpen size={20} className="text-amber-700" />
              </div>
              My Profile
            </h1>
            <div className="bg-gradient-to-r from-amber-100 to-orange-100 text-xs sm:text-sm p-3 sm:p-4 px-4 sm:px-5 rounded-xl text-amber-800 flex gap-3 items-center border border-amber-200 shadow-sm">
              <div className="p-1.5 bg-amber-200 rounded-lg">
                <Palette size={14} className="text-amber-700" />
              </div>
              <span className="font-medium">Customize your avatar, collect titles, and show off your achievements</span>
            </div>
          </header>

          {/* Profile Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Profile Card */}
            <div className="lg:col-span-1">              
              <Card className="bg-white/95 backdrop-blur-sm border-amber-200 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">                
              <CardHeader 
                  className={cn(
                    "pb-4 sm:pb-6 relative bg-cover bg-center",
                    !equippedItems.background?.image_url && equippedItems?.background?.value ? BACKGROUND_THEMES[equippedItems.background?.value as keyof typeof BACKGROUND_THEMES] : 'bg-gradient-to-br from-amber-100 via-amber-50 to-amber-100'
                  )}
                  style={equippedItems.background?.image_url ? {
                    backgroundImage: `url(${equippedItems.background.image_url})`
                  } : undefined }
                >
                  {/* Enhanced overlay for better text readability */}
                  {equippedItems.background?.image_url && (
                    <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40" />
                  )}
                  
                  <div className="absolute top-2 sm:top-4 right-2 sm:right-4 z-10">                      
                    <ProfileEditPopup
                      currentDisplayName={displayName}
                      currentTitle={currentTitle}
                      currentBackground={currentBackground}
                      currentAvatar={currentAvatar}
                      currentBorder={currentBorder}
                      userLevel={userLevel}
                      userId={user.id}
                      currentRP={currentRP}
                      currentXP={currentXP}
                    >
                      <Button variant="ghost" size="icon" className="h-8 w-8 sm:h-9 sm:w-9 rounded-full bg-white/95 hover:bg-white shadow-lg border border-white/20 backdrop-blur-sm">
                        <Edit size={14} className="text-amber-800 sm:hidden" />
                        <Edit size={16} className="text-amber-800 hidden sm:inline" />
                      </Button>
                    </ProfileEditPopup>
                  </div>

                  <div className="flex flex-col items-center relative z-10">
                    <div className="relative">
                      <div 
                        className="absolute inset-0 rounded-full bg-cover bg-center scale-110"
                      />
                        <StyledAvatar
                          src={currentAvatar?.image_url ? currentAvatar.image_url : playerData.avatar_url}
                          alt={displayName}
                          fallback={displayName.substring(0, 2).toUpperCase()}
                          size="2xl"
                          border={currentBorder?.image_url}
                          totalXP={currentXP}
                          className="relative z-10"
                        />
                    </div>
                  
                      <CardTitle className={cn(
                        "mt-3 sm:mt-4 text-xl sm:text-2xl font-bold",
                        equippedItems.background?.image_url 
                          ? "text-white drop-shadow-lg shadow-black/50" 
                          : "text-amber-900"
                      )}>
                        {displayName}
                      </CardTitle>
                    
                      {currentTitle?.value && (
                        <div className={cn(
                          "text-center p-2 rounded-lg mt-2",
                          equippedItems.background?.image_url 
                            ? "bg-white/95 backdrop-blur-sm border border-white/20 shadow-lg" 
                            : "bg-amber-50"
                        )}>
                          <span className="text-sm font-medium text-amber-900">
                            {currentTitle?.value}
                          </span>
                        </div>
                      )}
                    
                      <div className={cn("mt-2 rounded-lg")}>
                        <RankDisplay
                          rp={currentRP}
                          variant="compact"
                          showProgress={false}
                          showRP={false}
                        />
                      </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4 sm:pt-6 px-3 sm:px-6">
                  <div className="flex items-center gap-2 mb-4 text-amber-800">
                    <Calendar size={14} className="sm:hidden" />
                    <Calendar size={16} className="hidden sm:inline" />
                    <span className="text-xs sm:text-sm">Member since {joinDate}</span>
                  </div>

                  <div className="space-y-3 sm:space-y-4 mt-4 sm:mt-6">
                    <h3 className="font-semibold text-amber-900 flex items-center gap-2 text-sm sm:text-base">
                      <BarChart4 size={16} className="text-amber-700 sm:hidden" />
                      <BarChart4 size={18} className="text-amber-700 hidden sm:inline" />
                      Stats Overview
                    </h3>

                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Games Played</span>
                          <span className="font-medium text-amber-900">{playerStats.total_matches}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-amber-500" style={{ width: '100%' }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Wins ({playerStats.win_rate}% win rate)</span>
                          <span className="font-medium text-amber-900">{playerStats.wins}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-green-500" style={{ width: `${playerStats.win_rate}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Highest Score</span>
                          <span className="font-medium text-amber-900">{playerStats.highest_score.toLocaleString()}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-blue-500" style={{ width: `${Math.min(100, playerStats.highest_score / 100)}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Longest Streak</span>
                          <span className="font-medium text-amber-900">{playerStats.longest_streak} words</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-red-500" style={{ width: `${Math.min(100, playerStats.longest_streak * 5)}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Survival Rate</span>
                          <span className="font-medium text-amber-900">{playerStats.survival_rate}%</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-purple-500" style={{ width: `${playerStats.survival_rate}%` }}></div>
                        </div>
                      </div>
                    </div>

                    {/* Rank Progress Section */}
                    <div className="mt-6 pt-4 border-t border-amber-200">
                      <RankDisplay
                        rp={currentRP}
                        variant="detailed"
                        showProgress={true}
                        showRP={true}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>            {/* Right Column - Tabs for different sections */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="achievements" className="w-full">
                <TabsList className="grid grid-cols-4 sm:mb-6 bg-amber-100/50 p-1 sm:p-2 rounded-lg h-auto">
                  <TabsTrigger value="achievements" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Award size={14} className="mr-1 sm:mr-2" />
                    <span>Achievements</span>
                  </TabsTrigger>
                  <TabsTrigger value="customization" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Palette size={14} className="mr-1 sm:mr-2" />
                    <span>Customize</span>
                  </TabsTrigger>
                  <TabsTrigger value="history" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <History size={14} className="mr-1 sm:mr-2" />
                    <span>History</span>
                  </TabsTrigger>
                  <TabsTrigger value="settings" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Settings size={14} className="mr-1 sm:mr-2" />
                    <span>Settings</span>
                  </TabsTrigger>
                </TabsList>

                {/* Achievements Tab */}
                <TabsContent value="achievements" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Award className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Achievements
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Unlock Achievements by completing special challenges
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4 mt-2 sm:mt-4">
                        {achievements.map((badge, index) => {
                          const getRarityColor = (rarity: string) => {
                            switch (rarity) {
                              case 'common': return 'border-gray-300 bg-gray-50';
                              case 'rare': return 'border-blue-300 bg-blue-50';
                              case 'epic': return 'border-purple-300 bg-purple-50';
                              case 'legendary': return 'border-yellow-300 bg-yellow-50';
                              default: return 'border-gray-300 bg-gray-50';
                            }
                          };

                          return (
                            <div
                              key={index}
                              className={cn(
                                "flex flex-col items-center p-2 sm:p-4 rounded-lg border text-center transition-all duration-200",
                                badge.unlocked
                                  ? `${getRarityColor(badge.rarity)} hover:shadow-md`
                                  : "bg-gray-100 border-gray-200 opacity-70"
                              )}
                            >
                              <div className={cn(
                                "p-2 sm:p-3 rounded-full mb-1 sm:mb-2 relative",
                                badge.unlocked ? "bg-amber-200" : "bg-gray-200"
                              )}>
                                <badge.icon
                                  size={16}
                                  className={cn(
                                    "sm:hidden",
                                    badge.unlocked ? "text-amber-700" : "text-gray-500"
                                  )}
                                />
                                <badge.icon
                                  size={24}
                                  className={cn(
                                    "hidden sm:block",
                                    badge.unlocked ? "text-amber-700" : "text-gray-500"
                                  )}
                                />
                                {!badge.unlocked && (
                                  <Lock size={12} className="absolute -top-1 -right-1 text-gray-400" />
                                )}
                                {badge.unlocked && (
                                  <Check size={12} className="absolute -top-1 -right-1 text-green-500 bg-white rounded-full" />
                                )}
                              </div>
                              <h3 className={cn(
                                "font-medium mb-0.5 sm:mb-1 text-xs sm:text-sm",
                                badge.unlocked ? "text-amber-900" : "text-gray-600"
                              )}>
                                {badge.name}
                              </h3>
                              <Badge
                                variant="outline"
                                className={cn(
                                  "text-[10px] mb-1",
                                  badge.rarity === 'common' && "border-gray-300 text-gray-600",
                                  badge.rarity === 'rare' && "border-blue-300 text-blue-600",
                                  badge.rarity === 'epic' && "border-purple-300 text-purple-600",
                                  badge.rarity === 'legendary' && "border-yellow-300 text-yellow-600"
                                )}
                              >
                                {badge.rarity}
                              </Badge>
                              <p className="text-[10px] sm:text-xs text-gray-600 line-clamp-2">
                                {badge.description}
                              </p>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>                  </Card>
                </TabsContent>

                {/* Customization Tab */}
                <TabsContent value="customization" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Palette className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Customize Your Profile
                      </CardTitle>                      
                      <CardDescription className="text-xs sm:text-sm">
                        Unlock and equip avatars, avatar borders, backgrounds, and titles
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">                    
                    <CustomizationPanel
                      playerId={user.id}
                      playerLevel={userLevel}
                      playerRP={currentRP}
                      hasBattlePass={false}
                      battlePassTier={0}
                      currentDisplayName={displayName}
                    />
                    </CardContent>
                  </Card>
                </TabsContent>                
                {/* Match History Tab */}
                <TabsContent value="history" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <History className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Match History
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Your recent matches and detailed performance analysis
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <MatchHistorySection userId={user.id} />
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="mt-0">
                  <div className="space-y-6">
                    {/* Privacy Settings */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2 px-3 sm:px-6">
                        <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                          <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                          Privacy & Safety
                        </CardTitle>
                        <CardDescription className="text-xs sm:text-sm">
                          Control your profile visibility and safety settings
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6">
                        <PrivacySettings userId={user.id} />
                      </CardContent>
                    </Card>

                    {/* Audio & Game Settings */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2 px-3 sm:px-6">
                        <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                          <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                          Game Preferences
                        </CardTitle>
                        <CardDescription className="text-xs sm:text-sm">
                          Customize your gameplay experience
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6">
                        <div className="space-y-4 sm:space-y-6">
                          {/* Audio Settings */}
                          <div>
                            <h3 className="font-medium text-amber-900 mb-2 sm:mb-3 text-sm sm:text-base">Audio Settings</h3>
                            <div className="space-y-2 sm:space-y-3">
                              <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                                <div className="flex items-center gap-2 sm:gap-3">
                                  <Volume2 size={14} className="text-amber-700 sm:hidden" />
                                  <Volume2 size={18} className="text-amber-700 hidden sm:block" />
                                  <span className="text-amber-900 text-xs sm:text-sm">Word Pronunciations</span>
                                </div>
                                <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-amber-600">
                                  <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-[18px] sm:translate-x-6"></span>
                                </div>
                              </div>

                              <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                                <div className="flex items-center gap-2 sm:gap-3">
                                  <VolumeX size={14} className="text-amber-700 sm:hidden" />
                                  <VolumeX size={18} className="text-amber-700 hidden sm:block" />
                                  <span className="text-amber-900 text-xs sm:text-sm">Background Music</span>
                                </div>
                                <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-gray-300">
                                  <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-1"></span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Game Settings */}
                          <div>
                            <h3 className="font-medium text-amber-900 mb-2 sm:mb-3 text-sm sm:text-base">Game Settings</h3>
                            <div className="space-y-2 sm:space-y-3">
                              <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                                <div className="flex items-center gap-2 sm:gap-3">
                                  <Zap size={14} className="text-amber-700 sm:hidden" />
                                  <Zap size={18} className="text-amber-700 hidden sm:block" />
                                  <span className="text-amber-900 text-xs sm:text-sm">Auto-skip Countdown</span>
                                </div>
                                <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-gray-300">
                                  <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-1"></span>
                                </div>
                              </div>

                              <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                                <div className="flex items-center gap-2 sm:gap-3">
                                  <Bell size={14} className="text-amber-700 sm:hidden" />
                                  <Bell size={18} className="text-amber-700 hidden sm:block" />
                                  <span className="text-amber-900 text-xs sm:text-sm">Match Notifications</span>
                                </div>
                                <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-amber-600">
                                  <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-[18px] sm:translate-x-6"></span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-end pt-0 px-3 sm:px-6 pb-3 sm:pb-4">
                        <Button className="bg-amber-600 hover:bg-amber-700 text-white text-xs sm:text-sm h-8 sm:h-10">
                          Save Changes
                        </Button>
                      </CardFooter>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};
