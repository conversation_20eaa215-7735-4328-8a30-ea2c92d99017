'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  MessageSquare,
  Bug, 
  Lightbulb, 
  Gamepad2, 
  Palette, 
  Zap, 
  HelpCircle,
  Clock, 
  CheckCircle, 
  AlertCircle,
  Star,
  Calendar,
  User,
  Filter,
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  Eye,
  ArrowUpDown,
  Search,
  Download,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  getAllFeedback,
  updateFeedbackStatus,
  bulkUpdateFeedback,
  deleteFeedback,
  getFeedbackDetails,
  AdminFeedback
} from '@/utils/admin-feedback-utils';
import { toast } from 'sonner';

export default function AdminFeedbackManagement() {
  const [feedback, setFeedback] = useState<AdminFeedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFeedback, setSelectedFeedback] = useState<AdminFeedback | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedPriority, setSelectedPriority] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Filtering and search
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  useEffect(() => {
    loadFeedback();
  }, [filterStatus, filterCategory, filterPriority]);

  const loadFeedback = async () => {
    setLoading(true);
    try {
      const data = await getAllFeedback(filterStatus, filterCategory, filterPriority);
      
      setFeedback(data);
    } catch (error) {
      console.error('Error loading feedback:', error);
      toast.error('Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateFeedback = async () => {
    if (!selectedFeedback) return;

    setIsProcessing(true);
    try {
      const result = await updateFeedbackStatus(
        selectedFeedback.id,
        selectedStatus as any,
        adminNotes,
        selectedPriority as any
      );

      if (result.success) {
        toast.success('Feedback updated successfully');
        setShowEditDialog(false);
        resetEditForm();
        loadFeedback();
      } else {
        toast.error(result.error || 'Failed to update feedback');
      }
    } catch (error) {
      console.error('Error updating feedback:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkUpdate = async (status?: string, priority?: string) => {
    if (selectedItems.length === 0) {
      toast.error('Please select items to update');
      return;
    }

    setIsProcessing(true);
    try {
      const updates: any = {};
      if (status) updates.status = status;
      if (priority) updates.priority = priority;

      const result = await bulkUpdateFeedback(selectedItems, updates);

      if (result.success) {
        toast.success(`Updated ${result.updated} feedback items`);
        setSelectedItems([]);
        loadFeedback();
      } else {
        toast.error(result.error || 'Failed to update feedback');
      }
    } catch (error) {
      console.error('Error bulk updating feedback:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteFeedback = async (feedbackId: string) => {
    if (!confirm('Are you sure you want to delete this feedback? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await deleteFeedback(feedbackId);

      if (result.success) {
        toast.success('Feedback deleted successfully');
        loadFeedback();
      } else {
        toast.error(result.error || 'Failed to delete feedback');
      }
    } catch (error) {
      console.error('Error deleting feedback:', error);
      toast.error('An unexpected error occurred');
    }
  };

  const openEditDialog = (item: AdminFeedback) => {
    setSelectedFeedback(item);
    setSelectedStatus(item.status);
    setSelectedPriority(item.priority);
    setAdminNotes(item.admin_notes || '');
    setShowEditDialog(true);
  };

  const openDetailsDialog = async (item: AdminFeedback) => {
    setSelectedFeedback(item);
    setShowDetailsDialog(true);
  };

  const resetEditForm = () => {
    setSelectedFeedback(null);
    setSelectedStatus('');
    setSelectedPriority('');
    setAdminNotes('');
  };

  const filteredFeedback = feedback.filter(item => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        item.title.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.user_display_name?.toLowerCase().includes(query) ||
        item.category.toLowerCase().includes(query)
      );
    }
    return true;
  });

  const toggleSelectAll = () => {
    if (selectedItems.length === filteredFeedback.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredFeedback.map(item => item.id));
    }
  };

  const toggleSelectItem = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      bug_report: Bug,
      feature_request: Lightbulb,
      gameplay: Gamepad2,
      ui_ux: Palette,
      performance: Zap,
      other: HelpCircle
    };
    return icons[category as keyof typeof icons] || HelpCircle;
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      bug_report: 'Bug Report',
      feature_request: 'Feature Request',
      gameplay: 'Gameplay',
      ui_ux: 'UI/UX',
      performance: 'Performance',
      other: 'Other'
    };
    return labels[category as keyof typeof labels] || category;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-orange-100 text-orange-800 border-orange-200',
      in_review: 'bg-blue-100 text-blue-800 border-blue-200',
      resolved: 'bg-green-100 text-green-800 border-green-200',
      closed: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };
    return colors[priority as keyof typeof colors] || colors.low;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p>Loading feedback...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-2">
          <MessageSquare className="h-8 w-8 text-amber-600" />
          Feedback Management
        </h1>
        <p className="text-gray-600">
          Review and manage user feedback to improve Word Nook experience.
        </p>
      </div>

      {/* Filters and Search */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search feedback..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_review">In Review</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="bug_report">Bug Report</SelectItem>
                  <SelectItem value="feature_request">Feature Request</SelectItem>
                  <SelectItem value="gameplay">Gameplay</SelectItem>
                  <SelectItem value="ui_ux">UI/UX</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Priority</Label>
              <Select value={filterPriority} onValueChange={setFilterPriority}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Actions</Label>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={loadFeedback}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedItems.length > 0 && (
            <div className="flex items-center gap-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
              <span className="text-sm font-medium text-amber-800">
                {selectedItems.length} item(s) selected
              </span>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => handleBulkUpdate('in_review')}>
                  Mark In Review
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkUpdate('resolved')}>
                  Mark Resolved
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkUpdate(undefined, 'high')}>
                  Set High Priority
                </Button>
                <Button size="sm" variant="outline" onClick={() => setSelectedItems([])}>
                  Clear Selection
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Feedback List */}
      <div className="space-y-4">
        {/* Header with select all */}
        <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
          <Checkbox
            checked={selectedItems.length === filteredFeedback.length && filteredFeedback.length > 0}
            onCheckedChange={toggleSelectAll}
          />
          <span className="text-sm font-medium text-gray-700">
            Select All ({filteredFeedback.length} items)
          </span>
        </div>

        {filteredFeedback.length === 0 ? (
          <Card>
            <CardContent className="py-12 text-center">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Feedback Found</h3>
              <p className="text-gray-600">
                {searchQuery || filterStatus !== 'all' || filterCategory !== 'all' || filterPriority !== 'all'
                  ? 'No feedback matches your current filters.'
                  : 'No feedback has been submitted yet.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredFeedback.map((item) => {
            const CategoryIcon = getCategoryIcon(item.category);
            
            return (
              <Card key={item.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={() => toggleSelectItem(item.id)}
                    />
                    
                    <div className="flex-1 space-y-4">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="p-2 rounded-lg bg-amber-100">
                            <CategoryIcon className="h-5 w-5 text-amber-700" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">
                              {item.title}
                            </h3>
                            <div className="flex items-center gap-2 flex-wrap mb-2">
                              <Badge variant="outline" className="text-xs">
                                {getCategoryLabel(item.category)}
                              </Badge>
                              <Badge className={cn("text-xs border", getStatusColor(item.status))}>
                                {item.status.replace('_', ' ')}
                              </Badge>
                              <Badge className={cn("text-xs", getPriorityColor(item.priority))}>
                                {item.priority} priority
                              </Badge>
                              {item.rating && (
                                <div className="flex items-center gap-1">
                                  <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                                  <span className="text-xs text-gray-600">{item.rating}/5</span>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{item.user_display_name}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(item.created_at)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDetailsDialog(item)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteFeedback(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Description */}
                      <div>
                        <p className="text-gray-700 text-sm leading-relaxed line-clamp-2">
                          {item.description}
                        </p>
                      </div>

                      {/* Admin Notes */}
                      {item.admin_notes && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <h4 className="font-medium text-blue-900 mb-1 text-sm">Admin Response:</h4>
                          <p className="text-blue-800 text-sm">{item.admin_notes}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Update Feedback</DialogTitle>
          </DialogHeader>
          
          {selectedFeedback && (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">{selectedFeedback.title}</h3>
                <p className="text-gray-600 text-sm">{selectedFeedback.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="in_review">In Review</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Priority</Label>
                  <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Admin Notes</Label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add notes for the user..."
                  rows={4}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateFeedback} disabled={isProcessing}>
                  {isProcessing ? 'Updating...' : 'Update Feedback'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Feedback Details</DialogTitle>
          </DialogHeader>
          
          {selectedFeedback && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Basic Information</h3>
                    <div className="space-y-2 text-sm">
                      <div><strong>Title:</strong> {selectedFeedback.title}</div>
                      <div><strong>Category:</strong> {getCategoryLabel(selectedFeedback.category)}</div>
                      <div><strong>Status:</strong> {selectedFeedback.status.replace('_', ' ')}</div>
                      <div><strong>Priority:</strong> {selectedFeedback.priority}</div>
                      <div><strong>User:</strong> {selectedFeedback.user_display_name}</div>
                      {selectedFeedback.rating && (
                        <div><strong>Rating:</strong> {selectedFeedback.rating}/5 stars</div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Timestamps</h3>
                    <div className="space-y-1 text-sm">
                      <div><strong>Created:</strong> {formatDate(selectedFeedback.created_at)}</div>
                      <div><strong>Updated:</strong> {formatDate(selectedFeedback.updated_at)}</div>
                      {selectedFeedback.resolved_at && (
                        <div><strong>Resolved:</strong> {formatDate(selectedFeedback.resolved_at)}</div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {selectedFeedback.browser_info && (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Browser Info</h3>
                      <div className="bg-gray-50 rounded-lg p-3 text-xs">
                        <div><strong>User Agent:</strong> {selectedFeedback.browser_info.userAgent}</div>
                        <div><strong>Language:</strong> {selectedFeedback.browser_info.language}</div>
                        <div><strong>Platform:</strong> {selectedFeedback.browser_info.platform}</div>
                      </div>
                    </div>
                  )}

                  {selectedFeedback.device_info && (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Device Info</h3>
                      <div className="bg-gray-50 rounded-lg p-3 text-xs">
                        <div><strong>Screen:</strong> {selectedFeedback.device_info.screenWidth}x{selectedFeedback.device_info.screenHeight}</div>
                        <div><strong>Window:</strong> {selectedFeedback.device_info.windowWidth}x{selectedFeedback.device_info.windowHeight}</div>
                        <div><strong>Pixel Ratio:</strong> {selectedFeedback.device_info.pixelRatio}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700 whitespace-pre-wrap">{selectedFeedback.description}</p>
                </div>
              </div>

              {selectedFeedback.admin_notes && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Admin Notes</h3>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-blue-800 whitespace-pre-wrap">{selectedFeedback.admin_notes}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
