// Utility functions to seed default data for the application
import { seedDefaultItems } from './customization-utils';
import { seedDefaultBattlePass } from './battle-pass-utils';

export interface SeedResult {
  success: boolean;
  message: string;
  details?: any;
}

// Seed all default data
export async function seedAllDefaultData(): Promise<{
  items: SeedResult;
  battlePass: SeedResult;
  overall: SeedResult;
}> {
  console.log('Starting to seed default data...');
  
  // Seed default items
  const itemsResult = await seedDefaultItems();
  console.log('Items seeding result:', itemsResult);
  
  // Seed default battle pass
  const battlePassResult = await seedDefaultBattlePass();
  console.log('Battle pass seeding result:', battlePassResult);
  
  const overallSuccess = itemsResult.success && battlePassResult.success;
  
  return {
    items: itemsResult,
    battlePass: battlePassResult,
    overall: {
      success: overallSuccess,
      message: overallSuccess 
        ? 'All default data seeded successfully'
        : 'Some seeding operations failed'
    }
  };
}

// Seed only items
export async function seedItems(): Promise<SeedResult> {
  return await seedDefaultItems();
}

// Seed only battle pass
export async function seedBattlePass(): Promise<SeedResult> {
  return await seedDefaultBattlePass();
}

// Check if seeding is needed
export async function checkSeedingStatus(): Promise<{
  needsItems: boolean;
  needsBattlePass: boolean;
  needsSeeding: boolean;
}> {
  // This would check if default data exists
  // For now, we'll assume seeding is always safe to run
  return {
    needsItems: true,
    needsBattlePass: true,
    needsSeeding: true
  };
}
