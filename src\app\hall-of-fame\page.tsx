'use client';

import DashboardNavbar from "@/components/dashboard-navbar";
import {
  BookOpen,
  Crown,
  TrendingUp,
  Users,
  Medal,
  Star,
  Award
} from "lucide-react";
import { createClient } from "../../../supabase/client";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getRankingLeaderboard } from "@/utils/ranking-system";
import RankDisplay, { LeaderboardRank } from "@/components/rank-display";
import StyledAvatar from "@/components/styled-avatar";
import PlayerStatsPopup from "@/components/player-stats-popup";
import { getAvatarBorderImageUrl, getAvatarImageUrl } from "@/utils/avatar-utils";
import { useState, useEffect } from "react";
import { Skeleton } from "@/components/ui/skeleton";

export default function LeaderboardPage() {
  const [globalRankingLeaderboard, setGlobalRankingLeaderboard] = useState<any[]>([]);
  const [playersByTier, setPlayersByTier] = useState<{
    Luminari: any[];
    Sage: any[];
    Lexicon: any[];
    Quill: any[];
    Parchment: any[];
  }>({
    Luminari: [],
    Sage: [],
    Lexicon: [],
    Quill: [],
    Parchment: [],
  });
  const [totalRankedPlayers, setTotalRankedPlayers] = useState(0);
  const [loading, setLoading] = useState(true);

  const supabase = createClient();

  useEffect(() => {
    loadLeaderboardData();
  }, []);

  const loadLeaderboardData = async () => {
    try {
      setLoading(true);

      // Get ranking leaderboard data
      const leaderboardData = await getRankingLeaderboard(50);
      setGlobalRankingLeaderboard(leaderboardData);
      
      // Group players by rank tiers for display
      const tierGroups = {
        Luminari: leaderboardData.filter(p => p.rankInfo.tier === 'Luminari'),
        Sage: leaderboardData.filter(p => p.rankInfo.tier === 'Sage'),
        Lexicon: leaderboardData.filter(p => p.rankInfo.tier === 'Lexicon'),
        Quill: leaderboardData.filter(p => p.rankInfo.tier === 'Quill'),
        Parchment: leaderboardData.filter(p => p.rankInfo.tier === 'Parchment'),
      };
      setPlayersByTier(tierGroups);

      // Get overall stats
      const { data: totalPlayersData } = await supabase
        .from('players')
        .select('id', { count: 'exact' })
        .gt('rankpoints', 0);

      setTotalRankedPlayers(totalPlayersData?.length || 0);

    } catch (error) {
      console.error('Error loading leaderboard data:', error);
    } finally {
      setLoading(false);
    }
  };
  if (loading) {
    return (
      <>
        <DashboardNavbar />
        <main className="w-full bg-gradient-to-br from-amber-50/60 via-orange-50/30 to-yellow-50/40 min-h-screen">
          <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-6 sm:gap-8">
            {/* Header Skeleton */}
            <header className="text-center space-y-4">
              <div className="flex items-center justify-center gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-10 w-80" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
              <Skeleton className="h-6 w-96 mx-auto" />
              <div className="flex items-center justify-center gap-6">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
              </div>
            </header>

            {/* Elite Scribes Section Skeleton */}
            <section className="bg-white/80 rounded-2xl p-6 border-2 border-amber-200">
              <div className="flex items-center gap-3 mb-6">
                <Skeleton className="h-6 w-6" />
                <Skeleton className="h-8 w-32" />
                <Skeleton className="h-6 w-24 rounded-full" />
              </div>
              <div className="space-y-3">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-4 p-4 bg-white/50 rounded-lg">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <div className="text-right space-y-1">
                      <Skeleton className="h-5 w-20" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Rank Tiers Skeleton */}
            <section className="space-y-8">
              <div className="text-center space-y-2">
                <Skeleton className="h-8 w-32 mx-auto" />
                <Skeleton className="h-4 w-64 mx-auto" />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="bg-white rounded-lg border-2 border-amber-200 overflow-hidden">
                    {/* Header */}
                    <div className="bg-amber-50 p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <Skeleton className="h-6 w-6" />
                          <Skeleton className="h-6 w-24" />
                        </div>
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center space-y-1">
                          <Skeleton className="h-8 w-16 mx-auto" />
                          <Skeleton className="h-3 w-12 mx-auto" />
                        </div>
                        <div className="text-center space-y-1">
                          <Skeleton className="h-8 w-16 mx-auto" />
                          <Skeleton className="h-3 w-12 mx-auto" />
                        </div>
                      </div>
                    </div>

                    {/* Player List */}
                    <div className="divide-y divide-gray-100">
                      {Array.from({ length: 3 }).map((_, playerIndex) => (
                        <div key={playerIndex} className="flex items-center justify-between p-4">
                          <div className="flex items-center gap-4">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <Skeleton className="h-10 w-10 rounded-full" />
                            <div className="space-y-2">
                              <Skeleton className="h-4 w-24" />
                              <Skeleton className="h-3 w-16" />
                            </div>
                          </div>
                          <div className="text-right space-y-1">
                            <Skeleton className="h-5 w-16" />
                            <Skeleton className="h-3 w-20" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          </div>
        </main>
      </>
    );
  }

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Crown className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                The Scribe's Ascent
              </h1>
              <Crown className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Climb the ladder and solidify your place as a true master of words. Rankings are based on Rank Points (RP) earned through competitive matches.
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>{totalRankedPlayers} Ranked Players</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                <span>Live Rankings</span>
              </div>
            </div>
          </header>

          {/* Top Ranked Players */}
          {globalRankingLeaderboard.length > 0 && (
            <section className="bg-gradient-to-r from-amber-100 via-white to-amber-100 rounded-2xl p-6 border-2 border-amber-200 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <Crown className="h-6 w-6 text-amber-600" />
                <h2 className="text-2xl font-bold text-amber-900">Elite Scribes</h2>
                <Badge variant="secondary" className="bg-amber-200 text-amber-800">
                  Top {Math.min(globalRankingLeaderboard.length, 10)} Players
                </Badge>
              </div>

              <div className="space-y-3">
                {globalRankingLeaderboard.slice(0, 10).map((player, index) => (
                  <LeaderboardRank
                    key={player.id}
                    rp={player.rankpoints}
                    position={index + 1}
                    playerName={player.display_name}
                    avatarFallback={player.avatar_url}
                    avatar={player.avatar_items}
                    playerId={player.id}
                    border={player.avatar_border}
                    playerXp={player.level_experience || 0}
                    rankPoints={player.rankpoints}

                    className="bg-white/50 hover:bg-white/80 transition-colors"
                  />
                ))}
              </div>
            </section>
          )}

          {/* Rank Tier Leaderboards */}
          <section className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Rank Tiers</h2>
              <p className="text-gray-600">Players organized by their current rank in The Scribe's Ascent</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {Object.entries(playersByTier).reverse().map(([tierName, players]) => {
                if (players.length === 0) return null;

                const tierInfo = {
                  Luminari: { icon: Crown, color: "text-yellow-500", bgColor: "bg-yellow-100", borderColor: "border-yellow-200", gradientFrom: "from-yellow-50", gradientTo: "to-yellow-100" },
                  Sage: { icon: Award, color: "text-purple-500", bgColor: "bg-purple-100", borderColor: "border-purple-200", gradientFrom: "from-purple-50", gradientTo: "to-purple-100" },
                  Lexicon: { icon: BookOpen, color: "text-blue-500", bgColor: "bg-blue-100", borderColor: "border-blue-200", gradientFrom: "from-blue-50", gradientTo: "to-blue-100" },
                  Quill: { icon: Star, color: "text-green-500", bgColor: "bg-green-100", borderColor: "border-green-200", gradientFrom: "from-green-50", gradientTo: "to-green-100" },
                  Parchment: { icon: Medal, color: "text-gray-500", bgColor: "bg-gray-100", borderColor: "border-gray-200", gradientFrom: "from-gray-50", gradientTo: "to-gray-100" }
                };

                const tier = tierInfo[tierName as keyof typeof tierInfo];
                if (!tier) return null;

                return (
                  <Card
                    key={`tier-${tierName}`}
                    className={cn(
                      "overflow-hidden border-2 shadow-lg transition-all duration-300 hover:shadow-xl",
                      tier.borderColor
                    )}
                  >
                    {/* Header */}
                    <CardHeader className={cn(
                      "bg-gradient-to-r p-6",
                      tier.gradientFrom,
                      tier.gradientTo
                    )}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <tier.icon className={cn("h-6 w-6", tier.color)} />
                          <CardTitle className="text-xl">{tierName}</CardTitle>
                        </div>
                        <Badge variant="secondary" className="bg-white/80">
                          {players.length} Players
                        </Badge>
                      </div>

                      {/* Tier Stats */}
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{players[0]?.rankpoints.toLocaleString() || 0}</p>
                          <p className="text-xs text-gray-600">Highest RP</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{players[players.length - 1]?.rankpoints.toLocaleString() || 0}</p>
                          <p className="text-xs text-gray-600">Entry RP</p>
                        </div>
                      </div>
                    </CardHeader>

                    {/* Tier Leaderboard */}
                    <CardContent className="p-0">
                      <div className="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                        {players.slice(0, 10).map((player, index) => (
                          <div
                            key={`${tierName}-${player.id}`}
                            className="flex items-center justify-between p-4 hover:bg-gray-50/50 transition-colors"
                          >
                            <div className="flex items-center gap-4">
                              <div className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                                index === 0 ? "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white" :
                                index === 1 ? "bg-gradient-to-r from-gray-300 to-gray-400 text-white" :
                                index === 2 ? "bg-gradient-to-r from-amber-600 to-amber-700 text-white" :
                                "bg-gray-100 text-gray-700"
                              )}>
                                {index + 1}
                              </div>                              
                              <PlayerStatsPopup
                                playerId={player.id}
                                playerName={player.display_name}
                                playerAvatar={getAvatarImageUrl(player.avatar_items) || player.avatar_url}
                                playerBorder={getAvatarBorderImageUrl(player.avatar_border)}
                              >
                                <div className="flex items-center gap-4 flex-1 min-w-0 cursor-pointer hover:bg-gray-50 rounded p-1 transition-colors">
                                  <div className="relative">
                                    <StyledAvatar
                                      src={getAvatarImageUrl(player.avatar_items) || player.avatar_url}
                                      alt={player.display_name}
                                      fallback={player.display_name.substring(0, 2).toUpperCase()}
                                      size="sm"
                                      border={getAvatarBorderImageUrl(player.avatar_border)}
                                      totalXP={player.level_experience || 0}
                                      showLevel={false}
                                    />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                                    <RankDisplay rp={player.rankpoints} variant="inline" showRP={false} />
                                  </div>
                                </div>
                              </PlayerStatsPopup>
                            </div>

                            <div className="text-right">
                              <p className="font-bold text-lg text-amber-700">
                                {player.rankpoints.toLocaleString()} RP
                              </p>
                              <p className="text-sm text-gray-600">
                                Peak: {player.highest_rankpoints.toLocaleString()} RP
                              </p>
                            </div>
                          </div>
                        ))}
                        {players.length > 10 && (
                          <div className="p-4 text-center text-gray-500">
                            <p className="text-sm">... and {players.length - 10} more players</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
