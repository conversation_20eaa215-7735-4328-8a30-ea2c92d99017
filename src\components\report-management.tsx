'use client'

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  AlertTriangle, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Clock,
  User,
  Flag,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  getPendingReports, 
  updateReportStatus, 
  applyModerationAction,
  PlayerReport 
} from '@/utils/report-utils';
import { toast } from 'sonner';

export default function ReportManagementPage() {
  const [reports, setReports] = useState<PlayerReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<PlayerReport | null>(null);
  const [showActionDialog, setShowActionDialog] = useState(false);
  const [moderatorNotes, setModeratorNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    setLoading(true);
    try {
      const data = await getPendingReports();
      setReports(data);
    } catch (error) {
      console.error('Error loading reports:', error);
      toast.error('Failed to load reports');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateReport = async (
    reportId: string, 
    status: PlayerReport['status']
  ) => {
    setIsProcessing(true);
    try {
      const result = await updateReportStatus(reportId, status, moderatorNotes);
      
      if (result.success) {
        toast.success(`Report ${status} successfully`);
        setShowActionDialog(false);
        setModeratorNotes('');
        loadReports(); // Refresh the list
      } else {
        toast.error(result.error || 'Failed to update report');
      }
    } catch (error) {
      console.error('Error updating report:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleModerationAction = async (
    playerId: string,
    actionType: 'warning' | 'mute' | 'temporary_ban' | 'permanent_ban',
    reportId: string
  ) => {
    setIsProcessing(true);
    try {
      let durationHours: number | undefined;
      
      // Set duration based on action type
      switch (actionType) {
        case 'mute':
          durationHours = 24; // 1 day mute
          break;
        case 'temporary_ban':
          durationHours = 168; // 7 day ban
          break;
        case 'permanent_ban':
          durationHours = undefined; // Permanent
          break;
        default:
          durationHours = undefined;
      }

      const result = await applyModerationAction(
        playerId,
        actionType,
        moderatorNotes || 'Violation of community guidelines',
        durationHours,
        reportId,
        moderatorNotes
      );

      if (result.success) {
        // Also update the report status to resolved
        await updateReportStatus(reportId, 'resolved', moderatorNotes);
        
        toast.success(`${actionType.replace('_', ' ')} applied successfully`);
        setShowActionDialog(false);
        setModeratorNotes('');
        loadReports();
      } else {
        toast.error(result.error || 'Failed to apply moderation action');
      }
    } catch (error) {
      console.error('Error applying moderation action:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'under_review':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'resolved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'cheating':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'harassment':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'inappropriate_name':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'spam':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p>Loading reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-2">
          <Shield className="h-8 w-8 text-amber-600" />
          Report Management
        </h1>
        <p className="text-gray-600">
          Review and manage player reports to maintain community standards.
        </p>
      </div>

      {reports.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <Flag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Reports</h3>
            <p className="text-gray-600">There are currently no reports requiring attention.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {reports.map((report) => (
            <Card key={report.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center gap-3">
                      <Badge className={cn("text-xs border", getCategoryColor(report.category))}>
                        {report.category.replace('_', ' ')}
                      </Badge>
                      <Badge className={cn("text-xs border", getStatusColor(report.status))}>
                        {report.status.replace('_', ' ')}
                      </Badge>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Clock className="h-3 w-3" />
                        {new Date(report.created_at).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-600" />
                        <span className="text-sm text-gray-600">Reported Player:</span>
                        <span className="font-medium">{report.reported_player_id}</span>
                      </div>
                      
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-gray-900">Reason:</p>
                        <p className="text-sm text-gray-700">{report.reason}</p>
                      </div>

                      {report.additional_details && (
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-gray-900">Additional Details:</p>
                          <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded border">
                            {report.additional_details}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedReport(report);
                        setShowActionDialog(true);
                      }}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Review
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Action Dialog */}
      <Dialog open={showActionDialog} onOpenChange={setShowActionDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
              Review Report
            </DialogTitle>
          </DialogHeader>

          {selectedReport && (
            <div className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Report Details:</p>
                <div className="bg-gray-50 p-3 rounded border space-y-1">
                  <p className="text-sm"><strong>Category:</strong> {selectedReport.category.replace('_', ' ')}</p>
                  <p className="text-sm"><strong>Reason:</strong> {selectedReport.reason}</p>
                  {selectedReport.additional_details && (
                    <p className="text-sm"><strong>Details:</strong> {selectedReport.additional_details}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Moderator Notes:</label>
                <Textarea
                  value={moderatorNotes}
                  onChange={(e) => setModeratorNotes(e.target.value)}
                  placeholder="Add notes about your decision..."
                  rows={3}
                />
              </div>

              <div className="space-y-3">
                <p className="text-sm font-medium">Actions:</p>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleUpdateReport(selectedReport.id, 'dismissed')}
                    disabled={isProcessing}
                    className="text-gray-700"
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Dismiss
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleModerationAction(selectedReport.reported_player_id, 'warning', selectedReport.id)}
                    disabled={isProcessing}
                    className="text-yellow-700"
                  >
                    Warning
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleModerationAction(selectedReport.reported_player_id, 'mute', selectedReport.id)}
                    disabled={isProcessing}
                    className="text-orange-700"
                  >
                    Mute (1d)
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleModerationAction(selectedReport.reported_player_id, 'temporary_ban', selectedReport.id)}
                    disabled={isProcessing}
                    className="text-red-700"
                  >
                    Ban (7d)
                  </Button>
                </div>

                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleModerationAction(selectedReport.reported_player_id, 'permanent_ban', selectedReport.id)}
                  disabled={isProcessing}
                  className="w-full"
                >
                  Permanent Ban
                </Button>
              </div>

              <div className="flex justify-end gap-2 pt-2 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowActionDialog(false);
                    setModeratorNotes('');
                  }}
                  disabled={isProcessing}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
