'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RewardPreviewModal } from '@/components/reward-preview-modal';
import { Reward } from '@/lib/item-utils';
import { Gift, Lock, Star, Zap } from 'lucide-react';

// Test rewards with different unlock requirement formats
const testRewards: Reward[] = [
  {
    type: 'item',
    id: 'test-item-1',
    item_id: 'wizard_avatar',
    item: {
      id: 'wizard_avatar',
      name: 'Wizard Avatar',
      type: 'avatar',
      rarity: 'epic',
      value: 'wizard_01',
      unlock_requirement: JSON.stringify({ type: 'level', value: 10 }), // String format to test object conversion
      is_default: false,
      description: 'A mystical wizard avatar for accomplished spellcasters.',
      created_at: '2025-01-01T00:00:00Z',
      image_url: '/items/avatars/wizard.png',
      is_animated: false
    }
  },
  {
    type: 'item',
    id: 'test-item-2',
    item_id: 'golden_border',
    item: {
      id: 'golden_border',
      name: 'Golden Border',
      type: 'avatar_border',
      rarity: 'legendary',
      value: 'gold_border',
      unlock_requirement: JSON.stringify({ type: 'rp', value: 2000 }), // String format (was working)
      is_default: false,
      description: 'A prestigious golden border for elite players.',
      created_at: '2025-01-01T00:00:00Z',
      image_url: '/items/borders/gold.png',
      is_animated: true
    }
  },
  {
    type: 'item',
    id: 'test-item-3',
    item_id: 'sage_title',
    item: {
      id: 'sage_title',
      name: 'Spelling Sage',
      type: 'title',
      rarity: 'rare',
      value: 'Spelling Sage',
      unlock_requirement: JSON.stringify({ type: 'battlepass', value: 15 }), // String format to test object conversion
      is_default: false,
      description: 'A title for those who have mastered the art of spelling.',
      created_at: '2025-01-01T00:00:00Z',
      image_url: '',
      is_animated: false
    }
  }
];

export function RewardPreviewTest() {
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openPreview = (reward: Reward) => {
    setSelectedReward(reward);
    setIsModalOpen(true);
  };

  const closePreview = () => {
    setSelectedReward(null);
    setIsModalOpen(false);
  };
  const getRewardIcon = (reward: Reward | null) => {
    if (!reward) return <Gift className="h-5 w-5" />;
    if (reward.type === 'item') {
      return <Gift className="h-5 w-5" />;
    } else if (reward.type === 'xp') {
      return <Zap className="h-5 w-5" />;
    } else {
      return <Star className="h-5 w-5" />;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-amber-600 bg-amber-100 border-amber-200';
      case 'epic': return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'rare': return 'text-blue-600 bg-blue-100 border-blue-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Reward Preview Test</h2>
        <p className="text-gray-600">Testing the fix for locked reward previews</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">        {testRewards.map((reward, index) => {
          if (!reward || reward.type !== 'item' || !reward.item) return null;
          
          const item = reward.item;
          const requirementFormat = typeof item.unlock_requirement === 'string' ? 'JSON String' : 'Object';
          
          return (
            <Card key={index} className="relative cursor-pointer hover:shadow-lg transition-all">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    {getRewardIcon(reward)}
                    {item.name}
                  </CardTitle>
                  <Badge variant="outline" className={getRarityColor(item.rarity)}>
                    {item.rarity}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="flex items-center justify-center h-20 bg-gray-100 rounded-lg">
                  {item.image_url ? (
                    <img 
                      src={item.image_url} 
                      alt={item.name}
                      className="h-16 w-16 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div className="h-16 w-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center text-2xl text-purple-600">
                    {getRewardIcon(reward)}
                  </div>
                </div>

                <div className="text-center space-y-2">
                  <p className="text-xs text-gray-500">
                    Format: {requirementFormat}
                  </p>
                  <div className="flex items-center justify-center gap-1 text-amber-600">
                    <Lock className="h-4 w-4" />
                    <span className="text-xs">Locked</span>
                  </div>
                  
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full"
                    onClick={() => openPreview(reward)}
                  >
                    Preview Locked Item
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Test Results */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-700 flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Test Results
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-600">
          <p className="text-sm">
            ✅ If you can click on any of the locked items above without getting a "Objects are not valid as a React child" error, 
            then the fix is working correctly!
          </p>
          <p className="text-xs mt-2 text-blue-500">
            The fix handles both JSON string and object formats for unlock requirements.
          </p>
        </CardContent>
      </Card>

      {/* Preview Modal */}
      {selectedReward && (
        <RewardPreviewModal
          reward={selectedReward}
          isOpen={isModalOpen}
          onClose={closePreview}
          playerLevel={5} // Low level to ensure items are locked
          playerRP={100}   // Low RP to ensure items are locked
          hasBattlePass={false}
          battlePassTier={0}
          isUnlocked={false}
        />
      )}
    </div>
  );
}
