"use client";

import React, { useEffect, useState, createContext, useContext } from "react";
import { Detector } from "react-detect-offline";
import { <PERSON>, <PERSON>H<PERSON>er, CardContent, CardTitle, CardDescription } from "@/components/ui/card";
import { WifiOff, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

// Context for sharing connection status across the app
interface ConnectionContextType {
  isOnline: boolean;
  isReconnecting: boolean;
  lastDisconnectTime: number | null;
  connectionLostDuration: number;
}

const ConnectionContext = createContext<ConnectionContextType>({
  isOnline: true,
  isReconnecting: false,
  lastDisconnectTime: null,
  connectionLostDuration: 0,
});

export const useConnectionStatus = () => useContext(ConnectionContext);

interface InternetStatusProviderProps {
  children: React.ReactNode;
}

export default function InternetStatusProvider({ children }: InternetStatusProviderProps) {
  const [isOffline, setIsOffline] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [lastDisconnectTime, setLastDisconnectTime] = useState<number | null>(null);
  const [connectionLostDuration, setConnectionLostDuration] = useState(0);
  const pathname = usePathname();

  // Check if we're on a battle page
  const isBattlePage = pathname?.includes('/battle/');

  // Handle offline state changes
  const handleOffline = () => {
    const disconnectTime = Date.now();
    setIsOffline(true);
    setShowModal(true);
    setLastDisconnectTime(disconnectTime);
    setIsReconnecting(true);

    // For battle pages, we want to preserve game state and pause timers
    if (isBattlePage) {
      console.log('Connection lost during battle - preserving game state and pausing timers');
      // Dispatch event to pause timers and prevent database updates
      window.dispatchEvent(new CustomEvent('connection-lost', {
        detail: { disconnectTime }
      }));
    }
  };

  const handleOnline = () => {
    const reconnectTime = Date.now();
    const duration = lastDisconnectTime ? reconnectTime - lastDisconnectTime : 0;

    setIsOffline(false);
    setIsReconnecting(false);
    setConnectionLostDuration(duration);

    // For battle pages, trigger reconnection logic
    if (isBattlePage && duration > 0) {
      console.log(`Reconnected after ${duration}ms - triggering battle sync`);
      // Dispatch a custom event that the battle page can listen to
      window.dispatchEvent(new CustomEvent('connection-restored', {
        detail: { duration, reconnectTime }
      }));
    }

    // Hide modal after a brief delay to show successful reconnection
    setTimeout(() => {
      setShowModal(false);
    }, 1000);
  };

  // Context value for child components
  const contextValue: ConnectionContextType = {
    isOnline: !isOffline,
    isReconnecting,
    lastDisconnectTime,
    connectionLostDuration,
  };

  return (
    <ConnectionContext.Provider value={contextValue}>
      <Detector
        render={({ online }) => {
          // Use polling method for more reliable detection
          if (!online && !isOffline) {
            handleOffline();
          } else if (online && isOffline) {
            handleOnline();
          }
          return null;
        }}
        polling={{
          enabled: true,
          url: "https://httpbin.org/get",
          interval: 5000,
          timeout: 5000,
        }}
      />

      {children}
      
      {/* Offline Modal */}
      {showModal && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center">
          {/* Backdrop with blur effect */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
          
          {/* Modal Content */}
          <div className="relative z-10 w-full max-w-md mx-4">
            <Card className="border-2 border-orange-200 shadow-2xl">
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/20">
                    <WifiOff className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {isReconnecting ? "Connection Lost" : "Reconnected!"}
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">
                  {isReconnecting ? "Attempting to reconnect..." : "Connection restored successfully"}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="text-center pb-6">
                {isReconnecting ? (
                  <>
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Checking connection</span>
                    </div>

                    <div className="mt-4 text-xs text-gray-400 dark:text-gray-500">
                      {isBattlePage
                        ? "Your game state is preserved. The battle will resume once connection is restored."
                        : "Please check your internet connection. The app will automatically reconnect when your connection is restored."
                      }
                    </div>
                  </>
                ) : (
                  <div className="text-sm text-green-600 dark:text-green-400">
                    ✓ Connection restored successfully
                    {isBattlePage && <div className="mt-2 text-xs text-gray-500">Syncing with battle...</div>}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </ConnectionContext.Provider>
  );
}
