import { createClient } from "../../supabase/client";
import { toast } from "sonner";
import { Difficulty } from "@/interfaces/interfaces";

// Cross-platform UUID generation function
function generateUUID(): string {
  // Try to use crypto.randomUUID() if available (modern browsers)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    try {
      return crypto.randomUUID();
    } catch (error) {
      console.warn('crypto.randomUUID() failed, falling back to alternative method');
    }
  }

  // Fallback for environments without crypto.randomUUID() (older mobile browsers)
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    // Use crypto.getRandomValues() for better randomness
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    
    // Convert to UUID format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return [
      hex.substring(0, 8),
      hex.substring(8, 12),
      '4' + hex.substring(13, 16), // Version 4 UUID
      ((parseInt(hex.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + hex.substring(17, 20), // Variant bits
      hex.substring(20, 32)
    ].join('-');
  }

  // Final fallback using Math.random() (less secure but compatible)
  console.warn('Using Math.random() fallback for UUID generation - not cryptographically secure');
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Constants
const MAX_PLAYERS_PER_ROOM = 8;

// Define type for match data
type MatchData = {
  id: string;
  difficulty: Difficulty | null;
  match_players: { player_id: string }[];
};

// Function to check if user is in any active match
async function checkUserInAnyActiveMatch(userId: string): Promise<MatchData | null> {
  const supabase = createClient();
  
  try {
    const { data, error } = await supabase
      .from('match_players')
      .select(`
        match_id,
        status,
        matches!inner(
          id,
          difficulty,
          status
        )
      `)
      .eq('player_id', userId)
      .eq('status', 'active')
      .in('matches.status', ['waiting', 'ongoing']);

    if (error) {
      console.error('Error checking user active match:', error);
      return null;
    }

    if (data && data.length > 0) {
      const matchesArray = data[0].matches as { id: string; difficulty: Difficulty | null; status: string }[];
      const match = Array.isArray(matchesArray) ? matchesArray[0] : matchesArray;
      if (match) {
        return {
          id: match.id,
          difficulty: match.difficulty,
          match_players: []
        };
      }
    }

    return null;
  } catch (error) {
    console.error('Exception checking if user is in active match:', error);
    return null;
  }
}

// Main function to join a room
export async function joinRoom(roomName: string): Promise<{ success: boolean; redirectPath?: string }> {
  const supabase = createClient();
  
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      toast.error("You must be logged in to join a room");
      return { success: false };
    }

    // Check if user is already in any active match
    const activeMatch = await checkUserInAnyActiveMatch(user.id);

    if (activeMatch) {
      const currentDifficulty = roomName.toLowerCase();
      const userMatchDifficulty = activeMatch.difficulty?.toLowerCase();

      if (userMatchDifficulty !== currentDifficulty) {
        // User is already in a different difficulty room
        toast.error(`You are already in the ${activeMatch.difficulty} room. Please leave that room first before joining the ${roomName} room.`);
        
        // Return the path to the user's current room
        const formattedDifficulty = userMatchDifficulty?.replace(/\s+/g, "-");
        return { 
          success: false, 
          redirectPath: `/battle/${formattedDifficulty}` 
        };
      }

      // User is already in this room, just navigate there
      const formattedDifficulty = userMatchDifficulty?.replace(/\s+/g, "-");
      return { 
        success: true, 
        redirectPath: `/battle/${formattedDifficulty}` 
      };
    }

    // First, check if the user already has a player record
    let playerId: string = user.id; // Use the user's ID as the player ID

    try {
      const { data: existingPlayer, error: fetchError } = await supabase
        .from('players')
        .select('id')
        .eq('id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
        console.error("Error checking for existing player:", fetchError);
        // Continue anyway, we'll try to create the player
      }

      if (!existingPlayer) {
        // Create a player record if it doesn't exist
        const { data: newPlayer, error: playerError } = await supabase
          .from('players')
          .insert({
            id: user.id,
            display_name: user.email || 'Anonymous Player',
            avatar_url: user.user_metadata?.avatar_url || null,
            stats: { games_played: 0, wins: 0 }
          })
          .select('id')
          .single();

        if (playerError) {
          console.error("Error creating player profile:", playerError);
          // If we can't create a player, we'll still try to join with the user ID
          toast.warning("Could not create player profile, but trying to join anyway");
        } else if (newPlayer) {
          playerId = newPlayer.id;
        }
      } else {
        playerId = existingPlayer.id;
      }
    } catch (error) {
      console.error("Error in player check/creation:", error);
      // Continue with the user ID as the player ID
    }

    // Check for matches with the specified difficulty
    const difficulty = roomName.toLowerCase();
    let matchId: string;

    try {      // First, check specifically for waiting matches with this difficulty
      const { data: waitingMatches, error: waitingMatchesError } = await supabase
        .from('matches')
        .select(`
          id,
          match_players!inner(player_id, status)
        `)
        .eq('status', 'waiting')
        .eq('difficulty', difficulty)
        .eq('match_players.status', 'active');

      if (waitingMatchesError) {
        console.error("Error fetching waiting matches:", waitingMatchesError);
        toast.error("Failed to check for waiting matches");
        return { success: false };
      }

      // If there are waiting matches, check if any have room for more players (max 8)
      let availableMatch = null;
      if (waitingMatches && waitingMatches.length > 0) {
        for (const match of waitingMatches) {
          const playerCount = match.match_players?.length || 0;
          if (playerCount < MAX_PLAYERS_PER_ROOM) {
            availableMatch = match;
            break;
          }
        }
      }

      if (availableMatch) {
        matchId = availableMatch.id;
      } else {        // No available waiting matches found (either none exist or all are full with 8 players), create a new one
        try {
          // Generate a UUID for the new match (cross-platform compatible)
          const matchUuid = generateUUID();

          // Create a new match
          const { data: newMatch, error: matchError } = await supabase
            .from('matches')
            .insert({
              id: matchUuid,
              difficulty: difficulty,
              status: 'waiting',
              current_round: 1,
              current_word_id: 0,
              current_state: 'waiting',
              created_at: new Date().toISOString(),
              waiting_time: new Date().toISOString(),
              start_time: null, // Will be set when there are at least 2 players
              is_locked: false // Initialize lock status
            })
            .select('id')
            .single();

          if (matchError) {
            console.error("Error creating new match:", matchError);
            toast.error("Failed to create a new match");
            return { success: false };
          }

          if (newMatch) {
            matchId = newMatch.id;
          } else {
            toast.error("Failed to create a new match");
            return { success: false };
          }
        } catch (createError) {
          console.error("Exception creating match:", createError);
          toast.error("Failed to create a new match");
          return { success: false };
        }
      }      // Check if player is already in this match
      try {
        const { data: existingMatchPlayer, error: playerCheckError } = await supabase
          .from('match_players')
          .select('match_id, status')
          .eq('match_id', matchId)
          .eq('player_id', playerId)
          .eq('status', 'active')
          .single();

        if (playerCheckError && playerCheckError.code !== 'PGRST116') {
          console.error("Error checking if player is in match:", playerCheckError);
        }

        if (!existingMatchPlayer) {
          // Before adding player, check if match is already full (8 players max)
          const { data: currentPlayers, error: countError } = await supabase
            .from('match_players')
            .select('player_id')
            .eq('match_id', matchId)
            .eq('status', 'active');

          if (countError) {
            console.error("Error checking current player count:", countError);
            toast.error("Failed to check room capacity");
            return { success: false };
          }

          const currentPlayerCount = currentPlayers?.length || 0;
          if (currentPlayerCount >= MAX_PLAYERS_PER_ROOM) {
            toast.error(`This room is full (${MAX_PLAYERS_PER_ROOM} players maximum). A new room will be created.`);            // Create a new match since this one is full
            try {
              const newMatchUuid = generateUUID();
              const { data: newMatch, error: newMatchError } = await supabase
                .from('matches')
                .insert({
                  id: newMatchUuid,
                  difficulty: difficulty,
                  status: 'waiting',
                  current_round: 1,
                  current_word_id: 0,
                  current_state: 'waiting',
                  created_at: new Date().toISOString(),
                  waiting_time: new Date().toISOString(),
                  start_time: null,
                  is_locked: false
                })
                .select('id')
                .single();

              if (newMatchError || !newMatch) {
                console.error("Error creating new match:", newMatchError);
                toast.error("Failed to create a new room");
                return { success: false };
              }

              matchId = newMatch.id;
            } catch (error) {
              console.error("Exception creating new match:", error);
              toast.error("Failed to create a new room");
              return { success: false };
            }
          }          // Add player to the match
          const { error: joinError } = await supabase
            .from('match_players')
            .insert({
              match_id: matchId,
              player_id: playerId,
              score: 0,
              lives: 3,
              is_spectator: false,
              status: 'active',
              created_at: new Date().toISOString()
            });

          if (joinError) {
            console.error("Error joining match:", joinError);
            toast.error("Failed to join the match");
            return { success: false };
          }
        }
      } catch (playerError) {
        console.error("Exception checking/adding player to match:", playerError);
        toast.error("Failed to join the match");
        return { success: false };
      }
    } catch (error) {
      console.error("Exception in match handling:", error);
      toast.error("Failed to process match data");
      return { success: false };
    }

    // Success - return the path to navigate to
    toast.success(`Joined ${roomName} room successfully!`);
    return { 
      success: true, 
      redirectPath: `/battle/${roomName.toLowerCase().replace(/\s+/g, "-")}` 
    };
  } catch (error) {
    console.error("Error joining room:", error);
    toast.error("An unexpected error occurred");
    return { success: false };
  }
}
