'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '../../../supabase/client';
import { useRouter } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Trophy,
  Star,
  Award,
  TrendingUp,
  Users,
  Clock,
  Medal,
  Sparkles,
  Zap,
  Crown,
  Check
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { getLevelLeaderboard, getXPMilestones, formatXP, getLevelInfo } from "@/utils/level-system";
import StyledAvatar from "@/components/styled-avatar";
import LevelDisplay from "@/components/level-display";
import PlayerStatsPopup from "@/components/player-stats-popup";
import { getAvatarImageUrl } from '@/utils/avatar-utils';
import { Skeleton } from "@/components/ui/skeleton";

export default function LevelsPage() {
  const [user, setUser] = useState<any>(null);
  const [playerData, setPlayerData] = useState<any>(null);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [currentXP, setCurrentXP] = useState(0);
  const [levelInfo, setLevelInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const supabase = createClient();
  const router = useRouter();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        router.push("/sign-in");
        return;
      }

      setUser(currentUser);

      // Get player data for current user
      const { data: userData } = await supabase
        .from('players')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      setPlayerData(userData);

      const userXP = userData?.level_experience || 0;
      setCurrentXP(userXP);
      setLevelInfo(getLevelInfo(userXP));

      // Get level leaderboard
      const leaderboardData = await getLevelLeaderboard(20);
      setLeaderboard(leaderboardData);

    } catch (error) {
      console.error('Error loading levels data:', error);
    } finally {
      setLoading(false);
    }
  };
  if (loading) {
    return (
      <>
        <DashboardNavbar />
        <main className="w-full bg-gradient-to-br from-amber-50/60 via-orange-50/30 to-yellow-50/40 min-h-screen">
          <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-6 sm:gap-8">
            {/* Header Skeleton */}
            <header className="text-center space-y-4">
              <div className="flex items-center justify-center gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-10 w-64" />
              </div>
              <Skeleton className="h-6 w-96 mx-auto" />
              <div className="flex items-center justify-center gap-6">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-4 w-24" />
              </div>
            </header>

            {/* Player Progress Card Skeleton */}
            <Card className="border-2 border-amber-200">
              <CardHeader className="bg-gradient-to-r from-amber-50 to-orange-50">
                <div className="flex flex-col md:flex-row items-center gap-6">
                  <Skeleton className="h-24 w-24 rounded-full" />
                  <div className="flex-1 space-y-4 text-center md:text-left">
                    <Skeleton className="h-8 w-48 mx-auto md:mx-0" />
                    <Skeleton className="h-6 w-32 mx-auto md:mx-0" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-64 mx-auto md:mx-0" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* XP Milestones Skeleton */}
            <Card className="border-2 border-amber-200">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton className="h-6 w-6" />
                  <Skeleton className="h-6 w-32" />
                </div>
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent className="space-y-4">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-4 p-4 bg-amber-50 rounded-lg">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Level Leaderboard Skeleton */}
            <Card className="border-2 border-amber-200">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton className="h-6 w-6" />
                  <Skeleton className="h-6 w-40" />
                </div>
                <Skeleton className="h-4 w-56" />
              </CardHeader>
              <CardContent className="space-y-3">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 bg-white rounded-lg border">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <div className="text-right space-y-1">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </main>
      </>
    );
  }

  if (!user || !playerData) {
    return null;
  }

  console.log(leaderboard);
  
  // Get XP milestones
  const milestones = getXPMilestones();

  // Helper function to get rank styling
  const getRankStyling = (index: number) => {
    if (index === 0) return "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white shadow-lg";
    if (index === 1) return "bg-gradient-to-r from-gray-300 to-gray-400 text-white shadow-md";
    if (index === 2) return "bg-gradient-to-r from-amber-600 to-amber-700 text-white shadow-md";
    return "bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800";
  };

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Star className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                Level System
              </h1>
              <Star className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Earn XP by playing matches and climb the ranks! Higher ranks unlock exclusive avatars, backgrounds, and borders.
            </p>
          </header>

          {/* Your Level Card */}
          <section>
            <Card className="border-2 border-amber-200 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-amber-100 to-amber-50 pb-6">
                <CardTitle className="text-xl text-amber-900 flex items-center gap-2">
                  <Star className="h-5 w-5 text-amber-600" />
                  Your Level
                </CardTitle>
                <CardDescription className="text-sm">
                  Your current level and progress
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <LevelDisplay 
                  totalXP={currentXP}
                  variant="card"
                  showProgress={true}
                  showTitle={true}
                />
              </CardContent>
            </Card>
          </section>

          {/* Level Leaderboard */}
          <section className="space-y-4">
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-amber-600" />
              <h2 className="text-2xl font-bold text-amber-900">Level Leaderboard</h2>
            </div>
            
            <Card className="border shadow-sm">
              <CardContent className="p-0">
                <div className="divide-y">
                  {leaderboard.map((player, index) => (
                    <div
                      key={player.id}
                      className={cn(
                        "flex items-center justify-between p-4 hover:bg-gray-50/50 transition-colors",
                        index === 0 ? "bg-amber-50/50" : ""
                      )}
                    >
                      <div className="flex items-center gap-4">
                        <div className={cn(
                          "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                          getRankStyling(index)
                        )}>
                          {index + 1}
                        </div>
                        <PlayerStatsPopup
                          playerId={player.id}
                          playerName={player.display_name}
                          playerAvatar={getAvatarImageUrl(player.avatar_items) || player?.avatar_url}
                          playerBorder={player?.avatar_border?.image_url}
                        >
                          <div className="flex items-center gap-4 cursor-pointer">
                            <div className='relative'>
                              <StyledAvatar
                                src={getAvatarImageUrl(player.avatar_items) || player?.avatar_url}
                                alt={player.display_name}
                                fallback={player.display_name.substring(0, 2).toUpperCase()}
                                size="sm"
                                border={player?.avatar_border?.image_url}
                                totalXP={player.level_experience || 0}
                                showLevel={false}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                            </div>
                          </div>
                        </PlayerStatsPopup>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-bold text-lg text-amber-700">
                          Level {player.levelInfo.currentLevel}
                        </p>
                        <p className="text-sm text-gray-600">
                          {formatXP(player.level_experience)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Level Milestones */}
          <section className="space-y-4">
            <div className="flex items-center gap-2">
              <Award className="h-6 w-6 text-amber-600" />
              <h2 className="text-2xl font-bold text-amber-900">Level Milestones</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {milestones.filter(m => m.rewards.length > 0).map((milestone) => (
                <Card 
                  key={milestone.level}
                  className={cn(
                    "border overflow-hidden transition-all duration-200 hover:shadow-md",
                    levelInfo.currentLevel >= milestone.level ? "bg-gradient-to-r from-green-50 to-white border-green-200" : 
                    "bg-white border-gray-200 opacity-80"
                  )}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Star className={cn(
                          "h-5 w-5",
                          levelInfo.currentLevel >= milestone.level ? "text-green-500" : "text-gray-400"
                        )} />
                        Level {milestone.level}
                      </CardTitle>
                      <Badge className={cn(
                        levelInfo.currentLevel >= milestone.level ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                      )}>
                        {levelInfo.currentLevel >= milestone.level ? "Unlocked" : formatXP(milestone.xp - currentXP) + " to go"}
                      </Badge>
                    </div>
                    <CardDescription>
                      {formatXP(milestone.xp)} total XP required
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Rewards:</p>
                      <div className="flex flex-wrap gap-2">
                        {milestone.rewards.map((reward, i) => (
                          <Badge 
                            key={i}
                            variant="outline" 
                            className={cn(
                              "text-xs",
                              levelInfo.currentLevel >= milestone.level ? "border-green-300 text-green-700" : "border-gray-300 text-gray-500"
                            )}
                          >
                            {levelInfo.currentLevel >= milestone.level && <Check className="h-3 w-3 mr-1" />}
                            {reward}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
