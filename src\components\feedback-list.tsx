'use client'

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Bug, 
  Lightbulb, 
  Gamepad2, 
  Palette, 
  Zap, 
  HelpCircle, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Star,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Feedback {
  id: string;
  category: string;
  title: string;
  description: string;
  rating?: number;
  status: string;
  priority: string;
  created_at: string;
  updated_at: string;
  admin_notes?: string;
  resolved_at?: string;
}

interface FeedbackListProps {
  feedback: Feedback[];
}

const categoryIcons = {
  bug_report: Bug,
  feature_request: Lightbulb,
  gameplay: Gamepad2,
  ui_ux: Palette,
  performance: Zap,
  other: HelpCircle
};

const categoryLabels = {
  bug_report: 'Bug Report',
  feature_request: 'Feature Request',
  gameplay: 'Gameplay',
  ui_ux: 'UI/UX',
  performance: 'Performance',
  other: 'Other'
};

const statusConfig = {
  pending: { label: 'Pending', icon: Clock, color: 'bg-orange-100 text-orange-800' },
  in_review: { label: 'In Review', icon: AlertCircle, color: 'bg-blue-100 text-blue-800' },
  resolved: { label: 'Resolved', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
  closed: { label: 'Closed', icon: CheckCircle, color: 'bg-gray-100 text-gray-800' }
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  critical: { label: 'Critical', color: 'bg-red-100 text-red-800' }
};

export default function FeedbackList({ feedback }: FeedbackListProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (feedback.length === 0) {
    return (
      <div className="text-center py-12">
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No feedback yet</h3>
        <p className="text-gray-600">
          Submit your first feedback using the form above to get started!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {feedback.map((item) => {
        const CategoryIcon = categoryIcons[item.category as keyof typeof categoryIcons];
        const statusInfo = statusConfig[item.status as keyof typeof statusConfig];
        const StatusIcon = statusInfo.icon;
        const priorityInfo = priorityConfig[item.priority as keyof typeof priorityConfig];

        return (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <div className="p-2 rounded-lg bg-amber-100">
                    <CategoryIcon className="h-5 w-5 text-amber-700" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                      {item.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 flex-wrap">
                      <Badge variant="outline" className="text-xs">
                        {categoryLabels[item.category as keyof typeof categoryLabels]}
                      </Badge>
                      <Badge className={cn("text-xs", statusInfo.color)}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {statusInfo.label}
                      </Badge>
                      <Badge className={cn("text-xs", priorityInfo.color)}>
                        {priorityInfo.label}
                      </Badge>
                      {item.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                          <span className="text-xs text-gray-600">{item.rating}/5</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Description */}
              <div>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {item.description}
                </p>
              </div>

              {/* Admin Notes */}
              {item.admin_notes && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h4 className="font-medium text-blue-900 mb-1 text-sm">Response from our team:</h4>
                  <p className="text-blue-800 text-sm">{item.admin_notes}</p>
                </div>
              )}

              {/* Timestamps */}
              <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Submitted {formatDate(item.created_at)}</span>
                  </div>
                  {item.resolved_at && (
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      <span>Resolved {formatDate(item.resolved_at)}</span>
                    </div>
                  )}
                </div>
                
                {item.updated_at !== item.created_at && (
                  <span>Updated {formatDate(item.updated_at)}</span>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
