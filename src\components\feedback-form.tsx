'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Send, Bug, Lightbulb, Gamepad2, Palette, Zap, HelpCircle } from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '../../supabase/client';

interface FeedbackFormProps {
  userId: string;
}

const categories = [
  { value: 'bug_report', label: 'Bug Report', icon: Bug, description: 'Something isn\'t working correctly' },
  { value: 'feature_request', label: 'Feature Request', icon: Lightbulb, description: 'Suggest a new feature or improvement' },
  { value: 'gameplay', label: 'Gameplay', icon: Gamepad2, description: 'Game mechanics, difficulty, or balance' },
  { value: 'ui_ux', label: 'UI/UX', icon: Palette, description: 'Design, layout, or user experience' },
  { value: 'performance', label: 'Performance', icon: Zap, description: 'Speed, loading times, or technical issues' },
  { value: 'other', label: 'Other', icon: HelpCircle, description: 'General feedback or suggestions' }
];

export default function FeedbackForm({ userId }: FeedbackFormProps) {
  const [formData, setFormData] = useState({
    category: '',
    title: '',
    description: '',
    rating: 0
  });
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.category || !formData.title || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    
    try {
      const supabase = createClient();
      
      // Collect browser and device info
      const browserInfo = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      };
      
      const deviceInfo = {
        screenWidth: screen.width,
        screenHeight: screen.height,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
        pixelRatio: window.devicePixelRatio,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      };

      const { error } = await supabase
        .from('feedback')
        .insert({
          user_id: userId,
          category: formData.category,
          title: formData.title,
          description: formData.description,
          rating: formData.rating || null,
          browser_info: browserInfo,
          device_info: deviceInfo
        });

      if (error) {
        console.error('Error submitting feedback:', error);
        toast.error('Failed to submit feedback. Please try again.');
        return;
      }

      toast.success('Thank you for your feedback! We\'ll review it soon.');
      
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('Exception submitting feedback:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleRatingClick = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Category Selection */}
      <div className="space-y-2">
        <Label htmlFor="category">Category *</Label>
        <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select feedback category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <SelectItem key={category.value} value={category.value}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <div>
                      <p className="font-medium">{category.label}</p>
                      <p className="text-xs text-gray-500">{category.description}</p>
                    </div>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">Title *</Label>
        <Input
          id="title"
          placeholder="Brief summary of your feedback"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          maxLength={100}
        />
        <p className="text-xs text-gray-500">{formData.title.length}/100 characters</p>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          placeholder="Provide detailed information about your feedback..."
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          rows={6}
          maxLength={1000}
        />
        <p className="text-xs text-gray-500">{formData.description.length}/1000 characters</p>
      </div>

      {/* Rating */}
      <div className="space-y-2">
        <Label>Overall Experience Rating (Optional)</Label>
        <div className="flex items-center gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleRatingClick(star)}
              className="p-1 hover:scale-110 transition-transform"
            >
              <Star
                className={`h-6 w-6 ${
                  star <= formData.rating
                    ? 'text-yellow-400 fill-yellow-400'
                    : 'text-gray-300'
                }`}
              />
            </button>
          ))}
          {formData.rating > 0 && (
            <span className="ml-2 text-sm text-gray-600">
              {formData.rating} star{formData.rating !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={submitting || !formData.category || !formData.title || !formData.description}
        className="w-full bg-amber-600 hover:bg-amber-700"
      >
        {submitting ? (
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            Submitting...
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Send className="h-4 w-4" />
            Submit Feedback
          </div>
        )}
      </Button>
    </form>
  );
}
